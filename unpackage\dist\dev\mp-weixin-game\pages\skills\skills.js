"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const utils_websocket = require("../../utils/websocket.js");
const _sfc_main = {
  data() {
    return {
      player: {},
      selectedMartials: {},
      // 每种类型选中的武功
      martialConfigs: [],
      // 武功配置数据
      martialTypes: [
        { key: "剑法", label: "剑法" },
        { key: "刀法", label: "刀法" },
        { key: "空手", label: "空手" },
        { key: "招架", label: "招架" },
        { key: "轻功", label: "轻功" },
        { key: "内功", label: "内功" },
        { key: "暗器", label: "暗器" }
      ],
      activeTab: "equip",
      // 新增，默认显示"使用武功"tab
      showDetail: false,
      showMovesModal: false,
      selectedSkill: null,
      isAuthed: false,
      showMartialSelect: false,
      martialSelectType: "",
      martialSelectTypeLabel: "",
      bonusSummary: {},
      // 新增：后端增益摘要
      trainResultMsg: "",
      // 新增：修炼结果提示
      showTrainResultModal: false,
      // 修炼日志弹窗显示
      trainLog: [],
      // 修炼日志
      trainLogScrollTop: 0,
      // 修炼日志滚动位置
      showTrainLoading: false,
      // 自定义修炼loading
      trainLogTimeout: null,
      // 修炼日志超时定时器
      trainFinished: false
      // 修炼是否已完成
    };
  },
  computed: {
    // "使用武功"tab：每类显示所有已解锁武功，直接用后端分组
    martialSkillsByType() {
      if (!this.player || !this.player.martial_skills)
        return {};
      const grouped = {};
      this.player.martial_skills.forEach((skill) => {
        const type = skill.type || skill.类型 || skill.类别 || "其他";
        if (!grouped[type])
          grouped[type] = [];
        grouped[type].push(skill);
        if (skill["是否可招架"] === "是") {
          if (!grouped["招架"])
            grouped["招架"] = [];
          grouped["招架"].push(skill);
        }
      });
      return grouped;
    },
    // 武功技能（不含生活技能，且读书/写字归为生活技能）
    learnedMartials() {
      return this.player && this.player.martial_skills ? this.player.martial_skills.filter((skill) => {
        const name = skill.name || skill.名称 || "";
        const isLife = this.isLifeSkill(skill) || name.includes("读书") || name.includes("写字");
        return (skill.unlocked || skill.解锁) && !isLife;
      }) : [];
    },
    // 生活技能（含读书/写字）
    lifeSkills() {
      return this.player && this.player.martial_skills ? this.player.martial_skills.filter((skill) => {
        const name = skill.name || skill.名称 || "";
        return (skill.unlocked || skill.解锁) && (this.isLifeSkill(skill) || name.includes("读书") || name.includes("写字"));
      }) : [];
    },
    learnedMartialCount() {
      return this.learnedMartials.length;
    }
  },
  onLoad() {
    this.isAuthed = utils_gameState.gameState.isAuthenticated ? utils_gameState.gameState.isAuthenticated() : utils_gameState.gameState.isAuthed;
    this.loadMartialSkills();
    this.loadMartialConfigs();
    this.fetchBonusSummary();
    if (!this._gameStateCallback) {
      this._gameStateCallback = (type, state) => {
        if (type === "player") {
          this.loadMartialSkills();
        }
      };
      utils_gameState.gameState.updateCallbacks.push(this._gameStateCallback);
    }
  },
  onUnload() {
    if (this._gameStateCallback) {
      const idx = utils_gameState.gameState.updateCallbacks.indexOf(this._gameStateCallback);
      if (idx >= 0)
        utils_gameState.gameState.updateCallbacks.splice(idx, 1);
      this._gameStateCallback = null;
    }
    if (this.showTrainLoading || this.showTrainResultModal) {
      return;
    }
  },
  onHide() {
    if (this.showTrainLoading || this.showTrainResultModal) {
      return;
    }
  },
  onShow() {
    if (!utils_websocket.wsManager.isConnected) {
      utils_websocket.wsManager.connect().then(() => {
        if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
          utils_websocket.wsManager.autoAuthenticate();
        }
        setTimeout(() => {
          if (utils_gameState.gameState.requestAllData)
            utils_gameState.gameState.requestAllData();
        }, 500);
      });
    } else if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
      utils_websocket.wsManager.autoAuthenticate();
      setTimeout(() => {
        if (utils_gameState.gameState.requestAllData)
          utils_gameState.gameState.requestAllData();
      }, 500);
    }
    this.isAuthed = utils_gameState.gameState.isAuthenticated ? utils_gameState.gameState.isAuthenticated() : utils_gameState.gameState.isAuthed;
    this.loadMartialSkills();
    this.loadMartialConfigs();
    this.fetchBonusSummary();
  },
  mounted() {
    if (typeof utils_websocket.wsManager !== "undefined" && utils_websocket.wsManager.on) {
      utils_websocket.wsManager.on("train_martial_log", (data) => {
        if (data && data.message) {
          this.appendTrainLog(data.message);
          this.resetTrainLogTimeout();
          if (data.message.includes("修炼总结") || data.message.includes("修炼结束")) {
            this.showTrainLoading = false;
            this.trainFinished = true;
            this.clearTrainLogTimeout();
          }
        }
      });
    }
    common_vendor.index.$on && common_vendor.index.$on("ws_reconnected", this.handleReconnectCloseAllPopups);
  },
  methods: {
    async loadMartialSkills() {
      this.player = Object.assign({}, utils_gameState.gameState.getPlayerData());
      if (this.player && this.player.martial_skills && !Array.isArray(this.player.martial_skills)) {
        this.player.martial_skills = Object.values(this.player.martial_skills);
      }
      this.syncSelectedMartials();
      this.updateData();
    },
    updateData() {
      if (!this.player)
        this.player = {};
      this.$forceUpdate();
    },
    convertMartialData(martialSkills) {
      const allSkills = [];
      Object.keys(martialSkills).forEach((category) => {
        const skills = martialSkills[category] || [];
        skills.forEach((skill) => {
          allSkills.push({
            ...skill,
            类别: this.getSkillCategory(skill.名称 || skill.name),
            门派: this.getSkillSchool(skill.名称 || skill.name),
            等级: this.getSkillLevel(skill.名称 || skill.name),
            描述: this.getSkillDescription(skill.名称 || skill.name),
            // 不再覆盖 moves/招式/招式列表
            效果: this.getSkillEffects(skill.名称 || skill.name)
          });
        });
      });
      return allSkills;
    },
    getMartialsByType(type) {
      if (!this.player || !this.player.martial_skills)
        return [];
      let arr = this.martialSkillsByType[type] || [];
      if (!Array.isArray(arr) && typeof arr === "object") {
        arr = Object.values(arr);
      }
      if (type === "招架") {
        arr = arr.filter((skill) => skill["是否可招架"] === "是" || skill.is_blockable === true);
      }
      return arr.filter((skill) => (skill.unlocked || skill.解锁) && !(skill.name || skill.名称 || "").startsWith("基本"));
    },
    // 获取选中武功的索引
    getSelectedMartialIndex(type) {
      const selectedName = this.selectedMartials[type];
      if (!selectedName)
        return 0;
      const martials = this.getMartialsByType(type);
      const index = martials.findIndex((skill) => skill.name === selectedName);
      return index >= 0 ? index : 0;
    },
    // 获取选中武功的名称
    getSelectedMartialName(type) {
      const selectedName = this.selectedMartials[type];
      if (!selectedName)
        return "未装备";
      const martial = this.getMartialsByType(type).find((skill) => skill.name === selectedName);
      return martial ? martial.name : "未装备";
    },
    // 获取选中的武功对象
    getSelectedMartial(type) {
      const selectedName = this.selectedMartials[type];
      if (!selectedName)
        return null;
      const result = this.getMartialsByType(type).find((skill) => skill.name === selectedName) || null;
      return result;
    },
    // 检查武功是否已装备
    isMartialEquipped(skill) {
      if (!skill)
        return false;
      const isEquipped = !!(skill.equipped || skill.装备);
      return isEquipped;
    },
    // 处理武功选择
    onMartialSelect(event, type) {
      const index = event.detail.value;
      const martials = this.getMartialsByType(type);
      if (martials[index]) {
        this.selectedMartials[type] = martials[index].name;
      }
    },
    async fetchPlayerDataFromServer() {
      const res = await utils_gameData.gameUtils.sendMessage({ type: "get_player_data", data: {} });
      if (res.type === "player_data") {
        utils_gameState.gameState.setPlayerData(res.data);
        this.updateData();
        setTimeout(() => {
          this.$forceUpdate();
        }, 50);
      }
    },
    async equipMartial(type, skill) {
      if (!this.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!skill.unlocked) {
        common_vendor.index.showToast({ title: "该武学尚未解锁", icon: "none" });
        return;
      }
      try {
        const isCurrentlyEquipped = this.isMartialEquipped(skill);
        const action = isCurrentlyEquipped ? "unequip_martial" : "use_martial";
        const skillName = skill.name || skill.名称;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: action,
          data: { skill_name: skillName }
        });
        if (response && response.type === action + "_success") {
          await this.fetchPlayerDataFromServer();
          this.syncSelectedMartials();
          this.updateData();
          this.$forceUpdate();
          await this.fetchBonusSummary();
          common_vendor.index.showToast({ title: response.data.message || "装备成功", icon: "success" });
        } else {
          console.warn("装备失败:", response && response.data && response.data.message);
          common_vendor.index.showToast({ title: response && response.data && response.data.message || "操作失败", icon: "none" });
        }
      } catch (error) {
        common_vendor.index.showToast({ title: "装备失败", icon: "none" });
      }
    },
    // 卸下指定类型的武功
    async unequipMartial(type) {
      if (!this.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      const skill = this.getSelectedMartial(type);
      if (!skill) {
        common_vendor.index.showToast({ title: "未选择武功", icon: "none" });
        return;
      }
      if (!this.isMartialEquipped(skill)) {
        common_vendor.index.showToast({ title: "该武功未装备", icon: "none" });
        return;
      }
      try {
        const skillName = skill.name || skill.名称;
        common_vendor.index.showLoading({
          title: "正在卸载...",
          mask: true
        });
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "unequip_martial",
          data: { skill_name: skillName }
        });
        common_vendor.index.hideLoading();
        if (response && response.type === "unequip_martial_success") {
          if (response.data.player) {
            if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
              response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map((name) => {
                return { name, ...response.data.player.martial_skills[name] };
              });
            }
            this.player = Object.assign({}, response.data.player);
            utils_gameState.gameState.player = response.data.player;
            await this.fetchPlayerDataFromServer();
            this.syncSelectedMartials();
            this.updateData();
            const updatedSkill = this.player.martial_skills.find((s) => s.name === skillName || s.名称 === skillName);
            if (updatedSkill) {
              updatedSkill.equipped = false;
              if (updatedSkill.装备 !== void 0) {
                updatedSkill.装备 = false;
              }
            }
            this.updateData();
            await this.fetchBonusSummary();
            setTimeout(() => {
              this.$forceUpdate();
            }, 50);
            common_vendor.index.showToast({
              title: response.data.message || `成功卸下${skillName}`,
              icon: "success"
            });
          }
        } else {
          common_vendor.index.showToast({
            title: response && response.data && response.data.message || "卸下失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("卸载武功失败:", error);
        common_vendor.index.showToast({ title: "操作异常", icon: "none" });
      }
    },
    getSkillCategory(skillName) {
      const categoryMap = {
        "基本拳法": "拳法",
        "太极拳": "拳法",
        "基本剑法": "剑法",
        "独孤九剑": "剑法",
        "凌波微步": "轻功",
        "神行百变": "轻功",
        "九阳神功": "内功",
        "九阴真经": "内功",
        "降龙十八掌": "掌法",
        "打狗棒法": "棍法",
        "采药": "生活技能",
        "伐木": "生活技能",
        "挖矿": "生活技能",
        "剥皮": "生活技能"
      };
      return categoryMap[skillName] || "其他";
    },
    getSkillSchool(skillName) {
      const schoolMap = {
        "独孤九剑": "华山",
        "太极拳": "武当",
        "九阳神功": "少林",
        "九阴真经": "古墓派",
        "降龙十八掌": "丐帮",
        "打狗棒法": "丐帮",
        "凌波微步": "逍遥派",
        "神行百变": "逍遥派"
      };
      return schoolMap[skillName] || "无门派";
    },
    getSkillLevel(skillName) {
      const levelMap = {
        "基本拳法": "基础",
        "基本剑法": "基础",
        "太极拳": "高级",
        "独孤九剑": "绝学",
        "凌波微步": "绝学",
        "神行百变": "绝学",
        "九阳神功": "绝学",
        "九阴真经": "绝学",
        "降龙十八掌": "绝学",
        "打狗棒法": "绝学"
      };
      return levelMap[skillName] || "基础";
    },
    getSkillDescription(skillName) {
      const descMap = {
        "基本拳法": "最基础的拳法，习武之初必学",
        "太极拳": "武当派绝学，以柔克刚",
        "独孤九剑": "华山派绝学，破尽天下武功",
        "凌波微步": "逍遥派绝学，身法如鬼魅",
        "九阳神功": "少林派绝学，内力深厚",
        "九阴真经": "古墓派绝学，内力深厚",
        "降龙十八掌": "丐帮绝学，掌力雄浑",
        "打狗棒法": "丐帮绝学，棒法精妙",
        "采药": "采集草药，用于炼药",
        "伐木": "砍伐树木，用于制作装备",
        "挖矿": "挖掘矿石，用于制作装备",
        "剥皮": "剥取动物皮毛，用于制作装备"
      };
      return descMap[skillName] || "武功描述待补充";
    },
    getSkillMoves(skillName) {
      const movesMap = {
        "基本拳法": ["基本拳法1", "基本拳法2", "基本拳法3", "基本拳法4", "基本拳法5"],
        "太极拳": ["白鹤亮翅", "野马分鬃", "搂膝拗步", "倒卷肱", "揽雀尾"],
        "独孤九剑": ["总诀式", "破剑式", "破刀式", "破枪式", "破鞭式", "破索式", "破掌式", "破箭式", "破气式"],
        "凌波微步": ["凌波微步1", "凌波微步2", "凌波微步3", "凌波微步4"],
        "九阳神功": ["九阳护体", "九阳真气", "九阳神功"],
        "九阴真经": ["九阴护体", "九阴真气", "九阴神功"],
        "降龙十八掌": ["亢龙有悔", "飞龙在天", "见龙在田", "鸿渐于陆", "潜龙勿用"],
        "打狗棒法": ["棒打双犬", "棒打狗头", "棒打狗腿", "棒打狗尾"],
        "采药": ["采药1", "采药2", "采药3"],
        "伐木": ["伐木1", "伐木2", "伐木3"],
        "挖矿": ["挖矿1", "挖矿2", "挖矿3"],
        "剥皮": ["剥皮1", "剥皮2", "剥皮3"]
      };
      return movesMap[skillName] || [];
    },
    getMoveDescription(skillName, moveName) {
      var _a;
      const descMap = {
        "基本拳法": {
          "基本拳法1": "对准敌人的胸口打出一拳！",
          "基本拳法2": "双拳齐出，敌人连连后退！",
          "基本拳法3": "对准敌人的腹部一拳！",
          "基本拳法4": "对准敌人的头部一拳！",
          "基本拳法5": "对准敌人的腿部一拳！"
        },
        "太极拳": {
          "白鹤亮翅": "一式「白鹤亮翅」，双手成白鹤亮翅之势，敌人连连后退！",
          "野马分鬃": "一式「野马分鬃」，双手成野马分鬃之势，敌人连连后退！",
          "搂膝拗步": "一式「搂膝拗步」，双手成搂膝拗步之势，敌人连连后退！",
          "倒卷肱": "一式「倒卷肱」，双手成倒卷肱之势，敌人连连后退！",
          "揽雀尾": "一式「揽雀尾」，双手成揽雀尾之势，敌人连连后退！"
        },
        "独孤九剑": {
          "总诀式": "一式「总诀式」，剑势如虹，敌人连连后退！",
          "破剑式": "一式「破剑式」，专门破解剑法，敌人连连后退！",
          "破刀式": "一式「破刀式」，专门破解刀法，敌人连连后退！",
          "破枪式": "一式「破枪式」，专门破解枪法，敌人连连后退！",
          "破鞭式": "一式「破鞭式」，专门破解鞭法，敌人连连后退！"
        }
      };
      return ((_a = descMap[skillName]) == null ? void 0 : _a[moveName]) || "招式描述待补充";
    },
    getSkillEffects(skill) {
      if (!skill || skill.等级 === "基础")
        return "暂无效果";
      const effects = [];
      const level = skill.等级;
      if (skill.类别 === "拳法") {
        effects.push(`攻击 +${Math.floor(level * 1.5)}`);
        effects.push(`力量 +${Math.floor(level / 10)}`);
      } else if (skill.类别 === "剑法") {
        effects.push(`攻击 +${Math.floor(level * 2)}`);
        effects.push(`悟性 +${Math.floor(level / 10)}`);
      } else if (skill.类别 === "轻功") {
        effects.push(`防御 +${Math.floor(level * 1.5)}`);
        effects.push(`闪避 +${Math.floor(level * 1.2)}`);
        effects.push(`身法 +${Math.floor(level / 10)}`);
      } else if (skill.类别 === "内功") {
        effects.push(`气血 +${Math.floor(level * 3)}`);
        effects.push(`内力 +${Math.floor(level * 2)}`);
        effects.push(`根骨 +${Math.floor(level / 10)}`);
      }
      return effects.length > 0 ? effects.join(", ") : "暂无效果";
    },
    // 获取武功特效
    getSkillSpecialEffects(skill) {
      if (!skill)
        return "";
      const skillName = skill.名称 || skill.name;
      const martialConfig = this.getMartialConfigByName(skillName);
      if (martialConfig && martialConfig["武功特效"] && martialConfig["武功特效"] !== "无") {
        return martialConfig["武功特效"];
      }
      return "";
    },
    // 获取招式列表
    getSkillMovesList(skill) {
      if (!skill)
        return [];
      const skillName = skill.名称 || skill.name;
      const currentLevel = skill.等级 || skill.level || 0;
      const martialConfig = this.getMartialConfigByName(skillName);
      if (martialConfig && martialConfig["招式列表"]) {
        return martialConfig["招式列表"].map((move) => ({
          name: move["名称"] || move.name,
          unlock_level: move["解锁等级"] || move.unlock_level || 0,
          attack: move["攻击"] || move.attack,
          defense: move["防御"] || move.defense,
          unlocked: currentLevel >= (move["解锁等级"] || move.unlock_level || 0)
        }));
      }
      return [];
    },
    // 根据武功名称获取配置信息
    getMartialConfigByName(skillName) {
      if (!skillName || !this.martialConfigs || !Array.isArray(this.martialConfigs)) {
        return null;
      }
      for (const config of this.martialConfigs) {
        if (config["武功名"] === skillName || config.name === skillName) {
          return config;
        }
      }
      return null;
    },
    // 加载武功配置数据
    async loadMartialConfigs() {
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_martial_configs",
          data: {}
        });
        if (response && response.type === "get_martial_configs_success" && response.data) {
          this.martialConfigs = response.data.configs || [];
        } else {
          this.martialConfigs = [];
        }
      } catch (error) {
        console.error("前端加载武功配置失败:", error);
        this.martialConfigs = [];
      }
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    onLevelChange(e) {
      this.levelIndex = e.detail.value;
    },
    onSchoolChange(e) {
      this.schoolIndex = e.detail.value;
    },
    getCategoryTitle() {
      if (this.activeTab === "all")
        return "全部武功";
      return this.activeTab;
    },
    getCategoryDescription() {
      const descriptions = {
        "all": "所有可学习的武功",
        "拳法": "拳法类武功，以拳为主",
        "剑法": "剑法类武功，以剑为主",
        "轻功": "轻功类武功，提升身法",
        "内功": "内功类武功，提升内力"
      };
      return descriptions[this.activeTab] || "武功描述";
    },
    getSkillProgress(skill) {
      if (skill.最大经验 === 0)
        return 0;
      return Math.min(skill.经验 / skill.最大经验 * 100, 100);
    },
    canUseMartial(skill) {
      return skill.等级 !== "基础" && skill.解锁 || skill.unlocked;
    },
    async toggleMartialUse(skill) {
      if (!this.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!skill.解锁 && !skill.unlocked) {
        common_vendor.index.showToast({ title: "该武学尚未解锁", icon: "none" });
        return;
      }
      try {
        const action = skill.装备 || skill.equipped ? "unequip_martial" : "use_martial";
        const skillName = skill.名称 || skill.name;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: action,
          data: { skill_name: skillName }
        });
        if (response.type === action + "_success") {
          if (response.data.player) {
            if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
              response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map((name) => {
                return { name, ...response.data.player.martial_skills[name] };
              });
            }
            this.player = Object.assign({}, response.data.player);
            utils_gameState.gameState.player = response.data.player;
            await this.fetchPlayerDataFromServer();
            this.syncSelectedMartials();
            this.updateData();
            this.$forceUpdate();
          } else {
            await this.fetchPlayerDataFromServer();
          }
          await this.fetchBonusSummary();
          this.$forceUpdate();
          common_vendor.index.showToast({ title: response.data.message, icon: "success" });
        } else {
          common_vendor.index.showToast({ title: response.data.message, icon: "none" });
        }
      } catch (error) {
        console.error("武学操作失败:", error);
        common_vendor.index.showToast({ title: "操作失败", icon: "none" });
      }
    },
    async showSkillDetail(skill) {
      if (this.martialConfigs.length === 0) {
        await this.loadMartialConfigs();
      }
      this.selectedSkill = skill;
      this.showDetail = true;
    },
    closeDetail() {
      this.showDetail = false;
      this.selectedSkill = null;
    },
    showMoves(skill) {
      this.selectedSkill = skill;
      this.showMovesModal = true;
    },
    closeMovesModal() {
      this.showMovesModal = false;
      this.selectedSkill = null;
    },
    onTrainMartialClick() {
      this.trainMartial(this.selectedSkill);
    },
    async trainMartial(skill) {
      if (!this.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!skill.解锁 && !skill.unlocked) {
        common_vendor.index.showToast({ title: "该武学尚未解锁", icon: "none" });
        return;
      }
      if (this.player.skill_points <= 0) {
        common_vendor.index.showToast({ title: "武学点不足", icon: "none" });
        return;
      }
      try {
        this.showTrainLoading = true;
        if (!this.showTrainResultModal) {
          this.trainLog = [];
          this.showTrainResultModal = true;
        }
        this.resetTrainLogTimeout();
        utils_gameData.gameUtils.sendMessage({
          type: "train_martial",
          data: { name: skill.name || skill.名称 }
        });
      } catch (error) {
        console.error("【调试】修炼异常", error);
        this.appendTrainLog("修炼失败");
        common_vendor.index.showToast({ title: "修炼失败", icon: "none" });
        this.showTrainLoading = false;
        this.clearTrainLogTimeout();
      }
    },
    isLifeSkill(skill) {
      const skillName = skill.名称 || skill.name || "";
      return skill.类别 === "生活技能" || skill.类别 === "生活类" || skillName.includes("采") || skillName.includes("药") || skillName.includes("伐木") || skillName.includes("挖矿") || skillName.includes("剥皮");
    },
    openMartialSelect(type) {
      this.martialSelectType = type;
      const found = this.martialTypes.find((t) => t.key === type);
      this.martialSelectTypeLabel = found ? found.label : type;
      this.showMartialSelect = true;
    },
    closeMartialSelect() {
      this.showMartialSelect = false;
    },
    async selectMartial(skill, type) {
      this.selectedMartials[type] = skill.name;
      this.showMartialSelect = false;
      if (skill.unlocked || skill.解锁) {
        if (this.isMartialEquipped(skill)) {
          return;
        }
        try {
          const skillName = skill.name || skill.名称;
          common_vendor.index.showLoading({
            title: "正在装备...",
            mask: true
          });
          const response = await utils_gameData.gameUtils.sendMessage({
            type: "use_martial",
            data: { skill_name: skillName }
          });
          common_vendor.index.hideLoading();
          if (response && response.type === "use_martial_success") {
            if (response.data.player) {
              if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
                response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map((name) => {
                  return { name, ...response.data.player.martial_skills[name] };
                });
              }
              this.player = Object.assign({}, response.data.player);
              utils_gameState.gameState.player = response.data.player;
              await this.fetchPlayerDataFromServer();
              this.syncSelectedMartials();
              this.updateData();
              const updatedSkill = this.player.martial_skills.find((s) => s.name === skillName || s.名称 === skillName);
              if (updatedSkill) {
                updatedSkill.equipped = true;
                if (updatedSkill.装备 !== void 0) {
                  updatedSkill.装备 = true;
                }
              }
              this.updateData();
              await this.fetchBonusSummary();
              setTimeout(() => {
                this.$forceUpdate();
              }, 50);
              common_vendor.index.showToast({
                title: response.data.message || `成功装备${skillName}`,
                icon: "success"
              });
            }
          } else {
            common_vendor.index.showToast({
              title: response && response.data && response.data.message || "装备失败",
              icon: "none"
            });
          }
        } catch (error) {
          common_vendor.index.hideLoading();
          console.error("装备武功失败:", error);
          common_vendor.index.showToast({ title: "操作异常", icon: "none" });
        }
      } else {
        common_vendor.index.showToast({ title: "该武学尚未解锁", icon: "none" });
      }
    },
    async fetchBonusSummary() {
      try {
        const res = await utils_gameData.gameUtils.sendMessage({
          type: "get_bonus_summary",
          data: {}
        });
        if (res.type === "bonus_summary") {
          this.bonusSummary = res.data;
        }
      } catch (e) {
        console.warn("获取增益摘要失败", e);
      }
    },
    isBasicMartial(skill) {
      const basicNames = ["基本剑法", "基本拳法", "基本刀法", "基本棍法", "基本招架", "基本轻功", "基础内功", "基础暗器", "基本暗器法"];
      return basicNames.includes(skill.name) || basicNames.includes(skill.名称);
    },
    syncSelectedMartials() {
      this.selectedMartials = {};
      if (this.player && this.player.martial_skills) {
        this.player.martial_skills.forEach((skill) => {
          if (skill.equipped) {
            const type = skill.type || skill.类型 || skill.类别 || "其他";
            this.selectedMartials[type] = skill.name;
          }
        });
      }
    },
    getSkillLevelUpExp(skill) {
      if (skill.maxExp)
        return skill.maxExp;
      const isLifeSkill = this.isLifeSkill(skill);
      const quality = skill.quality || skill.品质 || "普通";
      const level = skill.level || skill.等级 || 0;
      let coefficient = 50;
      if (isLifeSkill) {
        coefficient = 60;
      } else {
        if (quality === "稀有")
          coefficient = 80;
        else if (quality === "绝世")
          coefficient = 120;
        else if (quality === "传说")
          coefficient = 200;
      }
      return coefficient * Math.pow(level + 1, 2);
    },
    closeTrainResultModal() {
      this.showTrainResultModal = false;
      this.trainLog = [];
      this.clearTrainLogTimeout();
    },
    appendTrainLog(msg) {
      this.trainLog.push(msg);
      this.$nextTick(() => {
        this.trainLogScrollTop = 99999 + this.trainLog.length;
      });
    },
    resetTrainLogTimeout() {
      this.clearTrainLogTimeout();
      this.trainLogTimeout = setTimeout(() => {
        if (this.trainFinished)
          return;
        this.showTrainLoading = false;
        this.appendTrainLog("【提示】修炼超时，请检查网络或稍后重试。");
        common_vendor.index.showToast({ title: "修炼超时，请检查网络", icon: "none" });
      }, 1e4);
    },
    clearTrainLogTimeout() {
      if (this.trainLogTimeout) {
        clearTimeout(this.trainLogTimeout);
        this.trainLogTimeout = null;
      }
      this.trainFinished = false;
    },
    handleReconnectCloseAllPopups() {
      this.showSkillDetail = false;
      this.showTrainResultModal = false;
      this.showTrainLoading = false;
      this.trainLog = [];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b;
  return common_vendor.e({
    a: $data.showTrainLoading
  }, $data.showTrainLoading ? {} : {}, {
    b: $data.trainResultMsg
  }, $data.trainResultMsg ? {
    c: common_vendor.t($data.trainResultMsg)
  } : {}, {
    d: $data.activeTab === "equip" ? 1 : "",
    e: common_vendor.o(($event) => $options.switchTab("equip")),
    f: $data.activeTab === "skills" ? 1 : "",
    g: common_vendor.o(($event) => $options.switchTab("skills")),
    h: $data.activeTab === "life" ? 1 : "",
    i: common_vendor.o(($event) => $options.switchTab("life")),
    j: Object.values($options.martialSkillsByType).some((arr) => arr.length > 0) && $data.activeTab === "equip"
  }, Object.values($options.martialSkillsByType).some((arr) => arr.length > 0) && $data.activeTab === "equip" ? {
    k: common_vendor.f($data.martialTypes, (typeObj, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(typeObj.label),
        b: common_vendor.t($options.getSelectedMartialName(typeObj.key) || ($options.getMartialsByType(typeObj.key).length === 0 ? "未装备" : "请选择武功")),
        c: common_vendor.t($options.getSelectedMartial(typeObj.key) && $options.isMartialEquipped($options.getSelectedMartial(typeObj.key)) ? "(已装备)" : ""),
        d: common_vendor.o(($event) => $options.openMartialSelect(typeObj.key), typeObj.key),
        e: $options.getMartialsByType(typeObj.key).length === 0,
        f: $options.getSelectedMartial(typeObj.key) && $options.isMartialEquipped($options.getSelectedMartial(typeObj.key))
      }, $options.getSelectedMartial(typeObj.key) && $options.isMartialEquipped($options.getSelectedMartial(typeObj.key)) ? {
        g: common_vendor.o(($event) => $options.unequipMartial(typeObj.key), typeObj.key)
      } : {}, {
        h: typeObj.key
      });
    })
  } : $data.activeTab === "equip" ? {} : {}, {
    l: $data.activeTab === "equip",
    m: $data.activeTab === "skills"
  }, $data.activeTab === "skills" ? common_vendor.e({
    n: $options.learnedMartials.length
  }, $options.learnedMartials.length ? common_vendor.e({
    o: common_vendor.t($options.learnedMartialCount),
    p: common_vendor.f($options.learnedMartials, (skill, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(skill.名称 || skill.name),
        b: common_vendor.t(skill.等级 || skill.level),
        c: skill.门派 || skill.school && skill.school !== "无门派"
      }, skill.门派 || skill.school && skill.school !== "无门派" ? {
        d: common_vendor.t(skill.门派 || skill.school)
      } : {}, {
        e: common_vendor.o(($event) => $options.showSkillDetail(skill), skill.名称 || skill.name),
        f: skill.名称 || skill.name
      });
    }),
    q: $options.learnedMartials.length === 0
  }, $options.learnedMartials.length === 0 ? {} : {}) : {}) : {}, {
    r: $data.activeTab === "life"
  }, $data.activeTab === "life" ? common_vendor.e({
    s: $options.lifeSkills.length
  }, $options.lifeSkills.length ? common_vendor.e({
    t: common_vendor.f($options.lifeSkills, (skill, k0, i0) => {
      return {
        a: common_vendor.t(skill.名称 || skill.name),
        b: common_vendor.t(skill.等级 || skill.level),
        c: common_vendor.o(($event) => $options.showSkillDetail(skill), skill.名称 || skill.name),
        d: skill.名称 || skill.name
      };
    }),
    v: $options.lifeSkills.length === 0
  }, $options.lifeSkills.length === 0 ? {} : {}) : {}) : {}, {
    w: $data.showDetail
  }, $data.showDetail ? common_vendor.e({
    x: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    y: $data.selectedSkill
  }, $data.selectedSkill ? common_vendor.e({
    z: common_vendor.t($data.selectedSkill.名称 || $data.selectedSkill.name),
    A: common_vendor.t($data.selectedSkill.等级 || $data.selectedSkill.level),
    B: $data.selectedSkill.门派 || $data.selectedSkill.school && $data.selectedSkill.school !== "无门派"
  }, $data.selectedSkill.门派 || $data.selectedSkill.school && $data.selectedSkill.school !== "无门派" ? {
    C: common_vendor.t($data.selectedSkill.门派 || $data.selectedSkill.school)
  } : {}, {
    D: $data.selectedSkill.装备 || $data.selectedSkill.equipped
  }, $data.selectedSkill.装备 || $data.selectedSkill.equipped ? {} : {}, {
    E: common_vendor.t($data.selectedSkill.描述 || $data.selectedSkill.description),
    F: $data.selectedSkill.等级 > 0
  }, $data.selectedSkill.等级 > 0 ? {
    G: $options.getSkillProgress($data.selectedSkill) + "%",
    H: common_vendor.t($data.selectedSkill.经验 || $data.selectedSkill.exp),
    I: common_vendor.t($data.selectedSkill.最大经验 || $data.selectedSkill.maxExp)
  } : {}, {
    J: $data.selectedSkill.等级 > 0
  }, $data.selectedSkill.等级 > 0 ? {
    K: common_vendor.t($options.getSkillEffects($data.selectedSkill))
  } : {}, {
    L: $options.getSkillSpecialEffects($data.selectedSkill)
  }, $options.getSkillSpecialEffects($data.selectedSkill) ? {
    M: common_vendor.t($options.getSkillSpecialEffects($data.selectedSkill))
  } : {}, {
    N: common_vendor.t($data.selectedSkill.exp || $data.selectedSkill.经验 || 0),
    O: common_vendor.t($options.getSkillLevelUpExp($data.selectedSkill)),
    P: $options.getSkillMovesList($data.selectedSkill).length > 0
  }, $options.getSkillMovesList($data.selectedSkill).length > 0 ? {
    Q: common_vendor.f($options.getSkillMovesList($data.selectedSkill), (move, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(move.name),
        b: common_vendor.t(move.unlocked ? "已解锁" : "未解锁"),
        c: move.unlocked ? 1 : "",
        d: !move.unlocked ? 1 : "",
        e: common_vendor.t(move.unlock_level),
        f: move.attack
      }, move.attack ? {
        g: common_vendor.t(move.attack)
      } : {}, {
        h: move.defense
      }, move.defense ? {
        i: common_vendor.t(move.defense)
      } : {}, {
        j: move.name,
        k: move.unlocked ? 1 : "",
        l: !move.unlocked ? 1 : ""
      });
    })
  } : {}) : {}, {
    R: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    S: !$data.selectedSkill.isConfig
  }, !$data.selectedSkill.isConfig ? {
    T: common_vendor.o((...args) => $options.onTrainMartialClick && $options.onTrainMartialClick(...args)),
    U: !$data.isAuthed || !$data.selectedSkill.unlocked || $data.player.skill_points <= 0
  } : {}, {
    V: $data.trainResultMsg
  }, $data.trainResultMsg ? {
    W: common_vendor.t($data.trainResultMsg)
  } : {}, {
    X: common_vendor.o(() => {
    }),
    Y: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args))
  }) : {}, {
    Z: $data.showMovesModal
  }, $data.showMovesModal ? common_vendor.e({
    aa: common_vendor.t(((_a = $data.selectedSkill) == null ? void 0 : _a.名称) || ((_b = $data.selectedSkill) == null ? void 0 : _b.name)),
    ab: common_vendor.o((...args) => $options.closeMovesModal && $options.closeMovesModal(...args)),
    ac: $data.selectedSkill
  }, $data.selectedSkill ? {
    ad: common_vendor.f($data.selectedSkill.招式 || $data.selectedSkill.moves, (move, index, i0) => {
      return {
        a: common_vendor.t(move),
        b: common_vendor.t($options.getMoveDescription($data.selectedSkill.名称 || $data.selectedSkill.name, move)),
        c: index
      };
    })
  } : {}, {
    ae: common_vendor.o((...args) => $options.closeMovesModal && $options.closeMovesModal(...args)),
    af: common_vendor.o(() => {
    }),
    ag: common_vendor.o((...args) => $options.closeMovesModal && $options.closeMovesModal(...args))
  }) : {}, {
    ah: $data.showMartialSelect
  }, $data.showMartialSelect ? common_vendor.e({
    ai: common_vendor.t($data.martialSelectTypeLabel),
    aj: common_vendor.f($options.getMartialsByType($data.martialSelectType), (skill, idx, i0) => {
      return common_vendor.e({
        a: common_vendor.t(skill.name),
        b: common_vendor.t(skill.level || skill.等级 || 1),
        c: common_vendor.t(skill.quality || skill.品质 || "普通"),
        d: $options.isMartialEquipped(skill)
      }, $options.isMartialEquipped(skill) ? {} : {}, {
        e: common_vendor.t(skill.desc || skill.描述 || ""),
        f: skill.name,
        g: skill.name === $data.selectedMartials[$data.martialSelectType] ? 1 : "",
        h: common_vendor.o(($event) => $options.selectMartial(skill, $data.martialSelectType), skill.name)
      });
    }),
    ak: $options.getMartialsByType($data.martialSelectType).length === 0
  }, $options.getMartialsByType($data.martialSelectType).length === 0 ? {} : {}, {
    al: common_vendor.o((...args) => $options.closeMartialSelect && $options.closeMartialSelect(...args)),
    am: common_vendor.o(() => {
    }),
    an: common_vendor.o((...args) => $options.closeMartialSelect && $options.closeMartialSelect(...args))
  }) : {}, {
    ao: $data.showTrainResultModal
  }, $data.showTrainResultModal ? {
    ap: common_vendor.o((...args) => $options.closeTrainResultModal && $options.closeTrainResultModal(...args)),
    aq: common_vendor.f($data.trainLog, (msg, idx, i0) => {
      return {
        a: common_vendor.t(msg),
        b: idx
      };
    }),
    ar: $data.trainLogScrollTop,
    as: common_vendor.o((...args) => $options.closeTrainResultModal && $options.closeTrainResultModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1c7c41d0"]]);
wx.createPage(MiniProgramPage);
