<template>
	<view class="game-container">
		<canvas 
			canvas-id="gameCanvas" 
			id="gameCanvas"
			class="game-canvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			disable-scroll="true"
		></canvas>
	</view>
</template>

<script>
	// 导入小游戏兼容性补丁
	import '../../mini-game-patch.js'
	
	// 导入游戏适配器
	import MiniGameAdapter from '../../miniGameAdapter.js'
	
	// 导入原有的工具和数据
	import gameData from '../../utils/gameData.js'
	import wsManager from '../../utils/websocket.js'
	
	export default {
		data() {
			return {
				gameAdapter: null,
				canvas: null,
				ctx: null
			}
		},
		
		onLoad() {
			console.log('游戏页面加载');
			this.initGame();
		},
		
		onShow() {
			console.log('游戏页面显示');
			if (this.gameAdapter && typeof this.gameAdapter.onShow === 'function') {
				this.gameAdapter.onShow();
			}
		},
		
		onHide() {
			console.log('游戏页面隐藏');
			if (this.gameAdapter && typeof this.gameAdapter.onHide === 'function') {
				this.gameAdapter.onHide();
			}
		},
		
		onUnload() {
			console.log('游戏页面卸载');
			if (this.gameAdapter && typeof this.gameAdapter.destroy === 'function') {
				this.gameAdapter.destroy();
			}
		},
		
		methods: {
			// 初始化游戏
			async initGame() {
				try {
					// 获取Canvas上下文
					await this.initCanvas();
					
					// 创建游戏适配器
					this.gameAdapter = new MiniGameAdapter();
					
					// 设置Canvas
					this.gameAdapter.setCanvas(this.canvas, this.ctx);
					
					// 设置原有的数据和WebSocket管理器
					this.gameAdapter.setGameData(gameData);
					this.gameAdapter.setWebSocketManager(wsManager);
					
					// 初始化游戏
					await this.gameAdapter.init();
					
					console.log('游戏初始化完成');
					
				} catch (error) {
					console.error('游戏初始化失败:', error);
					uni.showToast({
						title: '游戏初始化失败',
						icon: 'none'
					});
				}
			},
			
			// 初始化Canvas
			initCanvas() {
				return new Promise((resolve, reject) => {
					// 获取系统信息
					uni.getSystemInfo({
						success: (res) => {
							// 创建Canvas上下文
							this.ctx = uni.createCanvasContext('gameCanvas', this);
							
							// 设置Canvas尺寸
							const query = uni.createSelectorQuery().in(this);
							query.select('#gameCanvas').boundingClientRect((rect) => {
								if (rect) {
									// 设置Canvas实际尺寸
									this.canvas = {
										width: res.screenWidth,
										height: res.screenHeight,
										pixelRatio: res.pixelRatio
									};
									
									console.log('Canvas初始化完成:', this.canvas);
									resolve();
								} else {
									reject(new Error('Canvas元素未找到'));
								}
							}).exec();
						},
						fail: (error) => {
							reject(error);
						}
					});
				});
			},
			
			// 触摸开始
			onTouchStart(e) {
				if (this.gameAdapter && typeof this.gameAdapter.handleTouch === 'function') {
					this.gameAdapter.handleTouch(e, 'start');
				}
			},
			
			// 触摸移动
			onTouchMove(e) {
				if (this.gameAdapter && typeof this.gameAdapter.handleTouch === 'function') {
					this.gameAdapter.handleTouch(e, 'move');
				}
			},
			
			// 触摸结束
			onTouchEnd(e) {
				if (this.gameAdapter && typeof this.gameAdapter.handleTouch === 'function') {
					this.gameAdapter.handleTouch(e, 'end');
				}
			}
		}
	}
</script>

<style scoped>
	.game-container {
		width: 100vw;
		height: 100vh;
		background-color: #000000;
		overflow: hidden;
		position: relative;
	}
	
	.game-canvas {
		width: 100%;
		height: 100%;
		display: block;
		background-color: #000000;
	}
</style>
