<view class="character-compact data-v-3387d3fa"><view wx:if="{{a}}" class="loading-overlay data-v-3387d3fa"><view class="loading-content data-v-3387d3fa"><text class="loading-text data-v-3387d3fa">{{b}}</text></view></view><view class="header-section data-v-3387d3fa"><view class="avatar-section data-v-3387d3fa"><image class="avatar data-v-3387d3fa" src="{{c}}"/></view><view class="info-section data-v-3387d3fa"><view class="name-row data-v-3387d3fa"><text class="name data-v-3387d3fa">{{d}}</text><text class="gender data-v-3387d3fa">{{e}}</text><text class="experience data-v-3387d3fa">📈 历练 {{f}}</text><text class="fortune data-v-3387d3fa">💎 富源 {{g}}</text><text class="sect-info data-v-3387d3fa">🏛️ {{h}}</text></view><view class="stats-row data-v-3387d3fa"><text class="money data-v-3387d3fa">💰 {{i}}</text><text class="skill-points data-v-3387d3fa">🎯 武学 {{j}}</text><text class="realm data-v-3387d3fa">⚔️ {{k}}</text></view></view></view><view class="attributes-grid data-v-3387d3fa"><text class="section-title data-v-3387d3fa">📊 基础属性</text><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">气血</text><text class="attr-value data-v-3387d3fa">{{l}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">内力</text><text class="attr-value data-v-3387d3fa">{{m}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">体力</text><text class="attr-value data-v-3387d3fa">{{n}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">精力</text><text class="attr-value data-v-3387d3fa">{{o}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">攻击</text><text class="attr-value data-v-3387d3fa">{{p}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">防御</text><text class="attr-value data-v-3387d3fa">{{q}}</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">闪避</text><text class="attr-value data-v-3387d3fa">{{r}}%</text></view><view class="attr-item data-v-3387d3fa"><text class="attr-label data-v-3387d3fa">暴击</text><text class="attr-value data-v-3387d3fa">{{s}}%</text></view></view><view wx:if="{{t}}" class="data-v-3387d3fa" style="background:#e8f4ff;padding:10rpx;margin:10rpx 0;font-size:22rpx;color:#333"><text class="data-v-3387d3fa">装备加成：攻击{{v}}，防御{{w}}，气血{{x}}</text><view class="data-v-3387d3fa"/><text class="data-v-3387d3fa">武功加成：攻击{{y}}，防御{{z}}，气血{{A}}</text><view class="data-v-3387d3fa"/><text class="data-v-3387d3fa">总加成：攻击{{B}}，防御{{C}}，气血{{D}}</text></view><view class="talent-section data-v-3387d3fa"><text class="section-title data-v-3387d3fa">天赋属性</text><view class="talent-list data-v-3387d3fa" style="display:flex;flex-direction:row;justify-content:space-between;align-items:center"><view wx:for="{{E}}" wx:for-item="item" wx:key="c" class="attr-item data-v-3387d3fa" style="flex:1 1 0;text-align:center" bindtap="{{item.d}}"><text class="attr-label data-v-3387d3fa">{{item.a}}</text><text class="attr-value data-v-3387d3fa">{{item.b}}</text></view></view></view><view wx:if="{{F}}" class="talent-modal-mask data-v-3387d3fa" bindtap="{{ab}}"><view class="talent-modal-content data-v-3387d3fa" catchtap="{{aa}}"><view class="talent-modal-header data-v-3387d3fa"><text class="talent-modal-title data-v-3387d3fa">{{G}}详情</text><text class="talent-modal-close data-v-3387d3fa" bindtap="{{H}}">×</text></view><view class="talent-modal-body data-v-3387d3fa"><view class="talent-current data-v-3387d3fa"><text class="talent-current-label data-v-3387d3fa">当前{{I}}：</text><text class="talent-current-value data-v-3387d3fa">{{J}}</text></view><view wx:if="{{K}}" class="talent-effects data-v-3387d3fa"><text class="talent-effects-title data-v-3387d3fa">根骨增益效果：</text><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">气血加成：</text><text class="effect-value data-v-3387d3fa">+{{L}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">实际提升：</text><text class="effect-value data-v-3387d3fa">+{{M}}点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">体力恢复加成：</text><text class="effect-value data-v-3387d3fa">+{{N}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">基础根骨值：</text><text class="effect-value data-v-3387d3fa">15点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">每点根骨增加：</text><text class="effect-value data-v-3387d3fa">0.5%气血，2%恢复速度</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">最大加成：</text><text class="effect-value data-v-3387d3fa">100%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">当前恢复速度：</text><text class="effect-value data-v-3387d3fa">{{O}}/10秒</text></view></view><view wx:elif="{{P}}" class="talent-effects data-v-3387d3fa"><text class="talent-effects-title data-v-3387d3fa">力量增益效果：</text><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">攻击力加成：</text><text class="effect-value data-v-3387d3fa">+{{Q}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">基础力量值：</text><text class="effect-value data-v-3387d3fa">15点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">每点力量增加：</text><text class="effect-value data-v-3387d3fa">0.3%攻击力</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">实际提升：</text><text class="effect-value data-v-3387d3fa">+{{R}}点</text></view></view><view wx:elif="{{S}}" class="talent-effects data-v-3387d3fa"><text class="talent-effects-title data-v-3387d3fa">悟性增益效果：</text><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">经验获取加成：</text><text class="effect-value data-v-3387d3fa">+{{T}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">基础悟性值：</text><text class="effect-value data-v-3387d3fa">15点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">每点悟性增加：</text><text class="effect-value data-v-3387d3fa">0.5%经验获取</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">经验倍率：</text><text class="effect-value data-v-3387d3fa">{{U}}x</text></view></view><view wx:elif="{{V}}" class="talent-effects data-v-3387d3fa"><text class="talent-effects-title data-v-3387d3fa">身法增益效果：</text><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">防御力加成：</text><text class="effect-value data-v-3387d3fa">+{{W}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">实际提升：</text><text class="effect-value data-v-3387d3fa">+{{X}}点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">闪避加成：</text><text class="effect-value data-v-3387d3fa">+{{Y}}%</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">基础身法值：</text><text class="effect-value data-v-3387d3fa">15点</text></view><view class="talent-effect-item data-v-3387d3fa"><text class="effect-label data-v-3387d3fa">每点身法增加：</text><text class="effect-value data-v-3387d3fa">0.5%防御力，0.5%闪避</text></view></view></view><view class="talent-modal-footer data-v-3387d3fa"><button class="talent-modal-btn data-v-3387d3fa" bindtap="{{Z}}">关闭</button></view></view></view><view class="realm-section data-v-3387d3fa"><view class="realm-header data-v-3387d3fa"><text class="realm-title data-v-3387d3fa">下一境界：{{ac}}</text><text class="realm-progress data-v-3387d3fa">{{ad}} / {{ae}}</text></view><view class="progress-bar data-v-3387d3fa"><view class="progress-fill data-v-3387d3fa" style="{{'width:' + af}}"></view></view><text class="realm-bonus data-v-3387d3fa">{{ag}}</text></view><view class="equipment-section data-v-3387d3fa"><text class="section-title data-v-3387d3fa">装备</text><view class="equipment-grid data-v-3387d3fa"><view wx:for="{{ah}}" wx:for-item="slot" wx:key="e" class="equip-slot data-v-3387d3fa" bindtap="{{slot.f}}"><block wx:if="{{slot.a}}"><text class="equip-icon data-v-3387d3fa">{{slot.b}}</text><text class="equip-name data-v-3387d3fa">{{slot.c}}</text></block><block wx:else><text class="empty-text data-v-3387d3fa">{{slot.d}}</text></block></view></view></view><view wx:if="{{ai}}" class="equipment-modal-mask data-v-3387d3fa" bindtap="{{ax}}"><view class="equipment-modal-content data-v-3387d3fa" catchtap="{{aw}}"><view class="equipment-modal-header data-v-3387d3fa"><text class="equipment-modal-title data-v-3387d3fa">{{aj}}</text><text class="equipment-modal-close data-v-3387d3fa" bindtap="{{ak}}">×</text></view><view class="equipment-modal-body data-v-3387d3fa"><view class="equipment-info data-v-3387d3fa"><view class="equipment-basic data-v-3387d3fa"><text class="equipment-icon data-v-3387d3fa">{{al}}</text><view class="equipment-details data-v-3387d3fa"><text class="equipment-type data-v-3387d3fa">类型：{{am}}</text><text class="equipment-quality data-v-3387d3fa">品质：{{an}}</text><text class="equipment-slot data-v-3387d3fa">槽位：{{ao}}</text></view></view><view class="equipment-stats data-v-3387d3fa"><text class="stats-title data-v-3387d3fa">战斗属性</text><view wx:if="{{ap}}" class="equipment-effects data-v-3387d3fa"><view wx:for="{{aq}}" wx:for-item="effect" wx:key="c" class="stat-item data-v-3387d3fa"><text class="stat-label data-v-3387d3fa">{{effect.a}}：</text><text class="stat-value data-v-3387d3fa">+{{effect.b}}</text></view></view><view wx:else class="no-effects data-v-3387d3fa"><text class="no-effects-text data-v-3387d3fa">无属性加成</text></view></view><view wx:if="{{ar}}" class="equipment-description data-v-3387d3fa"><text class="description-title data-v-3387d3fa">描述</text><text class="description-text data-v-3387d3fa">{{as}}</text></view></view></view><view class="equipment-modal-actions data-v-3387d3fa"><button class="equipment-action-btn unequip-btn data-v-3387d3fa" bindtap="{{at}}"><text class="data-v-3387d3fa">卸下装备</text></button><button class="equipment-action-btn close-btn data-v-3387d3fa" bindtap="{{av}}"><text class="data-v-3387d3fa">关闭</text></button></view></view></view><view class="action-section data-v-3387d3fa"><button class="action-btn healing-btn data-v-3387d3fa" bindtap="{{ay}}"><text class="data-v-3387d3fa">🏥 疗伤</text></button><button class="action-btn crafting-btn data-v-3387d3fa" bindtap="{{az}}"><text class="data-v-3387d3fa">⚒️ 打造</text></button></view><view class="action-section data-v-3387d3fa"><button class="action-btn breakthrough-btn data-v-3387d3fa" bindtap="{{aA}}" disabled="{{aB}}"><text class="data-v-3387d3fa">🌟 境界突破</text></button><button class="action-btn backpack-btn data-v-3387d3fa" bindtap="{{aC}}"><text class="data-v-3387d3fa">🎒 背包</text></button></view></view>