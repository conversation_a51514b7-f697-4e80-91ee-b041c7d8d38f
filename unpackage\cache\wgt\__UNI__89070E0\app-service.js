if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((a=>t.resolve(e()).then((()=>a))),(a=>t.resolve(e()).then((()=>{throw a}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64Array,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...a){uni.__log__?uni.__log__(e,t,...a):console[e].apply(console,[...a,t])}const a=new class{constructor(){this.ws=null,this.isConnected=!1,this.isAuthed=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=-1,this.reconnectInterval=3e3,this.maxReconnectInterval=3e4,this.messageQueue=[],this.eventHandlers={},this.connecting=!1,this.heartbeatInterval=null,this.heartbeatTimeout=null,this.manualDisconnect=!1,this.serverUrl="ws://localhost:8080",this.disableAutoAuth=!1,this.isLoginPage=!1,this.debug=!1,this.connectPromise=null,this.on("login_success",(e=>{this.isAuthed=!0,this.log("WebSocket: 登录成功，认证状态已更新")})),this.on("auth_success",(e=>{this.isAuthed=!0,this.log("WebSocket: 认证成功，认证状态已更新");try{const e=require("./gameState").default||require("./gameState");e&&"function"==typeof e.requestAllData&&(this.log("认证成功，自动刷新全局数据"),e.requestAllData())}catch(t){this.error("自动刷新全局数据失败:",t)}})),this.on("login_failed",(e=>{this.isAuthed=!1,this.log("WebSocket: 登录失败，认证状态已重置")})),this.on("auth_failed",(e=>{this.isAuthed=!1,this.log("WebSocket: 认证失败，认证状态已重置")}))}log(e,...t){}triggerReconnectActions(){const e=getCurrentPages(),t=e[e.length-1];t&&t.route&&t.route.includes("pages/index/index")||uni.reLaunch({url:"/pages/index/index"}),uni.$emit&&uni.$emit("ws_reconnected")}error(e,...a){t("error","at utils/websocket.js:78",`[WebSocket错误] ${e}`,...a)}connect(){return this.isConnected?(this.log("WebSocket已连接，无需重新连接"),Promise.resolve()):this.connecting?(this.log("WebSocket正在连接中，请等待..."),this.connectPromise||Promise.reject(new Error("正在连接中"))):(this.connecting=!0,this.connectPromise=new Promise(((e,t)=>{try{this.log("开始连接WebSocket服务器:",this.serverUrl),this.ws=uni.connectSocket({url:this.serverUrl,success:()=>{this.log("WebSocket连接请求已发送")},fail:e=>{this.error("WebSocket连接请求失败:",e),this.connecting=!1,this.connectPromise=null,t(e)}}),uni.onSocketOpen((t=>{this.isConnected=!0,this.connecting=!1,this.connectPromise=null,this.reconnectAttempts=0,this.log("WebSocket连接已建立"),this.processMessageQueue(),this.startHeartbeat(),this.eventHandlers.connected&&this.eventHandlers.connected.forEach((e=>e())),this.triggerReconnectActions(),this.isLoginPage||this.disableAutoAuth?this.log("当前在登录页面或自动认证已禁用，跳过自动认证"):(this.log("准备执行自动认证..."),setTimeout((()=>{this.autoAuthenticate()}),500)),e(t)})),uni.onSocketClose((e=>{this.isConnected=!1,this.connecting=!1,this.connectPromise=null,this.stopHeartbeat(),this.eventHandlers.disconnected&&this.eventHandlers.disconnected.forEach((e=>e())),this.log("WebSocket连接断开，code:",e.code,"reason:",e.reason),this.manualDisconnect||1e3===e.code?this.manualDisconnect&&(this.log("手动断开连接，不进行重连"),this.manualDisconnect=!1):(this.log("检测到异常断开，开始重连..."),this.handleDisconnect())})),uni.onSocketError((e=>{this.isConnected=!1,this.connecting=!1,this.connectPromise=null,this.error("WebSocket连接错误:",e),this.eventHandlers.error&&this.eventHandlers.error.forEach((t=>t(e))),this.handleDisconnect(),t(e)})),uni.onSocketMessage((e=>{try{const t=JSON.parse(e.data);if(this.log("收到消息:",t),"pong"===t.type)return void this.handlePong();this.triggerEvent(t.type,t.data)}catch(t){this.error("消息解析失败:",t,e.data)}})),setTimeout((()=>{!this.isConnected&&this.connecting&&(this.connecting=!1,this.connectPromise=null,this.error("WebSocket连接超时"),t(new Error("连接超时")))}),15e3)}catch(a){this.connecting=!1,this.connectPromise=null,this.error("WebSocket连接异常:",a),t(a)}})),this.connectPromise)}setLoginPageStatus(e){this.isLoginPage=e,this.log("设置登录页面状态:",e?"当前在登录页面":"当前不在登录页面")}disconnect(){this.ws&&this.isConnected&&(this.log("手动断开WebSocket连接"),uni.closeSocket(),this.isConnected=!1,this.connecting=!1,this.manualDisconnect=!0)}handleDisconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts&&-1!==this.maxReconnectAttempts)return void this.log("达到最大重连次数，停止重连");const e=Math.min(this.reconnectInterval*Math.pow(1.5,this.reconnectAttempts),this.maxReconnectInterval);this.reconnectAttempts++,this.log(`第${this.reconnectAttempts}次重连，延迟${e}ms`),setTimeout((()=>{this.isConnected||this.connecting||(this.log("执行重连..."),this.connect().catch((e=>{this.error("重连失败:",e)})))}),e)}sendMessage(e,t={}){const a=JSON.stringify({type:e,data:t});return this.isConnected?new Promise(((s,n)=>{this.log("发送消息:",{type:e,data:t}),uni.sendSocketMessage({data:a,success:()=>{this.log("消息发送成功:",{type:e,data:t}),s({type:e,data:t})},fail:s=>{this.error("消息发送失败:",s,{type:e,data:t}),this.messageQueue.push(a),n(s)}})})):(this.log("WebSocket未连接，消息加入队列:",{type:e,data:t}),this.messageQueue.push(a),Promise.reject(new Error("WebSocket未连接")))}processMessageQueue(){if(0===this.messageQueue.length)return;this.log(`处理消息队列，共${this.messageQueue.length}条消息`);const e=[...this.messageQueue];this.messageQueue=[],e.forEach((e=>{try{uni.sendSocketMessage({data:e,success:()=>{this.log("队列消息发送成功:",e)},fail:t=>{this.error("队列消息发送失败:",t,e),this.messageQueue.unshift(e)}})}catch(t){this.error("队列消息发送失败:",t,e),this.messageQueue.unshift(e)}}))}triggerEvent(e,t){if("game_event"===e)try{const e=require("./gameState").default;if(e&&"function"==typeof e.handleGameEvent){this.log("特殊处理game_event事件",t);const a={type:t.type||5,content:t.content||t.description||"你遇到了一个江湖事件",rewards:t.rewards||{},realm_breakthrough:t.realm_breakthrough||null};e.handleGameEvent(a)}}catch(a){this.error("处理game_event事件失败:",a)}this.eventHandlers[e]?(this.log(`触发事件: ${e}`,t),this.eventHandlers[e].forEach((e=>e(t)))):this.log(`没有处理程序的事件: ${e}`,t)}on(e,t){this.eventHandlers[e]||(this.eventHandlers[e]=[]),this.eventHandlers[e].push(t),this.log(`注册事件处理程序: ${e}`)}off(e,t){if(!this.eventHandlers[e])return;const a=this.eventHandlers[e].indexOf(t);a>-1&&this.eventHandlers[e].splice(a,1)}autoAuthenticate(){this.log("开始自动认证...");const e=uni.getStorageSync("token");this.log("认证信息检查:"),this.log("- token存在:",!!e),e?(this.log("✅ 找到登录信息，发送认证消息"),this.sendMessage("auth",{token:e}),this.log("✅ 认证消息已发送")):this.log("❌ 未找到登录信息，无法自动认证")}authenticate(){const e=uni.getStorageSync("token")||"";this.log("手动发送认证消息"),this.sendMessage("auth",{token:e})}sendGameAction(e,t={}){this.sendMessage("game_action",{action:e,...t})}requestPlayerData(){this.log("WebSocket: 请求玩家数据"),this.sendMessage("get_player_data")}requestInventoryData(){this.sendMessage("get_inventory_data")}requestSkillsData(){this.sendMessage("get_skills_data")}sendAdventureRequest(){this.log("=== WebSocket: 发送闯江湖请求 ==="),this.log("当前时间:",(new Date).toLocaleString()),this.log("WebSocket连接状态:",this.isConnected),this.log("服务器地址:",this.serverUrl),this.isConnected?(this.log("✅ 发送adventure消息"),this.sendMessage("adventure"),this.log("✅ WebSocket: 闯江湖请求已发送")):this.log("❌ WebSocket未连接，无法发送请求")}sendMeditationRequest(){this.sendMessage("meditation")}sendHealingRequest(){this.sendMessage("healing")}sendEquipmentAction(e,t){this.sendMessage("equipment_action",{action:e,itemId:t})}sendSkillAction(e,t){this.sendMessage("skill_action",{action:e,skillId:t})}sendShopAction(e,t){this.sendMessage("shop_action",{action:e,...t})}sendMarketAction(e,t){this.sendMessage("market_action",{action:e,...t})}sendGuildAction(e,t){this.sendMessage("guild_action",{action:e,...t})}sendCraftingAction(e,t){this.sendMessage("crafting_action",{action:e,...t})}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=setInterval((()=>{this.isConnected&&(this.sendMessage("ping"),this.heartbeatTimeout=setTimeout((()=>{this.log("心跳超时，断开连接"),this.disconnect(),setTimeout((()=>{this.connect()}),1e3)}),1e4))}),3e5)}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null),this.heartbeatTimeout&&(clearTimeout(this.heartbeatTimeout),this.heartbeatTimeout=null)}handlePong(){this.heartbeatTimeout&&(clearTimeout(this.heartbeatTimeout),this.heartbeatTimeout=null)}},s={calculateDamage(e,t,a=null){let s=e.attack;a&&(s*=a.damageMultiplier||1);const n=Math.random()<e.critRate;n&&(s*=e.critDamage);const i=Math.max(1,s-t.defense);return{damage:Math.floor(i),isCrit:n}},isHit(e,t){const a=e.hitRate-t.dodgeRate;return Math.random()<a},isBlocked:e=>Math.random()<e.blockRate,generateRandomEvent:()=>({type:4,name:"普通事件",description:"你漫步在江湖中，感受着武侠世界的魅力。"}),formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"万":e.toString(),getQualityColor:e=>({common:"#9e9e9e",uncommon:"#4caf50",rare:"#2196f3",epic:"#9c27b0",legendary:"#ff9800",mythic:"#f44336"}[e]||"#9e9e9e"),sendMessage:e=>new Promise(((t,s)=>{let n=e.type,i=e.data||{};"npc_function"===n&&(i={npc_name:e.npc_name,function:e.function,data:e.data||{}}),"get_equipment_data"===n&&(n="get_player_data");let l=!1;const o=setTimeout((()=>{l||(l=!0,c(),t({type:n+"_timeout",data:{message:"请求超时，请检查网络连接"}}))}),"escape_battle"===n?15e3:"sect_action"===n?2e4:1e4),c=()=>{clearTimeout(o),a.off(n+"_success",r),a.off(n+"_failed",r),a.off(n+"_error",r),a.off("error",d),"market_action"===n&&(a.off("market_list",r),a.off("success",r),a.off("error",r)),"get_map_npcs"===n&&a.off("map_npcs",r),"npc_function"===n&&(a.off("npc_talk",r),a.off("shop_items",r),a.off("buy_success",r),a.off("sell_success",r),a.off("transport_destinations",r),a.off("transport_success",r),a.off("heal_success",r),a.off("info_services",r),a.off("info_success",r)),"get_bonus_summary"===n&&a.off("bonus_summary",r),"get_inventory_data"===n&&a.off("inventory_data",r),"get_skills_data"===n&&a.off("skills_data",r),"get_martial_configs"===n&&a.off("martial_configs",r),"equip_item"===n&&(a.off("equip_success",r),a.off("equip_failed",r)),"get_player_data"===n&&a.off("player_data",r),"get_player_data"===n&&(a.off("get_player_data_success",r),a.off("get_player_data_failed",r),a.off("get_player_data_error",r)),"crafting_action"===n&&(a.off("get_craftable_success",r),a.off("get_craftable_failed",r),a.off("craft_success",r),a.off("craft_failed",r)),"start_battle_from_encounter"===n&&(a.off("start_battle_from_encounter_success",r),a.off("start_battle_from_encounter_failed",r)),"escape_battle"===n&&a.off("escape_battle_result",r),"get_ranking"===n&&(a.off("success",r),a.off("error",r)),"gather_action"===n&&a.off("gathering_result",r)},r=a=>{if(l)return;if(l=!0,c(),"get_player_data"===n&&"get_equipment_data"===e.type&&a&&a.equipment)return void t({type:"equipment_data",data:a.equipment});let s=n+(!1!==a.success?"_success":"_failed");return"get_inventory_data"===n&&void 0!==a.inventory&&(s="inventory_data"),"get_skills_data"===n&&void 0!==a.skills&&(s="skills_data"),"equip_item"===n&&(s=!1!==a.success?"equip_success":"equip_failed"),"get_player_data"===n&&"player_data"===a.type&&(s="player_data"),"market_action"===n?(s=a.type||s,void t({type:s,data:a.data||a})):("crafting_action"===n&&"get_craftable_success"===a.type&&(s="get_craftable_success"),"crafting_action"===n&&"craft_success"===a.type&&(s="craft_success"),"crafting_action"===n&&"craft_failed"===a.type&&(s="craft_failed"),"get_ranking"===n?("success"===a.type?s="get_ranking_success":"error"===a.type&&(s="get_ranking_failed"),void t({type:s,data:a.data||a})):"redeem_code"===n?("success"===a.type?s="redeem_code_success":"error"===a.type&&(s="redeem_code_failed"),void t({type:s,data:a.data||a})):void t("npc_function"!==n?{type:s,data:a}:{type:a.type||s,data:a.data||a}))},d=a=>{"get_equipment_data"!==e.type?t({type:n+"_failed",data:{message:a.message||"请求失败"}}):t({type:"equipment_data",data:{}})};a.on(n+"_success",r),a.on(n+"_failed",r),a.on(n+"_error",r),a.on("error",d),"crafting_action"===n&&(a.on("get_craftable_success",r),a.on("get_craftable_failed",r),a.on("craft_success",r),a.on("craft_failed",r)),"start_battle_from_encounter"===n&&(a.on("start_battle_from_encounter_success",r),a.on("start_battle_from_encounter_failed",r)),"escape_battle"===n&&a.on("escape_battle_result",r),"get_player_data"===n&&a.on("player_data",r),"get_player_data"===n&&(a.on("get_player_data_success",r),a.on("get_player_data_failed",r),a.on("get_player_data_error",r)),"market_action"===n&&(a.on("market_list",r),a.on("success",r),a.on("error",r)),"get_map_npcs"===n&&a.on("map_npcs",r),"npc_function"===n&&(a.on("npc_talk",r),a.on("shop_items",r),a.on("buy_success",r),a.on("sell_success",r),a.on("transport_destinations",r),a.on("transport_success",r),a.on("heal_success",r),a.on("info_services",r),a.on("info_success",r),a.on("error",r)),"get_bonus_summary"===n&&a.on("bonus_summary",r),"get_inventory_data"===n&&a.on("inventory_data",r),"gather_action"===n&&a.on("gathering_result",r),"get_skills_data"===n&&a.on("skills_data",r),"get_martial_configs"===n&&a.on("martial_configs",r),"equip_item"===n&&(a.on("equip_success",r),a.on("equip_failed",r)),"get_ranking"===n&&(a.on("success",r),a.on("error",r)),"redeem_code"===n&&(a.on("success",r),a.on("error",r)),a.isConnected?a.sendMessage(n,i):a.connect().then((()=>{a.sendMessage(n,i)})).catch((e=>{t({type:n+"_failed",data:{message:"连接服务器失败，请检查网络"}})}))}))},n=(e,t)=>{const a=e.__vccOpts||e;for(const[s,n]of t)a[s]=n;return a};const i=n({data:()=>({isRegistering:!1,isLoading:!1,loadingText:"正在处理...",loginForm:{username:"",password:""},registerForm:{username:"",password:"",confirmPassword:"",characterName:"",gender:"男"}}),computed:{isFormValid(){const e=this.registerForm;return e.username.length>=3&&e.username.length<=20&&e.password.length>=6&&e.password.length<=20&&e.password===e.confirmPassword&&e.characterName.length>=2&&e.characterName.length<=10}},onLoad(){a.setLoginPageStatus(!0),setTimeout((()=>{this.checkLoginStatus()}),1e3)},onUnload(){a.setLoginPageStatus(!1)},methods:{checkLoginStatus(){const e=uni.getStorageSync("token"),t=uni.getStorageSync("userInfo");e&&t&&(uni.showToast({title:"发现已保存的登录信息",icon:"none",duration:1500}),setTimeout((()=>{this.autoLogin()}),1500))},async autoLogin(){try{const e=uni.getStorageSync("token"),t=uni.getStorageSync("userInfo");if(!e)return;this.isLoading=!0,this.loadingText="正在自动登录...",await a.connect();const n=await s.sendMessage({type:"auth",data:{token:e,userInfo:t}});n&&"auth_success"===n.type?this.handleAutoLoginSuccess(n.data):n&&"auth_failed"===n.type?this.handleAutoLoginFailed(n.data):(this.isLoading=!1,uni.showToast({title:"自动登录失败，请手动登录",icon:"none"}))}catch(e){this.isLoading=!1,uni.showToast({title:"自动登录失败，请手动登录",icon:"none"})}},handleAutoLoginSuccess(e){this.isLoading=!1,e.userInfo&&uni.setStorageSync("userInfo",e.userInfo),uni.showToast({title:"自动登录成功",icon:"success"}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index"})}),1e3)},handleAutoLoginFailed(e){this.isLoading=!1,uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.showToast({title:e.message||"自动登录失败，请重新登录",icon:"none",duration:3e3})},switchToRegister(){this.isRegistering=!0},switchToLogin(){this.isRegistering=!1},selectGender(e){this.registerForm.gender=e},async handleLogin(){this.loginForm.username&&this.loginForm.password?(this.isLoading=!0,this.loadingText="正在登录...",this._loginSuccessHandler||(this._loginSuccessHandler=e=>{this.isLoading=!1,e&&e.token&&uni.setStorageSync("token",e.token),e&&e.userInfo&&uni.setStorageSync("userInfo",e.userInfo),uni.showToast({title:"登录成功",icon:"success"}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index"})}),1e3),a.off("login_success",this._loginSuccessHandler),a.off("login_failed",this._loginFailedHandler)},a.on("login_success",this._loginSuccessHandler)),this._loginFailedHandler||(this._loginFailedHandler=e=>{this.isLoading=!1,uni.showToast({title:e&&e.message?e.message:"登录失败，请重试",icon:"none"}),a.off("login_success",this._loginSuccessHandler),a.off("login_failed",this._loginFailedHandler)},a.on("login_failed",this._loginFailedHandler)),await a.connect(),a.sendMessage("login",{username:this.loginForm.username,password:this.loginForm.password})):uni.showToast({title:"请输入账号和密码",icon:"none"})},async handleRegister(){if(this.isFormValid)try{this.isLoading=!0,this.loadingText="正在创建角色...",await a.connect();const e=await s.sendMessage({type:"register",data:{username:this.registerForm.username,password:this.registerForm.password,characterName:this.registerForm.characterName,gender:this.registerForm.gender}});e&&"register_success"===e.type?this.handleRegisterSuccess(e.data):e&&"register_failed"===e.type?this.handleRegisterFailed(e.data):(this.isLoading=!1,uni.showToast({title:"注册失败，请重试",icon:"none"}))}catch(e){this.isLoading=!1,uni.showToast({title:"注册失败: "+e.message,icon:"none"})}else uni.showToast({title:"请检查输入信息",icon:"none"})},async handleLoginSuccess(e){this.isLoading=!1;try{uni.setStorageSync("token",e.token),uni.setStorageSync("userInfo",e.userInfo),a.setLoginPageStatus(!1),uni.showToast({title:"登录成功",icon:"success",duration:1500}),a.isConnected||await a.connect(),a.isAuthed=!0,setTimeout((()=>{uni.switchTab({url:"/pages/index/index",success:function(){},fail:function(e){uni.redirectTo({url:"/pages/index/index"})}})}),1500)}catch(t){uni.showToast({title:"登录过程中出现错误",icon:"none"})}},handleLoginFailed(e){this.isLoading=!1,uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.showToast({title:e.message||"登录失败",icon:"none",duration:3e3})},handleRegisterSuccess(e){this.isLoading=!1;try{uni.setStorageSync("token",e.token),uni.setStorageSync("userInfo",e.userInfo),a.setLoginPageStatus(!1);let t="";e.userInfo&&e.userInfo.talent&&(t=`\n力量：${e.userInfo.talent["力量"]}  悟性：${e.userInfo.talent["悟性"]}  身法：${e.userInfo.talent["身法"]}  根骨：${e.userInfo.talent["根骨"]}`);let s=e.userInfo&&e.userInfo.fortune?`\n富源：${e.userInfo.fortune}`:"";uni.showModal({title:"角色创建成功",content:`恭喜你，角色创建成功！${t}${s}`,showCancel:!1,success:async()=>{try{a.isConnected||await a.connect(),a.isAuthed=!0,uni.showToast({title:"正在进入游戏",icon:"success",duration:1500}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index",success:function(){},fail:function(e){uni.redirectTo({url:"/pages/index/index"})}})}),1e3)}catch(e){uni.showToast({title:"进入游戏时出现错误",icon:"none"})}}})}catch(t){uni.showToast({title:"注册过程中出现错误",icon:"none"})}},handleRegisterFailed(e){this.isLoading=!1,uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.showToast({title:e.message||"注册失败",icon:"none"})}}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"game-title"},[e.createElementVNode("text",{class:"title-text"},"仗剑江湖行"),e.createElementVNode("text",{class:"subtitle-text"},"武侠点击式游戏")]),i.isRegistering?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"login-form"},[e.createElementVNode("view",{class:"form-title"},"登录江湖"),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"账号"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"text",class:"input-field","onUpdate:modelValue":a[0]||(a[0]=e=>i.loginForm.username=e),placeholder:"请输入账号","placeholder-style":"color:#999;","confirm-type":"next",maxlength:"20","cursor-spacing":"10"},null,512),[[e.vModelText,i.loginForm.username]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"密码"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"password",class:"input-field","onUpdate:modelValue":a[1]||(a[1]=e=>i.loginForm.password=e),placeholder:"请输入密码","placeholder-style":"color:#999;","confirm-type":"done",maxlength:"20","cursor-spacing":"10",onConfirm:a[2]||(a[2]=(...e)=>l.handleLogin&&l.handleLogin(...e))},null,544),[[e.vModelText,i.loginForm.password]])])]),e.createElementVNode("button",{class:"login-btn primary-btn",onClick:a[3]||(a[3]=(...e)=>l.handleLogin&&l.handleLogin(...e))},[e.createElementVNode("text",{class:"btn-text"},"登录")]),e.createElementVNode("view",{class:"form-footer"},[e.createElementVNode("text",{class:"footer-text"},"还没有账号？"),e.createElementVNode("text",{class:"footer-link",onClick:a[4]||(a[4]=(...e)=>l.switchToRegister&&l.switchToRegister(...e))},"立即注册")])])),i.isRegistering?(e.openBlock(),e.createElementBlock("view",{key:1,class:"register-form"},[e.createElementVNode("view",{class:"form-title"},"创建角色"),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"账号"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"text",class:"input-field","onUpdate:modelValue":a[5]||(a[5]=e=>i.registerForm.username=e),placeholder:"请输入账号（3-20位字符）","placeholder-style":"color:#999;","confirm-type":"next",maxlength:"20","cursor-spacing":"10"},null,512),[[e.vModelText,i.registerForm.username]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"密码"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"password",class:"input-field","onUpdate:modelValue":a[6]||(a[6]=e=>i.registerForm.password=e),placeholder:"请输入密码（6-20位字符）","placeholder-style":"color:#999;","confirm-type":"next",maxlength:"20","cursor-spacing":"10"},null,512),[[e.vModelText,i.registerForm.password]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"确认密码"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"password",class:"input-field","onUpdate:modelValue":a[7]||(a[7]=e=>i.registerForm.confirmPassword=e),placeholder:"请再次输入密码","placeholder-style":"color:#999;","confirm-type":"next",maxlength:"20","cursor-spacing":"10"},null,512),[[e.vModelText,i.registerForm.confirmPassword]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"角色名称"),e.createElementVNode("view",{class:"input-wrapper"},[e.withDirectives(e.createElementVNode("input",{type:"text",class:"input-field","onUpdate:modelValue":a[8]||(a[8]=e=>i.registerForm.characterName=e),placeholder:"请输入角色名称（2-10位字符）","placeholder-style":"color:#999;","confirm-type":"done",maxlength:"10","cursor-spacing":"10",onConfirm:a[9]||(a[9]=(...e)=>l.handleRegister&&l.handleRegister(...e))},null,544),[[e.vModelText,i.registerForm.characterName]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"input-label"},"性别"),e.createElementVNode("view",{class:"gender-options"},[e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:"男"===i.registerForm.gender}]),onClick:a[10]||(a[10]=e=>l.selectGender("男"))},[e.createElementVNode("text",{class:"gender-text"},"男")],2),e.createElementVNode("view",{class:e.normalizeClass(["gender-option",{active:"女"===i.registerForm.gender}]),onClick:a[11]||(a[11]=e=>l.selectGender("女"))},[e.createElementVNode("text",{class:"gender-text"},"女")],2)])]),i.registerForm.characterName?(e.openBlock(),e.createElementBlock("view",{key:0,class:"character-preview"},[e.createElementVNode("text",{class:"preview-label"},"角色预览"),e.createElementVNode("view",{class:"character-card"},[e.createElementVNode("text",{class:"character-name"},e.toDisplayString(i.registerForm.characterName),1),e.createElementVNode("text",{class:"character-level"},"Lv.1 新手侠客")])])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"register-btn primary-btn",onClick:a[12]||(a[12]=(...e)=>l.handleRegister&&l.handleRegister(...e)),disabled:!l.isFormValid},[e.createElementVNode("text",{class:"btn-text"},"创建角色")],8,["disabled"]),e.createElementVNode("view",{class:"form-footer"},[e.createElementVNode("text",{class:"footer-text"},"已有账号？"),e.createElementVNode("text",{class:"footer-link",onClick:a[13]||(a[13]=(...e)=>l.switchToLogin&&l.switchToLogin(...e))},"立即登录")]),e.createElementVNode("view",{class:"talent-desc",style:{"margin-top":"30rpx",color:"#888","font-size":"24rpx","line-height":"1.7"}},[e.createTextVNode(" 角色天赋说明："),e.createElementVNode("br"),e.createTextVNode(" - 力量、悟性、身法、根骨四项天赋，初始总和100，每项最低15，注册时系统自动随机分配。"),e.createElementVNode("br"),e.createTextVNode(" - 富源属性：初始为1，影响角色的机遇和资源获取。"),e.createElementVNode("br"),e.createTextVNode(" - 天赋数值由服务器随机生成，确保公平性。 ")])])):e.createCommentVNode("",!0),i.isLoading?(e.openBlock(),e.createElementBlock("view",{key:2,class:"loading-overlay"},[e.createElementVNode("view",{class:"loading-content"},[e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(i.loadingText),1)])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-b8f6f970"]]);const l=new class{constructor(){this.player=null,this.inventory=[],this.equipment={},this.skills=[],this.eventLog=[],this.money=0,this.gold=0,this.status="normal",this.itemsConfig={},this.mapsConfig={},this.updateCallbacks=[],this.isAuthed=!1,this.initWebSocketHandlers()}initWebSocketHandlers(){a.on("player_data",(e=>{e&&"object"==typeof e?(e.data&&"object"==typeof e.data?this.player=e.data:this.player=e,this.notifyUpdate("player")):uni.showToast({title:"玩家数据格式错误",icon:"none"})})),a.on("inventory_data",(e=>{e&&e.inventory&&Array.isArray(e.inventory)?this.inventory=e.inventory:Array.isArray(e)?this.inventory=e:e&&"object"==typeof e?e.data&&Array.isArray(e.data)?this.inventory=e.data:(uni.showToast({title:"背包数据格式无法识别",icon:"none"}),this.inventory=[]):(uni.showToast({title:"背包数据格式错误",icon:"none"}),this.inventory=[]),this.notifyUpdate("inventory")})),a.on("equipment_data",(e=>{e&&"object"==typeof e?e.data&&"object"==typeof e.data?this.equipment=e.data:this.equipment=e:(uni.showToast({title:"装备数据格式错误",icon:"none"}),this.equipment={}),this.notifyUpdate("equipment")})),a.on("skills_data",(e=>{e&&Array.isArray(e)?this.skills=e:e&&"object"==typeof e&&e.data&&Array.isArray(e.data)?this.skills=e.data:(uni.showToast({title:"武功数据格式错误",icon:"none"}),this.skills=[]),this.notifyUpdate("skills")})),a.on("event_log",(e=>{e&&Array.isArray(e)?this.eventLog=e:e&&"object"==typeof e&&e.data&&Array.isArray(e.data)?this.eventLog=e.data:(uni.showToast({title:"事件日志格式错误",icon:"none"}),this.eventLog=[]),this.notifyUpdate("eventLog")})),a.on("currency_update",(e=>{e&&"object"==typeof e?(this.money=e.silver||e.money||this.money||0,this.gold=e.gold||this.gold||0,this.notifyUpdate("currency")):uni.showToast({title:"货币数据格式错误",icon:"none"})})),a.on("status_update",(e=>{e&&"object"==typeof e?(this.status=e.status||this.status||"normal",this.notifyUpdate("status")):uni.showToast({title:"状态数据格式错误",icon:"none"})})),a.on("energy_update",(e=>{e&&"object"==typeof e&&this.player?(this.player.energy=e.energy||this.player.energy||0,this.player.max_energy=e.max_energy||this.player.max_energy||0,this.player.energy_regen_rate=e.energy_regen_rate||this.player.energy_regen_rate||0,this.player.energy_regen_details=e.energy_regen_details||null,this.notifyUpdate("player")):uni.showToast({title:"体力数据格式错误或玩家数据不存在",icon:"none"})})),a.on("auth_success",(e=>{if(this.isAuthed=!0,e&&"object"==typeof e){if(e.player&&"object"==typeof e.player&&(this.player=e.player),e.userInfo&&"object"==typeof e.userInfo){this.player||(this.player={});const t=e.userInfo;this.player.username=t.username||this.player.username,this.player.characterName=t.characterName||this.player.characterName,this.player.userId=t.userId||this.player.userId,this.player.gender=t.gender||this.player.gender,t.talent&&"object"==typeof t.talent&&(this.player.talent=t.talent)}void 0!==e.money&&(this.money=e.money),void 0!==e.silver&&(this.money=e.silver),void 0!==e.gold&&(this.gold=e.gold)}this.notifyUpdate("player"),this.notifyUpdate("currency"),this.notifyUpdate("status"),this.notifyUpdate("auth"),setTimeout((()=>{this.requestAllData()}),500)})),a.on("auth_failed",(e=>{this.isAuthed=!1,this.notifyUpdate("auth")})),a.on("login_success",(e=>{if(this.isAuthed=!0,e&&"object"==typeof e){if(e.player&&"object"==typeof e.player&&(this.player=e.player),e.userInfo&&"object"==typeof e.userInfo){this.player||(this.player={});const t=e.userInfo;this.player.username=t.username||this.player.username,this.player.characterName=t.characterName||this.player.characterName,this.player.userId=t.userId||this.player.userId,this.player.gender=t.gender||this.player.gender,t.talent&&"object"==typeof t.talent&&(this.player.talent=t.talent)}void 0!==e.money&&(this.money=e.money),void 0!==e.silver&&(this.money=e.silver),void 0!==e.gold&&(this.gold=e.gold)}this.notifyUpdate("player"),this.notifyUpdate("currency"),this.notifyUpdate("status"),this.notifyUpdate("auth"),setTimeout((()=>{this.requestAllData()}),500)})),a.on("error",(e=>{const a=e.message||"操作失败";a.includes("武功数据错误")||a.includes("list indices must be integers")||a.includes("dict object has no attribute")||a.includes("str object has no attribute")?t("error","at utils/gameState.js:290","内部错误（已过滤）:",a):uni.showToast({title:a,icon:"none"})})),a.on("success",(e=>{uni.showToast({title:e.message||"操作成功",icon:"success"})})),a.on("connected",(()=>{})),a.on("disconnected",(()=>{}))}handleGameEvent(e){const t=e.type||e.eventType||5,a=e.content||e.description||"你遇到了一个江湖事件";e.rewards;const s=e.realm_breakthrough||null,n={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:this.getEventTypeName(t),description:a};this.eventLog.unshift(n),this.eventLog.length>50&&(this.eventLog=this.eventLog.slice(0,50)),this.notifyUpdate("eventLog"),s&&setTimeout((()=>{uni.showModal({title:"境界突破",content:s.message,showCancel:!1,confirmText:"确定"})}),500)}getEventTypeName(e){return{1:"好运事件",2:"遭遇NPC",3:"采集事件",4:"普通事件",5:"奇遇事件",6:"恩怨事件",7:"组队事件",8:"商队事件",9:"江湖传闻",10:"天气事件",11:"神秘事件",12:"节日事件"}[e]||"江湖事件"}onUpdate(e){this.updateCallbacks.push(e)}offUpdate(e){const t=this.updateCallbacks.indexOf(e);t>-1&&this.updateCallbacks.splice(t,1)}notifyUpdate(e){this.updateCallbacks.forEach(((t,a)=>{try{t(e,this)}catch(s){}}))}async init(){try{await a.connect()}catch(e){t("error","at utils/gameState.js:437","游戏初始化失败:",e),uni.showToast({title:"连接服务器失败",icon:"none"})}}requestAllData(){a.isConnected?this.sendDataRequests():a.connect().then((()=>{this.sendDataRequests()})).catch((e=>{t("error","at utils/gameState.js:454","[gameState] WebSocket连接失败:",e)}))}sendDataRequests(){a.sendMessage("get_player_data"),setTimeout((()=>{a.sendMessage("get_inventory_data")}),200),setTimeout((()=>{const{gameUtils:e}=require("./gameData.js");e&&e.sendMessage?e.sendMessage({type:"get_equipment_data",data:{}}).then((e=>{"equipment_data"===e.type&&e.data&&(this.equipment=e.data,this.notifyUpdate("equipment"))})).catch((e=>{t("error","at utils/gameState.js:489","[gameState] 获取装备数据失败:",e)})):a.sendMessage("get_player_data")}),400),setTimeout((()=>{a.sendMessage("get_skills_data")}),600),setTimeout((()=>{this.requestMapsConfig()}),800),setTimeout((()=>{this.requestItemsConfig()}),1e3)}triggerAdventure(){a.isAuthed||this.isAuthed?a.isConnected?a.sendAdventureRequest():uni.showToast({title:"网络连接失败",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})}meditate(){this.isAuthed?a.sendMeditationRequest():uni.showToast({title:"请先登录",icon:"none"})}heal(){this.isAuthed?a.sendHealingRequest():uni.showToast({title:"请先登录",icon:"none"})}equipItem(e){this.isAuthed?a.sendEquipmentAction("equip",e):uni.showToast({title:"请先登录",icon:"none"})}unequipItem(e){this.isAuthed?a.sendEquipmentAction("unequip",e):uni.showToast({title:"请先登录",icon:"none"})}learnSkill(e){this.isAuthed?a.sendSkillAction("learn",e):uni.showToast({title:"请先登录",icon:"none"})}practiceSkill(e){this.isAuthed?a.sendSkillAction("practice",e):uni.showToast({title:"请先登录",icon:"none"})}buyItem(e,t=1,s=""){if(!this.isAuthed)return void uni.showToast({title:"请先登录",icon:"none"});const n={item_id:e,quantity:t};s&&(n.npc_id=s),a.sendShopAction("buy",n)}sellItem(e,t=1){this.isAuthed?a.sendShopAction("sell",{itemId:e,quantity:t}):uni.showToast({title:"请先登录",icon:"none"})}listItem(e,t,s=1){this.isAuthed?a.sendMarketAction("list",{itemId:e,price:t,quantity:s}):uni.showToast({title:"请先登录",icon:"none"})}buyMarketItem(e){this.isAuthed?a.sendMarketAction("buy",{itemId:e}):uni.showToast({title:"请先登录",icon:"none"})}cancelListing(e){this.isAuthed?a.sendMarketAction("cancel",{itemId:e}):uni.showToast({title:"请先登录",icon:"none"})}joinGuild(e){this.isAuthed?a.sendGuildAction("join",{guildId:e}):uni.showToast({title:"请先登录",icon:"none"})}acceptGuildTask(e){this.isAuthed?a.sendGuildAction("accept_task",{taskId:e}):uni.showToast({title:"请先登录",icon:"none"})}learnGuildSkill(e){this.isAuthed?a.sendGuildAction("learn_skill",{skillId:e}):uni.showToast({title:"请先登录",icon:"none"})}craftItem(e){this.isAuthed?a.sendCraftingAction("craft",{recipeId:e}):uni.showToast({title:"请先登录",icon:"none"})}getPlayer(){return this.player}getInventory(){return this.inventory}getEquipment(){return this.equipment}getSkills(){return this.skills}getEventLog(){return this.eventLog}getCurrency(){return{silver:this.money,gold:this.gold}}getStatus(){return this.status}disconnect(){a.disconnect()}getPlayerData(){return this.player}setPlayerData(e){this.player=e,this.notifyUpdate("player")}updateMoney(){this.player&&"number"==typeof this.player.money?this.money=this.player.money:this.money&&(this.money=this.money)}updateData(){this.myItems=[...this.inventory]}requestItemsConfig(){return new Promise(((e,t)=>{a.sendMessage("get_items_config",{}),a.on("items_config",(t=>{this.itemsConfig=t||{},this.notifyUpdate("itemsConfig"),e(this.itemsConfig)}))}))}async getItemsConfig(){return 0===Object.keys(this.itemsConfig).length&&await this.requestItemsConfig(),this.itemsConfig}requestMapsConfig(){return new Promise(((e,t)=>{a.sendMessage("get_maps_config",{}),a.on("maps_config",(t=>{this.mapsConfig={};const a=t.data||t;if(Array.isArray(a))for(const e of a)e.id&&(this.mapsConfig[e.id]=e);else if("object"==typeof a&&null!==a)for(const[e,s]of Object.entries(a))s&&s.id&&(this.mapsConfig[s.id]=s);this.notifyUpdate("mapsConfig"),e(this.mapsConfig)}))}))}async getMapsConfig(){return 0===Object.keys(this.mapsConfig).length&&await this.requestMapsConfig(),this.mapsConfig}};const o=n({components:{GatheringPopup:n({name:"GatheringPopup",props:{visible:Boolean,event:Object,times:Number,result:String,inventory:Array},data:()=>({toolNameMap:{sickle:"镰刀",axe:"斧头",pickaxe:"矿镐",knife:"小刀",hoe:"锄头",fishing_rod:"鱼竿",net:"捕网"}}),computed:{hasRequiredTool(){var e,a,s;if(t("log","at components/GatheringPopup.vue:48","检查工具:",{event:this.event,requiredTool:null==(e=this.event)?void 0:e.requiredTool,resourceLevel:null==(a=this.event)?void 0:a.resourceLevel,inventory:this.inventory,inventoryLength:null==(s=this.inventory)?void 0:s.length}),!this.event||!this.event.requiredTool||!this.inventory)return t("log","at components/GatheringPopup.vue:57","工具检查失败: 缺少必要数据"),!1;const n=this.event.requiredTool,i=this.event.resourceLevel||1,l={"镰刀":"sickle","斧头":"axe","矿镐":"pickaxe","小刀":"knife",sickle:"sickle",axe:"axe",pickaxe:"pickaxe",knife:"knife"};t("log","at components/GatheringPopup.vue:77","=== GatheringPopup 工具检查详细调试 ==="),t("log","at components/GatheringPopup.vue:78","需要的工具:",n),t("log","at components/GatheringPopup.vue:79","需要的等级:",i),t("log","at components/GatheringPopup.vue:80","工具类型映射:",l),t("log","at components/GatheringPopup.vue:81","需要的类型:",l[n]),t("log","at components/GatheringPopup.vue:82","背包物品数量:",this.inventory.length),this.inventory.forEach(((e,a)=>{t("log","at components/GatheringPopup.vue:86",`背包物品[${a}]:`,{id:e.id,name:e.name,type:e.type,level:e.level,quantity:e.quantity})}));const o=this.inventory.filter((e=>{if(e.name===n||e.id===n)return!0;const t=l[n];return!(!t||e.type!==t)||!(!e.name||!e.name.includes(n))}));t("log","at components/GatheringPopup.vue:116","找到的匹配工具:",o);const c=o.filter((e=>{const a=parseInt(e.level||1),s=a>=i;return t("log","at components/GatheringPopup.vue:122",`工具 ${e.name} 等级${a} ${s?"✅":"❌"} 需要等级${i}`),s}));t("log","at components/GatheringPopup.vue:126","等级足够的工具:",c);const r=c.length>0;return t("log","at components/GatheringPopup.vue:129","工具检查结果:",r),r}},methods:{onClose(){t("log","at components/GatheringPopup.vue:135","🚪 GatheringPopup.onClose 被调用"),console.trace("调用栈:"),this.$emit("close")},onGather(){t("log","at components/GatheringPopup.vue:140","🎯 GatheringPopup.onGather 被调用"),this.$emit("do-gather")}}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"gathering-popup-mask",onClick:a[2]||(a[2]=e.withModifiers(((...e)=>l.onClose&&l.onClose(...e)),["self"]))},[e.createElementVNode("view",{class:"gathering-popup"},[e.createElementVNode("view",{class:"gather-title"},"采集事件"),e.createElementVNode("view",{class:"gather-desc"},e.toDisplayString(s.event.content),1),e.createElementVNode("view",{class:"gather-info"},[e.createElementVNode("text",null,"需要工具："),e.createElementVNode("text",{class:"tool-type"},e.toDisplayString(s.event.requiredToolDesc||s.event.toolName||i.toolNameMap[s.event.requiredTool]||s.event.requiredTool),1)]),e.createElementVNode("view",{class:"gather-info"},[e.createElementVNode("text",null,"可采集次数："),e.createElementVNode("text",{class:"gather-times"},e.toDisplayString(s.times),1)]),s.result?(e.openBlock(),e.createElementBlock("view",{key:0,class:"gather-result"},e.toDisplayString(s.result),1)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"gather-btns"},[e.createElementVNode("button",{class:"gather-btn",type:"primary",onClick:a[0]||(a[0]=e.withModifiers(((...e)=>l.onGather&&l.onGather(...e)),["stop"])),disabled:!l.hasRequiredTool||s.times<=0},"采集",8,["disabled"]),e.createElementVNode("button",{class:"close-btn",onClick:a[1]||(a[1]=e.withModifiers(((...e)=>l.onClose&&l.onClose(...e)),["stop"]))},"关闭")])])])}],["__scopeId","data-v-bdf718bb"]]),BattlePopup:n({name:"BattlePopup",props:{visible:Boolean,battleLog:Array,player:Object,monster:Object,attackMode:{type:String,default:"active"},battleStage:{type:String,default:"battle"}},data:()=>({defaultPlayerAvatar:"/static/npc/default.png",defaultMonsterAvatar:"/static/npc/default.png",debug:!1,scrollTop:0,scrollIntoView:""}),computed:{playerHpPercent(){return this.player&&this.player.hp&&this.player.max_hp?this.player.hp/this.player.max_hp*100:100},playerMpPercent(){return this.player&&this.player.mp&&this.player.max_mp?this.player.mp/this.player.max_mp*100:100},monsterHpPercent(){return this.monster&&this.monster.hp&&this.monster.max_hp?this.monster.hp/this.monster.max_hp*100:100},processedBattleLog(){return this.battleLog&&Array.isArray(this.battleLog)?this.battleLog.map(((e,t)=>{const a=this.getCurrentTime(),s=this.formatBattleDesc(e.desc),n=e.effect_desc&&""!==e.effect_desc?this.formatBattleDesc(e.effect_desc):null;return{...e,defaultTime:a,formattedDesc:s,formattedEffectDesc:n}})):[]}},watch:{battleLog:{handler(e){this.scrollToBottom()},deep:!0}},mounted(){},methods:{onClose(){this.$emit("close")},onNext(){this.$emit("next")},onAttack(){this.$emit("attack")},onEscape(){this.$emit("escape")},formatBattleDesc(e){if(!e||""===e)return"";let t=e.replace(/【([^】]+)】/g,'<span style="color: #e74c3c; font-weight: bold; text-shadow: 0 0 2px rgba(231, 76, 60, 0.3);">【$1】</span>');return t=t.replace(/(\d+)(?=点伤害)/g,'<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>'),t=t.replace(/(\d+)(?=点)/g,'<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>'),t=t.replace(/(眩晕|中毒|流血)/g,'<span style="color: #9b59b6; font-weight: bold; text-shadow: 0 0 2px rgba(155, 89, 182, 0.3);">$1</span>'),t},scrollToBottom(){this.$nextTick((()=>{this.scrollIntoView="battle-log-bottom",setTimeout((()=>{this.scrollIntoView=""}),100)}))},onScroll(e){this.scrollTop=e.detail.scrollTop},getCurrentTime(){const e=new Date;return`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}`}}},[["render",function(t,a,s,n,i,l){return s.visible?(e.openBlock(),e.createElementBlock("view",{key:0,class:"battle-popup-mask"},[e.createElementVNode("view",{class:"battle-popup-content"},[e.createElementVNode("view",{class:"battle-title"},"战斗中"),e.createElementVNode("view",{class:"battle-roles"},[e.createElementVNode("view",{class:"role player"},[e.createElementVNode("image",{class:"role-avatar",src:s.player.avatar||i.defaultPlayerAvatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"role-name"},e.toDisplayString(s.player.name||"玩家"),1),e.createElementVNode("view",{class:"role-hp-bar"},[e.createElementVNode("text",null,"气血"),e.createElementVNode("progress",{percent:l.playerHpPercent,"stroke-width":"8",activeColor:"#e74c3c"},null,8,["percent"]),e.createElementVNode("text",null,e.toDisplayString(Math.floor(s.player.hp))+"/"+e.toDisplayString(Math.floor(s.player.max_hp)),1)]),e.createElementVNode("view",{class:"role-mp-bar"},[e.createElementVNode("text",null,"内力"),e.createElementVNode("progress",{percent:l.playerMpPercent,"stroke-width":"8",activeColor:"#3498db"},null,8,["percent"]),e.createElementVNode("text",null,e.toDisplayString(s.player.mp)+"/"+e.toDisplayString(s.player.max_mp),1)])]),e.createElementVNode("view",{class:"role monster"},[e.createElementVNode("image",{class:"role-avatar",src:s.monster.avatar||i.defaultMonsterAvatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"role-name"},e.toDisplayString(s.monster.name||"怪物"),1),e.createElementVNode("view",{class:"role-hp-bar"},[e.createElementVNode("text",null,"气血"),e.createElementVNode("progress",{percent:l.monsterHpPercent,"stroke-width":"8",activeColor:"#e67e22"},null,8,["percent"]),e.createElementVNode("text",null,e.toDisplayString(Math.floor(s.monster.hp))+"/"+e.toDisplayString(Math.floor(s.monster.max_hp)),1)])])]),e.createElementVNode("scroll-view",{class:"battle-log","scroll-y":"true","scroll-with-animation":"true","show-scrollbar":"true","scroll-top":i.scrollTop,"scroll-into-view":i.scrollIntoView,onScroll:a[0]||(a[0]=(...e)=>l.onScroll&&l.onScroll(...e))},[s.battleLog&&s.battleLog.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"battle-log-content"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.processedBattleLog,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:`round-${a}`,class:"battle-round"},[e.createElementVNode("view",{class:"round-header"},[e.createElementVNode("text",{class:"round-number"},"第"+e.toDisplayString(t.round||a+1)+"回合",1),e.createElementVNode("text",{class:"round-time"},e.toDisplayString(t.timestamp||t.defaultTime),1)]),e.createElementVNode("view",{class:"round-desc",innerHTML:t.formattedDesc},null,8,["innerHTML"]),t.formattedEffectDesc?(e.openBlock(),e.createElementBlock("view",{key:0,class:"effect-desc",innerHTML:t.formattedEffectDesc},null,8,["innerHTML"])):e.createCommentVNode("",!0)])))),128)),e.createElementVNode("view",{id:"battle-log-bottom",class:"battle-log-bottom"})])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"battle-log-placeholder"},[e.createElementVNode("text",null,"等待战斗开始...")]))],40,["scroll-top","scroll-into-view"]),e.createElementVNode("view",{class:"battle-popup-buttons"},["encounter"===s.battleStage&&"passive"===s.attackMode?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createElementVNode("button",{class:"main-btn",onClick:a[1]||(a[1]=e.withModifiers((e=>t.$emit("attack")),["stop"]))},"攻击"),e.createElementVNode("button",{class:"sub-btn",onClick:a[2]||(a[2]=e.withModifiers((e=>t.$emit("escape")),["stop"]))},"逃跑")],64)):"encounter"===s.battleStage&&"active"===s.attackMode?(e.openBlock(),e.createElementBlock("button",{key:1,class:"sub-btn",onClick:a[3]||(a[3]=e.withModifiers((e=>t.$emit("escape")),["stop"]))},"逃离")):"battle"===s.battleStage?(e.openBlock(),e.createElementBlock("button",{key:2,class:"main-btn",onClick:a[4]||(a[4]=e.withModifiers((e=>t.$emit("escape")),["stop"]))},"逃跑")):"end"===s.battleStage?(e.openBlock(),e.createElementBlock("button",{key:3,class:"main-btn",onClick:a[5]||(a[5]=e.withModifiers((e=>t.$emit("close")),["stop"]))},"关闭")):e.createCommentVNode("",!0)])])])):e.createCommentVNode("",!0)}],["__scopeId","data-v-a5da29ee"]])},data:()=>({player:{},money:0,gold:0,status:"normal",eventLog:[],requiredExp:0,connectionStatus:"未连接",isAuthed:!1,showGatheringPopup:!1,gatheringEvent:null,gatheringTimes:0,gatheringResult:"",lastGatheringResultTime:0,currentMap:null,showMapPopup:!1,announcementText:"欢迎来到仗剑江湖行！系统运行正常，祝您游戏愉快！新版本已上线，新增多种武功秘籍和装备道具，快来体验吧！",originalAnnouncement:null,mapNpcs:[],showNpcMenuModal:!1,selectedNpc:{},mapsConfig:{},showBuyModal:!1,buyItem:{},buyQuantity:1,buyNpcName:"",showBattlePopup:!1,battleLog:[],showRankingModal:!1,rankingCurrentTab:"wealth",rankingLoading:!1,rankingList:[],myRanking:null,showRedeemModal:!1,redeemCode:"",redeemLoading:!1,showRankingModal:!1,rankingCurrentTab:"wealth",rankingLoading:!1,rankingList:[],myRanking:null,battlePlayer:{},battleMonster:{},battleStage:"",battleAttackMode:"",healingMeditationMessages:[],healingMeditationLoading:!1,npcSidebarVisible:!1,npcBtnTop:"70%",npcBtnDragging:!1,npcBtnStartY:0,npcBtnStartTop:0,playerSidebarVisible:!1,playerBtnTop:"60%",playerBtnDragging:!1,playerBtnStartY:0,playerBtnStartTop:0,mapPlayers:[],showPlayerMenuModal:!1,selectedPlayer:{},showChatPopup:!1,chatMessages:[],chatInputText:"",chatTargetName:"",currentChatType:"world",chatTypeIndex:0,chatTypes:[{value:"world",name:"世界"},{value:"private",name:"私聊"},{value:"sect",name:"门派"},{value:"rumor",name:"谣言"}],leftMenuVisible:!1,leftBtnTop:"70%",leftBtnDragging:!1,leftBtnStartY:0,leftBtnStartTop:0,fetchingBonusSummary:!1,fetchingMapNpcs:!1,fetchingMapPlayers:!1,isInitializing:!1,chatBtnTop:"55%",chatBtnDragging:!1,chatBtnStartY:0,chatBtnStartTop:0,lastBattleHpUpdate:0}),computed:{currentInventory(){const e=l.inventory||[];return t("log","at pages/index/index.vue:668","当前背包数据:",e),e},chatTypeNames(){return this.chatTypes.map((e=>e.name))},hpPercent(){return(this.player.hp||0)/(this.player.max_hp||this.player.base_max_hp||100)*100},mpPercent(){return this.player.mp&&(this.player.max_mp||this.player.base_max_mp)?this.player.mp/(this.player.max_mp||this.player.base_max_mp)*100:100},staminaPercent(){return this.player.energy&&(this.player.max_energy||this.player.base_max_energy)?this.player.energy/(this.player.max_energy||this.player.base_max_energy)*100:100},energyPercent(){return this.player.spirit&&this.player.max_spirit?this.player.spirit/this.player.max_spirit*100:100},expPercent(){return this.player.exp&&this.requiredExp?this.player.exp/this.requiredExp*100:0},connectionStatusClass(){const e=this.connectionStatus;return"已连接"===e?"connected":"disconnected"},currentMapName(){const e=this.player&&this.player.current_map,t=this.mapsConfig&&e?this.mapsConfig[e]:null;return t?t.名称||t.name:"未知"},mapList(){return this.mapsConfig?Object.values(this.mapsConfig).map((e=>{const t={...e};return Array.isArray(t.NPC)||("string"==typeof t.NPC?t.NPC=t.NPC.split(",").map((e=>({"名称":e.trim()}))):t.NPC=[]),Array.isArray(t.怪物)||("string"==typeof t.怪物?t.怪物=t.怪物.split(",").map((e=>({"名称":e.trim()}))):t.怪物=[]),Array.isArray(t.monsters)||("string"==typeof t.monsters?t.monsters=t.monsters.split(",").map((e=>({name:e.trim()}))):t.monsters=[]),Array.isArray(t.采集物品)||("string"==typeof t.采集物品?t.采集物品=t.采集物品.split(",").map((e=>{const[t,a]=e.split(":");return{"物品":t.trim(),"概率":a?parseFloat(a):void 0}})):t.采集物品=[]),Array.isArray(t.gather_items)||("string"==typeof t.gather_items?t.gather_items=t.gather_items.split(",").map((e=>{const[t,a]=e.split(":");return{item:t.trim(),prob:a?parseFloat(a):void 0}})):t.gather_items=[]),t})):[]},currentMapNpcsLocal(){const e=this.player&&this.player.current_map,t=this.mapsConfig&&this.mapsConfig[e];return t&&Array.isArray(t.NPC)?t.NPC.map(((e,t)=>({id:`npc_${t}`,name:e,avatar:"static/npc/default.png",desc:`${e}：一位神秘的江湖人物。`,functions:[{key:"talk",label:"对话"},{key:"shop",label:"交易"}]}))):[]},displayMapNpcs(){return this.mapNpcs&&this.mapNpcs.length>0?this.mapNpcs:this.currentMapNpcsLocal},getStrengthBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.bonus_percentage)||0},getIntelligenceBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.intelligence)?void 0:a.bonus_percentage)||0},getAgilityDefenseBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.defense_bonus_percentage)||0},getConstitutionBonus:()=>0,getConstitutionHpBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.constitution)?void 0:a.hp_bonus_percentage)||0},get hasTalentBonuses(){var e,t,a,s,n,i,l,o,c,r,d,m;return((null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.bonus_percentage)||0)>0||((null==(i=null==(n=null==(s=this.player)?void 0:s.talent_bonuses)?void 0:n.intelligence)?void 0:i.bonus_percentage)||0)>0||((null==(c=null==(o=null==(l=this.player)?void 0:l.talent_bonuses)?void 0:o.agility)?void 0:c.defense_bonus_percentage)||0)>0||((null==(m=null==(d=null==(r=this.player)?void 0:r.talent_bonuses)?void 0:d.constitution)?void 0:m.hp_bonus_percentage)||0)>0},canBuy(){const e=(this.buyItem.price||0)*this.buyQuantity,t=this.money>=e,a=(this.buyItem.stock||0)>=this.buyQuantity,s=this.buyQuantity>0;return t&&a&&s}},async onLoad(){await this.loadMapsConfig(),this.checkLoginAndInit(),"function"==typeof l.onUpdate&&l.onUpdate(this.handleStateUpdate),this.updateData(),a.on("announcement",(e=>{e&&e.content&&(this.announcementText=e.content)})),a.on("system_announcement",(e=>{e&&e.message&&(this.originalAnnouncement||(this.originalAnnouncement=this.announcementText),this.announcementText=e.message,setTimeout((()=>{this.originalAnnouncement&&(this.announcementText=this.originalAnnouncement)}),6e4))})),a.on("game_event",this.handleGameEvent),this.fetchBonusSummary(),setTimeout((()=>{this.fetchMapNpcs()}),2e3),a.on("encounter_monster",this.handleEncounterMonster),a.on("battle_round",this.handleBattleRound),a.on("battle_result",this.handleBattleResult),a.on("player_data",this.handleBattlePlayerData),a.on("escape_battle_result",this.handleEscapeBattleResult),t("log","at pages/index/index.vue:864","📡 采集结果将通过 Promise 处理，避免重复调用")},onReady(){},onShow(){this.isInitializing||(this.isInitializing=!0,a.isConnected?!a.isAuthed&&a.autoAuthenticate?(a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData(),this.isInitializing=!1}),500)):(this.updateConnectionStatus&&this.updateConnectionStatus(),this.isInitializing=!1):a.connect().then((()=>{!a.isAuthed&&a.autoAuthenticate&&a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData(),this.isInitializing=!1}),500)})),this.player&&this.player.current_map&&!this.fetchingMapPlayers&&this.fetchMapPlayers())},onTabItemTap(e){e.index},onUnload(){l.offUpdate(this.handleStateUpdate),a.off("game_event",this.handleGameEvent),a.off("select_map_success",this.onSelectMapSuccess),a.off("error",this.onMapError)},created(){},watch:{"player.current_map":{handler(e,t){e&&e!==t&&this.mapList&&this.mapList.length&&this.fetchMapNpcs()},immediate:!1},mapList:{handler(e,t){l.player&&l.player.current_map&&e&&e.length&&e!==t&&this.fetchMapNpcs()},immediate:!1}},methods:{checkLoginAndInit(){const e=uni.getStorageSync("token"),t=uni.getStorageSync("userInfo");e&&t?this.initGame():uni.reLaunch({url:"/pages/login/login"})},async initGame(){try{l.onUpdate(this.handleStateUpdate),await l.init(),this.updateConnectionStatus(),this.updateData()}catch(e){this.connectionStatus="连接失败",uni.showToast({title:"游戏初始化失败: "+e.message,icon:"none",duration:3e3})}},updateConnectionStatus(){this.connectionStatus=a.isConnected?"已连接":"未连接",a.isConnected||(this.isAuthed=!1)},handleStateUpdate(e,t){switch(e){case"player":if(t.player){const e=this.player.hp,a=this.player.max_hp,s=this.showBattlePopup&&"battle"===this.battleStage;this.player={...t.player},s&&(null!=e&&(this.player.hp=e),null!=a&&(this.player.max_hp=a))}break;case"currency":this.money=t.money,this.gold=t.gold;break;case"status":this.status=t.status;break;case"eventLog":this.eventLog=t.eventLog.map(((e,t)=>0===t?{...e,displayText:""}:{...e,displayText:e.description})),this.eventLog.length>0&&this.typeWriterEffect(this.eventLog[0],0);break;case"auth":this.isAuthed=t.isAuthed||!1,this.isAuthed;break;default:this.updateData()}"auth"!==e&&(this.isAuthed=a.isAuthed||t.isAuthed||!1)},updateData(){l.getPlayerData&&"function"==typeof l.getPlayerData?this.player=l.getPlayerData():l.player&&(this.player={...l.player}),this.player?(this.mapList&&this.mapList.length&&this.player.current_map&&(this.currentMap=this.mapList.find((e=>e.id===this.player.current_map))||null),this.money=this.player.money||this.player.silver||l.money||0,this.gold=this.player.gold||l.gold||0):(this.money=l.money||0,this.gold=l.gold||0),this.status=l.status||"normal",this.eventLog=[...l.eventLog||[]],this.fetchBonusSummary(),this.fetchMapNpcs()},formatNumber:e=>s.formatNumber(e),getStatusText(){return{injured:"重伤",internal_injury:"内伤",poisoned:"中毒",tired:"疲劳"}[this.status]||"正常"},triggerAdventure(){this.isAuthed?a.isConnected?l.triggerAdventure():uni.showToast({title:"网络连接失败",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})},clearLog(){uni.showModal({title:"确认清空",content:"确定要清空江湖日志吗？",success:e=>{e.confirm&&(l.eventLog=[],this.eventLog=[],l.save())}})},navigateTo(e){uni.navigateTo({url:e})},testConnection(){const e=uni.getStorageSync("token"),t=uni.getStorageSync("userInfo"),s=`WebSocket连接: ${a.isConnected?"已连接":"未连接"}\n服务器地址: ${a.serverUrl}\nWebSocket认证: ${a.isAuthed?"已认证":"未认证"}\n页面认证: ${this.isAuthed?"已认证":"未认证"}\nGameState认证: ${l.isAuthed?"已认证":"未认证"}\n本地token: ${e?"存在":"不存在"}\n本地userInfo: ${t?"存在":"不存在"}\n玩家数据: ${l.player?"存在":"不存在"}\n连接状态: ${this.connectionStatus}`;uni.showModal({title:"连接状态详情",content:s,showCancel:!0,cancelText:"手动认证",confirmText:"确定",success:e=>{e.cancel&&a.autoAuthenticate()}}),a.isConnected||a.connect().then((()=>{this.updateConnectionStatus()})).catch((e=>{}))},getGatherToolInfo(e,a=1){t("log","at pages/index/index.vue:1173","查找工具:",e,"需要等级:",a);const s={"镰刀":"sickle","斧头":"axe","矿镐":"pickaxe","小刀":"knife",sickle:"sickle",axe:"axe",pickaxe:"pickaxe",knife:"knife"},n=a=>Array.isArray(a)?a.filter((a=>{if(!a)return!1;if(a.name===e||a.id===e)return t("log","at pages/index/index.vue:1196","找到直接匹配工具:",a),!0;const n=s[e];return n&&a.type===n?(t("log","at pages/index/index.vue:1203","找到类型匹配工具:",a,"需要类型:",n),!0):!(!a.name||!a.name.includes(e))&&(t("log","at pages/index/index.vue:1209","找到名称包含匹配工具:",a),!0)})):[];let i=[];if(l.equipment&&(i=i.concat(n(Object.values(l.equipment)))),l.inventory&&(i=i.concat(n(l.inventory))),0===i.length)return t("log","at pages/index/index.vue:1231","未找到匹配工具"),null;const o=i.filter((e=>{const s=parseInt(e.level||1),n=s>=a;return t("log","at pages/index/index.vue:1239",`工具 ${e.name} 等级${s} ${n?"✅":"❌"} 需要等级${a}`),n}));if(0===o.length)return t("log","at pages/index/index.vue:1244","没有等级足够的工具"),null;const c=o.reduce(((e,t)=>{const a=parseInt(e.level||1);return parseInt(t.level||1)>a?t:e}));return t("log","at pages/index/index.vue:1255","选择最高级工具:",c,"等级:",c.level),c},getGatherTimesByTool:e=>e?e.gather_times||1:0,handleGameEvent(e){t("log","at pages/index/index.vue:1267","处理游戏事件:",e);const s=e.requiredTool||e.toolType||e.tool||e.gatherTool||"",n=e.gatherType||e.type||"",i=!!(e.requiredTool||e.toolType||e.tool||e.gatherTool),o="gathering"===e.eventType||"gathering"===e.type||3===e.type||"gathering"===n||"gather"===n||/(采集点|可以采集|发现资源|发现矿脉|发现草药|资源地.*可以采集|可采集)/.test(e.content)||i,c=/采集到|获得了|挖到了|收获了/.test(e.content);if(t("log","at pages/index/index.vue:1282","🔍 事件判断:",{isGatherPoint:o,isGatherResult:c,toolField:s,gatherType:n}),o&&!c){t("log","at pages/index/index.vue:1285","✅ 确认为采集点事件，开始处理");const i={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"采集事件",description:e.content||"你发现了可采集的资源。",displayText:e.content||"你发现了可采集的资源。"};l.eventLog.unshift(i),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),l.notifyUpdate("eventLog"),t("log","at pages/index/index.vue:1300","🔧 准备采集数据，toolField:",s),t("log","at pages/index/index.vue:1301","🔧 事件数据:",e),t("log","at pages/index/index.vue:1302","🔧 资源名称:",e.resource,e.fixedResource);const o=e.resourceLevel||1;t("log","at pages/index/index.vue:1304","🔧 采集品等级要求:",o);const c=this.getGatherToolInfo(s||"hoe",o);t("log","at pages/index/index.vue:1306","🔧 找到的工具:",c);const r=this.getGatherTimesByTool(c);t("log","at pages/index/index.vue:1308","🔧 计算的采集次数:",r);const d=c?c.name:s||"采集工具",m=e.requiredToolDesc||`${s}(${o}级以上)`;this.gatheringEvent={content:e.content||"你发现了可采集的资源。",gatherType:n,requiredTool:s||"hoe",requiredToolDesc:m,resourceLevel:o,gatherTimes:r,toolName:d,resource:e.resource||e.fixedResource},this.gatheringTimes=r,t("log","at pages/index/index.vue:1322","🔧 设置采集次数:",this.gatheringTimes),this.gatheringResult="",a.sendMessage("get_inventory_data"),this.typeWriterEffect(i,0,(()=>{setTimeout((()=>{this.showGatheringPopup=!0}),500)}))}else c?t("log","at pages/index/index.vue:1337","采集结果事件，应该由 handleGatheringResult 处理"):l&&"function"==typeof l.handleGameEvent&&l.handleGameEvent({type:e.type||e.eventType,content:e.content,rewards:e.rewards||{}})},doGather(){if(t("log","at pages/index/index.vue:1352","🎯 doGather 方法被调用"),t("log","at pages/index/index.vue:1353","开始采集，检查工具..."),t("log","at pages/index/index.vue:1354","当前采集事件:",this.gatheringEvent),!this.gatheringEvent)return t("error","at pages/index/index.vue:1358","采集事件不存在"),void(this.gatheringResult="采集事件异常，请重新触发！");const e=this.gatheringEvent,a=this;setTimeout((()=>{const n=e.requiredTool,i=e.resourceLevel||1,o=a.getGatherToolInfo(n,i);if(t("log","at pages/index/index.vue:1375","采集工具检查:",{requiredTool:n,resourceLevel:i,tool:o,inventory:l.inventory}),t("log","at pages/index/index.vue:1376","当前采集次数:",a.gatheringTimes),!o){t("log","at pages/index/index.vue:1379","❌ 没有找到合适等级的工具，停止采集");const s=e.requiredToolDesc||`${n}(${i}级以上)`;return void(a.gatheringResult=`需要${s}才能采集！`)}if(a.gatheringTimes<=0)return t("log","at pages/index/index.vue:1385","❌ 采集次数用完，停止采集"),void(a.gatheringResult="本次采集已完成！");t("log","at pages/index/index.vue:1389","✅ 工具检查通过，准备发送采集请求");const c={type:"gather_action",data:{gatherType:e.gatherType,fixedResource:e.resource}};t("log","at pages/index/index.vue:1398","🚀 发送采集请求:",c),t("log","at pages/index/index.vue:1399","🚀 gatheringEvent.resource:",e.resource),s.sendMessage(c).then((e=>{if(t("log","at pages/index/index.vue:1405","🎯 收到采集响应:",e),e&&"gather_action_success"===e.type&&e.data){t("log","at pages/index/index.vue:1409","🎯 直接处理采集结果"),t("log","at pages/index/index.vue:1410","🎯 vm对象:",a),t("log","at pages/index/index.vue:1411","🎯 vm.handleGatheringResult方法:",a.handleGatheringResult);try{a.handleGatheringResult(e),t("log","at pages/index/index.vue:1415","✅ handleGatheringResult 调用完成")}catch(s){t("error","at pages/index/index.vue:1417","❌ 调用 handleGatheringResult 时出错:",s)}}else t("log","at pages/index/index.vue:1420","❌ 响应格式不正确:",e)})).catch((e=>{t("error","at pages/index/index.vue:1423","❌ 采集请求失败:",e),a.gatheringResult="采集失败，请重试！"})),a.gatheringTimes--,t("log","at pages/index/index.vue:1429","✅ 采集请求已发送，剩余次数:",a.gatheringTimes),a.gatheringTimes<=0&&(a.gatheringResult="采集中，请等待结果...")}),100)},handleGatheringResult(e){try{if(this.lastGatheringResultTime&&Date.now()-this.lastGatheringResultTime<1e3)return void t("log","at pages/index/index.vue:1442","🚫 防止重复处理采集结果，忽略此次调用");this.lastGatheringResultTime=Date.now(),t("log","at pages/index/index.vue:1447","🎯 handleGatheringResult 被调用，采用战斗结果相同的处理方式"),t("log","at pages/index/index.vue:1448","🎯 收到的数据:",e);let a=e.content;if(!a&&e.data&&e.data.content&&(a=e.data.content,t("log","at pages/index/index.vue:1455","🎯 使用 data.data.content:",a)),!a)return void t("error","at pages/index/index.vue:1459","❌ 没有找到采集结果内容");this.gatheringResult=a.replace(/\n/g,"\n"),t("log","at pages/index/index.vue:1465","✅ 采集弹窗内容已更新:",this.gatheringResult);const s={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"采集结果",description:a,displayText:a};t("log","at pages/index/index.vue:1474","🎯 准备添加到江湖日志:",s),t("log","at pages/index/index.vue:1475","🎯 当前 gameState.eventLog:",l.eventLog),l.eventLog.unshift(s),t("log","at pages/index/index.vue:1478","🎯 添加后的 gameState.eventLog:",l.eventLog),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),t("log","at pages/index/index.vue:1482",'🎯 调用 gameState.notifyUpdate("eventLog")'),l.notifyUpdate("eventLog"),t("log","at pages/index/index.vue:1484","✅ 江湖日志更新完成");let n=e.player_data;!n&&e.data&&e.data.player_data&&(n=e.data.player_data),n&&(n.inventory&&(l.inventory=n.inventory,l.notifyUpdate("inventory")),n.gather_skills&&(this.player.gather_skills=n.gather_skills)),this.gatheringTimes<=0&&setTimeout((()=>{this.closeGatheringPopup()}),2e3)}catch(a){t("error","at pages/index/index.vue:1510","❌ handleGatheringResult 执行出错:",a),t("error","at pages/index/index.vue:1511","❌ 错误堆栈:",a.stack)}},closeGatheringPopup(){if(t("log","at pages/index/index.vue:1516","🚪 closeGatheringPopup 被调用，当前次数:",this.gatheringTimes),this.gatheringTimes>0&&!this.gatheringResult&&this.gatheringEvent){const e={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"放弃采集",description:"你放弃了这次采集机会，离开了采集点。",displayText:"你放弃了这次采集机会，离开了采集点。"};l.eventLog.unshift(e),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),l.notifyUpdate("eventLog")}this.showGatheringPopup=!1,this.gatheringEvent=null,this.gatheringResult="",this.gatheringTimes=0},getStrengthBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.bonus_percentage)||0},getIntelligenceBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.intelligence)?void 0:a.bonus_percentage)||0},getAgilityDefenseBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.defense_bonus_percentage)||0},getConstitutionBonus:()=>0,getConstitutionHpBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.constitution)?void 0:a.hp_bonus_percentage)||0},get hasTalentBonuses(){var e,t,a,s,n,i,l,o,c,r,d,m;return((null==(a=null==(t=null==(e=this.player)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.bonus_percentage)||0)>0||((null==(i=null==(n=null==(s=this.player)?void 0:s.talent_bonuses)?void 0:n.intelligence)?void 0:i.bonus_percentage)||0)>0||((null==(c=null==(o=null==(l=this.player)?void 0:l.talent_bonuses)?void 0:o.agility)?void 0:c.defense_bonus_percentage)||0)>0||((null==(m=null==(d=null==(r=this.player)?void 0:r.talent_bonuses)?void 0:d.constitution)?void 0:m.hp_bonus_percentage)||0)>0},getGatherSkillInfo(e){const t=((l.player||{}).gather_skills||{})[e]||{level:1,exp:0},a=60*Math.pow(t.level+1,2);return{level:t.level,exp:t.exp,needExp:a,progress:a>0?(t.exp/a*100).toFixed(1):0}},getAllGatherSkills(){const e={mining:"挖矿",logging:"伐木",herbalism:"采药",skinning:"剥皮"};return["mining","logging","herbalism","skinning"].map((t=>({type:t,name:e[t],...this.getGatherSkillInfo(t)})))},async fetchBonusSummary(){if(!this.fetchingBonusSummary){this.fetchingBonusSummary=!0;try{const e=await s.sendMessage({type:"get_bonus_summary",data:{}});if("get_bonus_summary_timeout"===e.type)return;if("get_bonus_summary_success"===e.type||"bonus_summary"===e.type){const t=e.data||{};this.player.talent_bonuses||(this.player.talent_bonuses={}),t.strength&&(this.player.talent_bonuses.strength={bonus_percentage:t.strength.attack_bonus||0}),t.intelligence&&(this.player.talent_bonuses.intelligence={bonus_percentage:t.intelligence.exp_bonus||0}),t.agility&&(this.player.talent_bonuses.agility={defense_bonus_percentage:t.agility.defense_bonus||0}),t.constitution&&(this.player.talent_bonuses.constitution={hp_bonus_percentage:t.constitution.hp_bonus||0})}}catch(e){}finally{this.fetchingBonusSummary=!1}}},canEnterMap(e){const t=l.player||{},a=e.进入要求||e.enter_requirements||{};if(a.item){if(!(t.inventory||[]).some((e=>e.name===a.item)))return!1}return!(a.attack&&(t.attack||0)<a.attack)},async selectMap(e){if(e&&e.id)if(this.currentMap=e,this.canEnterMap(e))try{uni.showLoading({title:"正在切换地图...",mask:!0});const t=await s.sendMessage({type:"select_map",data:{map_id:e.id}});uni.hideLoading(),"select_map_success"===t.type?(this.currentMap=this.mapList.find((e=>e.id===t.data.map_id)),l.player&&(l.player.current_map=t.data.map_id,l.notifyUpdate("player")),this.refreshPlayerData(),this.showMapPopup=!1,uni.showToast({title:"切换成功",icon:"success"})):"error"===t.type&&t.data&&t.data.message&&uni.showToast({title:t.data.message,icon:"none"})}catch(t){uni.hideLoading(),uni.showToast({title:"切换地图失败: "+(t.message||"未知错误"),icon:"none"})}else uni.showToast({title:"不满足进入条件",icon:"none"});else uni.showToast({title:"地图ID无效",icon:"none"})},async fetchMapNpcs(){var e;if(!this.fetchingMapNpcs){this.fetchingMapNpcs=!0;try{const t=(null==(e=l.player)?void 0:e.current_map)||l.player&&l.player.current_map;if(!t)return;const a=await s.sendMessage({type:"get_map_npcs",data:{map_id:t}});if("get_map_npcs_timeout"===a.type){if(this.mapsConfig&&this.mapsConfig[t]){const e=this.mapsConfig[t];e.NPC&&Array.isArray(e.NPC)&&(this.mapNpcs=e.NPC.map(((e,t)=>"object"==typeof e?{id:e.id||`npc_${t}`,name:e.名称||e.name||"未知NPC",avatar:"static/npc/default.png",desc:e.描述||e.desc||`${e.名称||e.name||"未知NPC"}：一位神秘的江湖人物。`,functions:[{key:"talk",label:"对话"},{key:"shop",label:"交易"}]}:{id:`npc_${t}`,name:e,avatar:"static/npc/default.png",desc:`${e}：一位神秘的江湖人物。`,functions:[{key:"talk",label:"对话"},{key:"shop",label:"交易"}]})))}return}let n=[];Array.isArray(a.data)?n=a.data:a.data&&Array.isArray(a.data.npcs)?n=a.data.npcs:a.data&&a.data.data&&Array.isArray(a.data.data)&&(n=a.data.data),n.length>0?this.mapNpcs=n:this.mapNpcs=[]}catch(t){this.mapNpcs=[],uni.showToast({title:"获取NPC数据失败",icon:"none",duration:2e3})}finally{this.fetchingMapNpcs=!1}}},showNpcMenu(e){this.selectedNpc=e,this.showNpcMenuModal=!0,this.npcSidebarVisible=!1},closeNpcMenu(){this.showNpcMenuModal=!1,this.selectedNpc={}},async onNpcFunction(e,a){try{"talk"===e.key?await this.handleNpcTalk(a):"shop"===e.key?await this.handleNpcShop(a):"sell"===e.key?await this.handleNpcSell(a):"transport"===e.key?await this.handleNpcTransport(a):"heal"===e.key?await this.handleNpcHeal(a):"info"===e.key?await this.handleNpcInfo(a):"learn"===e.key?await this.handleNpcLearn(a):uni.showToast({title:`${e.label}功能暂未开放`,icon:"none"})}catch(s){t("error","at pages/index/index.vue:1846","NPC功能处理错误:",s),uni.showToast({title:"操作失败",icon:"none"})}this.closeNpcMenu()},debugSendMapList(){},async loadMapsConfig(){this.mapsConfig=await l.getMapsConfig()},async refreshPlayerData(){l.requestAllData?await l.requestAllData():await s.sendMessage({type:"get_player_data"}),this.updateData()},async handleNpcTalk(e){const t=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"talk"});t&&"npc_talk"===t.type&&uni.showModal({title:t.data.npc_name,content:t.data.dialogue,showCancel:!1})},async handleNpcShop(e){try{t("log","at pages/index/index.vue:1887","发送NPC商店请求:",e.name);const a=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"shop",data:{action:"list"}});t("log","at pages/index/index.vue:1895","收到NPC商店响应:",a),a&&"shop_items"===a.type?(t("log","at pages/index/index.vue:1899","商店物品:",a.data.items),this.showNpcShopModal(a.data.items,e.name)):a&&a.data&&a.data.items&&Array.isArray(a.data.items)?(t("log","at pages/index/index.vue:1903","兼容处理商店物品:",a.data.items),this.showNpcShopModal(a.data.items,e.name)):a&&"error"===a.type?(t("error","at pages/index/index.vue:1906","NPC商店错误:",a.data.message),uni.showToast({title:a.data.message,icon:"none"})):(t("error","at pages/index/index.vue:1912","未知响应类型:",a),uni.showToast({title:`未知响应: ${a?a.type:"无响应"}`,icon:"none"}))}catch(a){t("error","at pages/index/index.vue:1919","获取NPC商店失败:",a),uni.showToast({title:"获取商店信息失败",icon:"none"})}},async handleNpcSell(e){uni.showModal({title:e.name,content:"请在背包中选择要出售的物品",showCancel:!0,cancelText:"取消",confirmText:"去背包",success:t=>{t.confirm&&uni.navigateTo({url:`/pages/character/backpack?mode=sell&npc=${encodeURIComponent(e.name)}`})}})},async handleNpcTransport(e){const t=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"transport"});t&&"transport_destinations"===t.type&&this.showTransportModal(t.data.destinations)},async handleNpcHeal(e){uni.showModal({title:"治疗服务",content:"是否花费100银两完全恢复生命值？",success:async t=>{if(t.confirm){const t=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"heal"});t&&"heal_success"===t.type?(uni.showToast({title:"治疗成功",icon:"success"}),this.updateData()):t&&"error"===t.type&&uni.showToast({title:t.data.message,icon:"none"})}}})},async handleNpcInfo(e){const t=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"info"});t&&"info_services"===t.type&&this.showInfoServicesModal(t.data.services)},async handleNpcLearn(e){uni.showModal({title:"学习读书写字",content:"学费：10银两\n\n学习读书写字可以增加1点经验，悟性越高获得的经验越多。如果你还没有掌握读书写字技能，第一次学习将自动掌握该技能。\n\n确定要学习吗？",success:async a=>{if(a.confirm)try{const t=await s.sendMessage({type:"npc_function",npc_name:e.name,function:"learn"});t&&"learn_success"===t.type?uni.showModal({title:"学习成功",content:t.data.message,showCancel:!1,success:()=>{this.updateData()}}):t&&"error"===t.type&&uni.showToast({title:t.data.message,icon:"none"})}catch(n){t("error","at pages/index/index.vue:2030","学习失败:",n),uni.showToast({title:"学习失败",icon:"none"})}}})},showTransportModal(e){const t=e.map((e=>`${e.map} (${e.price}银两)`));uni.showActionSheet({itemList:t,success:async t=>{const a=e[t.tapIndex],n=await s.sendMessage({type:"npc_function",npc_name:this.selectedNpc.name,function:"transport",data:{destination:a.map}});n&&"transport_success"===n.type?(uni.showToast({title:"传送成功",icon:"success"}),this.updateData()):n&&"error"===n.type&&uni.showToast({title:n.data.message,icon:"none"})}})},showInfoServicesModal(e){const t=e.map((e=>`${e.description} (${e.price}银两)`));uni.showActionSheet({itemList:t,success:async t=>{const a=e[t.tapIndex],n=await s.sendMessage({type:"npc_function",npc_name:this.selectedNpc.name,function:"info",data:{info_type:a.type}});n&&"info_success"===n.type?(uni.showModal({title:"消息",content:n.data.message,showCancel:!1}),this.updateData()):n&&"error"===n.type&&uni.showToast({title:n.data.message,icon:"none"})}})},showNpcShopModal(e,a){if(t("log","at pages/index/index.vue:2102","显示NPC商店模态框:",a,e),!e||!Array.isArray(e)||0===e.length)return t("error","at pages/index/index.vue:2105","商店物品数据无效:",e),void uni.showToast({title:"该NPC没有商品出售",icon:"none"});const s=e.map((e=>`${e.name||e.item_id} - ${e.price||0}银两 (库存:${e.stock})`));t("log","at pages/index/index.vue:2120","商店物品列表:",s),uni.showActionSheet({itemList:s.slice(0,6),success:async s=>{t("log","at pages/index/index.vue:2125","选择了物品:",s.tapIndex,e[s.tapIndex]);const n=e[s.tapIndex];await this.buyNpcItem(n,a)},fail:e=>{t("log","at pages/index/index.vue:2130","用户取消选择:",e)}})},getItemName:e=>({heal_potion:"治疗药水",mana_potion:"法力药水",antidote:"解毒剂",strength_pill:"力量丹",agility_pill:"敏捷丹",intelligence_pill:"智力丹",great_heal_potion:"大治疗药水",exp_pill:"经验丹",breakthrough_pill:"突破丹",immortal_pill:"仙丹",iron_sword:"铁剑",steel_sword:"钢剑",iron_armor:"铁甲",steel_armor:"钢甲",iron_helmet:"铁盔",steel_helmet:"钢盔",bread:"面包",water:"清水",rope:"绳索",torch:"火把",map_scroll:"地图卷轴",teleport_scroll:"传送卷轴",common_herb:"普通草药",rare_herb:"稀有草药",ginseng:"人参",lingzhi:"灵芝"}[e]||e),async buyNpcItem(e,t){this.buyItem=e,this.buyNpcName=t,this.buyQuantity=1,this.showBuyModal=!0},closeBuyModal(){this.showBuyModal=!1,this.buyItem={},this.buyNpcName="",this.buyQuantity=1},decreaseQuantity(){this.buyQuantity>1&&this.buyQuantity--},increaseQuantity(){const e=this.buyItem.stock||0;this.buyQuantity<e&&this.buyQuantity++},onQuantityInput(e){const t=parseInt(e.detail.value)||1,a=this.buyItem.stock||0;this.buyQuantity=Math.max(1,Math.min(t,a))},async confirmBuy(){if(this.canBuy)try{const e=await s.sendMessage({type:"npc_function",npc_name:this.buyNpcName,function:"shop",data:{action:"buy",item_id:this.buyItem.item_id,quantity:this.buyQuantity}});e&&"buy_success"===e.type?(uni.showToast({title:"购买成功",icon:"success"}),this.updateData(),this.closeBuyModal()):e&&"error"===e.type&&uni.showToast({title:e.data.message,icon:"none"})}catch(e){t("error","at pages/index/index.vue:2251","购买失败:",e),uni.showToast({title:"购买失败",icon:"none"})}else uni.showToast({title:"无法购买",icon:"none"})},getMapNpcs(e){if(!e)return[];let t=e.NPC||e.npcs||[];return Array.isArray(t)?t.map((e=>"string"==typeof e?e:e.名称||e.name||e.id||"")).filter((e=>e)):[]},getMapMonsters(e){if(!e)return[];let t=e.怪物||e.monsters||e.monster||[];return Array.isArray(t)?t.map((e=>"string"==typeof e?e:e.名称||e.name||e.id||"")).filter((e=>e)):[]},getMapGatherItems(e){if(!e)return[];let t=e.采集物品||e.gather_items||e.gatherItems||[];return Array.isArray(t)?t.map((e=>{if("string"==typeof e)return e;const t=e.物品||e.item||e.name||"",a=e.概率||e.prob||e.probability||"";return t&&a?`${t}:${a}`:t})).filter((e=>e)):[]},getMapRequirements(e){if(!e)return"";const t=e.进入要求||e.enter_requirements||e.requirements||{};return"object"==typeof t&&null!==t?Object.entries(t).map((([e,t])=>`${e}:${t}`)).join("、"):""},onPlayerBtnTouchStart(e){this.playerBtnDragging=!0,this.playerBtnStartY=e.touches[0].clientY,this.playerBtnStartTop=parseInt(this.playerBtnTop)},onPlayerBtnTouchMove(e){if(!this.playerBtnDragging)return;const t=e.touches[0].clientY-this.playerBtnStartY;let a=this.playerBtnStartTop+t;a=Math.max(10,Math.min(90,a)),this.playerBtnTop=a+"%"},onPlayerBtnTouchEnd(){this.playerBtnDragging=!1},onPlayerBtnLongPress(){this.playerBtnTop="60%"},buildSelfPlayerInfo(){let e=this.player;if(!e&&l.getPlayerData&&(e=l.getPlayerData()),!e&&l.player&&(e=l.player),!e)return t("log","at pages/index/index.vue:2376","buildSelfPlayerInfo - no player data available"),null;const a={id:e.id||e.user_id||"self",name:e.name||e.character_name||e.username||"我",character_name:e.character_name||e.name||e.username||"我",level:e.level||1,status:e.status||"online",avatar:"/static/npc/default.png",isSelf:!0};return t("log","at pages/index/index.vue:2390","buildSelfPlayerInfo - built self info:",a),a},async fetchMapPlayers(){var e;if(!this.fetchingMapPlayers){this.fetchingMapPlayers=!0,this.player||(t("log","at pages/index/index.vue:2404","fetchMapPlayers - no player data, calling updateData"),this.updateData());try{t("log","at pages/index/index.vue:2409","fetchMapPlayers - this.player:",this.player),t("log","at pages/index/index.vue:2410","fetchMapPlayers - gameState.player:",l.player),t("log","at pages/index/index.vue:2411","fetchMapPlayers - gameState.getPlayerData():",l.getPlayerData?l.getPlayerData():"no getPlayerData method");const a=await s.sendMessage({type:"get_map_players",data:{map_id:(null==(e=this.player)?void 0:e.current_map)||"changan"}});if(t("log","at pages/index/index.vue:2420","fetchMapPlayers - response:",a),"map_players_success"===a.type){const e=a.data.players||[],s=this.buildSelfPlayerInfo();s?(t("log","at pages/index/index.vue:2428","fetchMapPlayers - selfPlayer:",s),this.mapPlayers=[s,...e]):(t("log","at pages/index/index.vue:2432","fetchMapPlayers - no player data, only showing other players"),this.mapPlayers=e),t("log","at pages/index/index.vue:2437","fetchMapPlayers - final mapPlayers:",this.mapPlayers),this.$forceUpdate()}else if("get_map_players_timeout"===a.type){t("log","at pages/index/index.vue:2443","fetchMapPlayers - timeout, showing self only");const e=this.buildSelfPlayerInfo();e?(this.mapPlayers=[e],t("log","at pages/index/index.vue:2447","fetchMapPlayers - timeout fallback, selfPlayer:",e)):(this.mapPlayers=[],t("log","at pages/index/index.vue:2450","fetchMapPlayers - timeout fallback, no player data")),this.$forceUpdate()}}catch(a){t("error","at pages/index/index.vue:2456","获取地图玩家失败:",a);const e=this.buildSelfPlayerInfo();e?(this.mapPlayers=[e],t("log","at pages/index/index.vue:2461","fetchMapPlayers - error fallback, selfPlayer:",e)):(this.mapPlayers=[],t("log","at pages/index/index.vue:2464","fetchMapPlayers - error fallback, no player data")),this.$forceUpdate()}finally{this.fetchingMapPlayers=!1}}},async showPlayerSidebar(){this.updateData(),this.playerSidebarVisible=!0,await this.fetchMapPlayers()},showPlayerMenu(e){this.selectedPlayer=e,this.showPlayerMenuModal=!0,this.playerSidebarVisible=!1},getPlayerStatusText:e=>({online:"在线",busy:"忙碌",battle:"战斗中",meditation:"打坐中",healing:"疗伤中",offline:"离线"}[e]||"未知"),async playerAction(e){const t={sneak_attack:"偷袭",give:"给与",steal:"偷窃",view:"查看"};try{const a=await s.sendMessage({type:"player_action",data:{action:e,target_player_id:this.selectedPlayer.id,target_player_name:this.selectedPlayer.name||this.selectedPlayer.character_name}});if("player_action_success"===a.type){if("sneak_attack"===e&&a.data.battle_started)return this.showPlayerMenuModal=!1,this.showBattlePopup=!0,this.battlePlayer=a.data.player,this.battleMonster=a.data.enemy,this.battleLog=a.data.battle_log||[],void(this.battleStage=a.data.stage||"battle");uni.showToast({title:a.data.message||`${t[e]}成功`,icon:"success"})}else"error"===a.type&&uni.showToast({title:a.data.message||`${t[e]}失败`,icon:"none"});this.showPlayerMenuModal=!1}catch(a){uni.showToast({title:`${t[e]}失败: ${a.message}`,icon:"none"})}},async loadChatMessages(){try{const e=await s.sendMessage({type:"get_chat_messages",data:{}});"chat_messages_success"===e.type&&(this.chatMessages=e.data.messages||[],this.scrollChatToBottom())}catch(e){t("error","at pages/index/index.vue:2569","加载聊天消息失败:",e)}},async sendChatMessage(){const e=this.chatInputText.trim();if(!e)return;if("private"===this.currentChatType&&!this.chatTargetName.trim())return void uni.showToast({title:"请输入目标玩家名",icon:"none"});const t=this.filterSensitiveWords(e);try{const e={content:t,chat_type:this.currentChatType};"private"===this.currentChatType&&(e.target_name=this.chatTargetName.trim());const a=await s.sendMessage({type:"send_chat_message",data:e});"chat_message_success"===a.type?(this.chatInputText="",uni.showToast({title:"发送成功",icon:"success"})):"error"===a.type&&uni.showToast({title:a.data.message||"发送失败",icon:"none"})}catch(a){uni.showToast({title:"发送失败: "+a.message,icon:"none"})}},filterSensitiveWords(e){let t=e;return["政治","反动","暴力","色情","赌博","毒品","诈骗","外挂","作弊","习近平","共产党","法轮功","台独","藏独","疆独","六四","天安门","操你妈","草你妈","傻逼","煞笔","智障","脑残","去死","死全家"].forEach((e=>{const a=new RegExp(e,"gi");t=t.replace(a,"*".repeat(e.length))})),t},formatChatTime(e){const t=new Date(e),a=new Date;return t.toDateString()===a.toDateString()?t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):t.toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},formatChatTimeWithSeconds:e=>new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),getChatTypeName:e=>({world:"世界",private:"私聊",sect:"门派",rumor:"谣言",system:"系统"}[e]||"未知"),getChatPlaceholder(){return{world:"输入世界聊天内容...",private:"输入私聊内容...",sect:"输入门派聊天内容...",rumor:"输入谣言内容..."}[this.currentChatType]||"输入聊天内容..."},getCurrentChatTypeName(){const e=this.chatTypes.find((e=>e.value===this.currentChatType));return e?e.name:"世界"},onChatTypeChange(e){const t=e.detail.value;this.chatTypeIndex=t,this.currentChatType=this.chatTypes[t].value,"private"!==this.currentChatType&&(this.chatTargetName="")},scrollChatToBottom(){this.$nextTick((()=>{}))},handleChatMessage(e){this.chatMessages.push({sender:e.sender,content:e.content,time:e.time||Date.now(),chat_type:e.chat_type||"world",target_name:e.target_name,isOwn:e.sender===(this.player.name||this.player.character_name)||"你"===e.sender}),this.scrollChatToBottom()},handleMapPlayersUpdate(e){e.map_id===this.player.current_map&&(this.mapPlayers=e.players||[])},typeWriterEffect(e,t,a){e.displayText="";const s=e.description,n=[];let i="",l=!1;for(let d=0;d<s.length;d++){const e=s[d];"<"===e?(i&&(n.push({type:"text",content:i}),i=""),l=!0,i=e):">"===e?(i+=e,n.push({type:"tag",content:i}),i="",l=!1):i+=e}i&&n.push({type:l?"tag":"text",content:i});let o=0,c=0;const r=()=>{if(o<n.length){const a=n[o];"tag"===a.type?(e.displayText+=a.content,o++,c=0):c<a.content.length?(e.displayText+=a.content[c],c++):(o++,c=0),this.$set(this.eventLog,t,{...e}),setTimeout(r,30)}else e.displayText=s,this.$set(this.eventLog,t,{...e}),"function"==typeof a&&a()};r()},handleEncounterMonster(e){const t=e.monster||{};let a="";a="active"===t.attack_mode?`${t.name||"未知怪物"}向你大喊一声扑了上来！`:`你遇到了${t.name||"未知怪物"}，你要怎么办呢？`;const n={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"遭遇战斗",description:a};l.eventLog.unshift(n),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),l.notifyUpdate("eventLog"),this.typeWriterEffect(n,0,(()=>{setTimeout((()=>{this.battleMonster={...t},this.battlePlayer={...this.player},this.battleLog=[],this.battleStage="encounter",this.battleAttackMode=t.attack_mode||"passive",this.battleResultProcessed=!1,this.escapeProcessed=!1,this.showBattlePopup=!0,"active"===t.attack_mode&&setTimeout((()=>{s.sendMessage({type:"start_battle_from_encounter",data:{monster_id:this.battleMonster.id}}),this.battleStage="battle"}),1e3)}),1e3)}))},handleBattleAttack(){"encounter"===this.battleStage&&"passive"===this.battleAttackMode&&(this.battleResultProcessed=!1,this.escapeProcessed=!1,s.sendMessage({type:"start_battle_from_encounter",data:{monster_id:this.battleMonster.id}}),this.battleStage="battle")},handleBattleEscape(){this.escapeRequested||this.escapeProcessed||(this.escapeRequested=!0,s.sendMessage({type:"escape_battle",data:{monster_id:this.battleMonster.id}}).then((e=>{})).catch((e=>{this.escapeRequested=!1,uni.showToast({title:"逃跑请求失败",icon:"none"})})))},handleBattleClose(){this.showBattlePopup=!1,this.battleLog=[],this.battleStage="",this.battleAttackMode="",this.battlePlayer={},this.battleMonster={},this.escapeProcessed=!1,this.battleResultProcessed=!1,this.escapeRequested=!1,this.updateData()},handleBattleRound(e){const t={...e,timestamp:e.timestamp||(new Date).toLocaleTimeString("zh-CN",{hour12:!1})};Array.isArray(this.battleLog)||(this.battleLog=[]),this.battleLog.push(t),this.battleLog.length>50&&(this.battleLog=this.battleLog.slice(-50));const a=Date.now();void 0!==e.player_hp&&(this.player.hp=e.player_hp,this.battlePlayer.hp=e.player_hp,this.lastBattleHpUpdate=a),void 0!==e.player_max_hp&&(this.player.max_hp=e.player_max_hp,this.battlePlayer.max_hp=e.player_max_hp),void 0!==e.player_mp&&(this.player.mp=e.player_mp),void 0!==e.player_max_mp&&(this.player.max_mp=e.player_max_mp),void 0!==e.enemy_hp&&(this.battleMonster.hp=e.enemy_hp),void 0!==e.enemy_max_hp&&(this.battleMonster.max_hp=e.enemy_max_hp)},handleBattleResult(e){if(this.battleResultProcessed)return;if(this.escapeProcessed)return;this.battleResultProcessed=!0,this.battleStage="end";const t={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"战斗结束",description:e.win?`你击败了${this.battleMonster.name||"怪物"}，战斗胜利！`:`你被${this.battleMonster.name||"怪物"}击败了，战斗失败！`};l.eventLog.unshift(t),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),l.notifyUpdate("eventLog"),e.win&&e.rewards?this.showBattleRewards(e.rewards):e.win||this.showBattleFailure()},showBattleRewards(e){let t="战斗胜利！获得奖励：";e["历练值"]&&(t+=`\n历练值 +${e["历练值"]}`),e["银两"]&&(t+=`\n银两 +${e["银两"]}`),e["武学点"]&&(t+=`\n武学点 +${e["武学点"]}`),e["物品"]&&e["物品"].length>0&&(t+="\n物品：",e["物品"].forEach((e=>{t+=`\n  ${e.id} x${e.quantity}`}))),"战斗胜利！获得奖励："===t&&(t="战斗胜利！但是没有获得任何奖励。"),setTimeout((()=>{uni.showModal({title:"战斗胜利",content:t.trim(),showCancel:!1,confirmText:"确定"})}),1e3)},showBattleFailure(){setTimeout((()=>{uni.showModal({title:"战斗失败",content:"你被击败了，受了重伤。",showCancel:!1,confirmText:"确定"})}),1e3)},handleBattlePlayerData(e){if(this.showBattlePopup&&"battle"===this.battleStage){const t=Date.now()-(this.lastBattleHpUpdate||0),a=this.battlePlayer.hp,s=this.player.hp;this.battlePlayer={...this.battlePlayer,...e},this.player={...this.player,...e},t<1e3&&(null!=a&&(this.battlePlayer.hp=a),null!=s&&(this.player.hp=s))}else this.player={...this.player,...e}},handleEscapeBattleResult(e){if(this.escapeRequested=!1,!this.escapeProcessed)if(e.success){this.escapeProcessed=!0;const t={round:this.battleLog.length+1,attacker:"系统",defender:"玩家",damage:0,desc:e.message||"你成功逃离了战斗！",move:"逃跑",martial:"",player_hp:this.battlePlayer.hp,enemy_hp:this.battleMonster.hp,special_effect:null,effect_desc:"",timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})};this.battleLog.push(t);const a={timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1}),name:"战斗结束",description:`你成功从${this.battleMonster.name||"怪物"}的追击中逃脱，逃之夭夭！`};l.eventLog.unshift(a),l.eventLog.length>50&&(l.eventLog=l.eventLog.slice(0,50)),l.notifyUpdate("eventLog"),setTimeout((()=>{this.showBattlePopup=!1,this.escapeProcessed=!1}),2e3)}else{const t={round:this.battleLog.length+1,attacker:"系统",defender:"玩家",damage:0,desc:e.message||"你试图逃跑，但被怪物拦住了！",move:"逃跑",martial:"",player_hp:this.battlePlayer.hp,enemy_hp:this.battleMonster.hp,special_effect:null,effect_desc:"",timestamp:(new Date).toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})};this.battleLog.push(t)}},async handleHealing(){try{this.healingMeditationMessages=[],this.healingMeditationLoading=!0,uni.showLoading({title:"正在疗伤..."}),a.sendMessage("healing",{})}catch(e){this.healingMeditationLoading=!1,uni.hideLoading(),uni.showToast({title:"疗伤失败: "+e.message,icon:"none"})}},async handleMeditation(){try{this.healingMeditationMessages=[],this.healingMeditationLoading=!0,uni.showLoading({title:"正在打坐..."}),a.sendMessage("meditation",{})}catch(e){this.healingMeditationLoading=!1,uni.hideLoading(),uni.showToast({title:"打坐失败: "+e.message,icon:"none"})}},handleHealingLog(e){e&&e.message&&(this.healingMeditationMessages.push(e.message),this.healingMeditationMessages.length>10&&(this.healingMeditationMessages=this.healingMeditationMessages.slice(-10)),this.healingMeditationLoading=!/疗伤结束|收功|耗尽/.test(e.message),/疗伤结束|收功|耗尽/.test(e.message)&&setTimeout((()=>{this.healingMeditationMessages=[],this.healingMeditationLoading=!1}),1200))},handleMeditationLog(e){e&&e.message&&(this.healingMeditationMessages.push(e.message),this.healingMeditationMessages.length>10&&(this.healingMeditationMessages=this.healingMeditationMessages.slice(-10)),this.healingMeditationLoading=!/打坐结束|收功|耗尽/.test(e.message),/打坐结束|收功|耗尽/.test(e.message)&&setTimeout((()=>{this.healingMeditationMessages=[],this.healingMeditationLoading=!1,this.refreshPlayerData&&this.refreshPlayerData()}),1200))},onNpcBtnLongPress(e){this.npcBtnDragging=!0,this.npcBtnStartY=e.touches[0].clientY;let t=0;t="string"==typeof this.npcBtnTop&&this.npcBtnTop.includes("%")?uni.getSystemInfoSync().windowHeight*parseFloat(this.npcBtnTop)/100:parseFloat(this.npcBtnTop),this.npcBtnStartTop=t},onNpcBtnTouchMove(e){if(!this.npcBtnDragging)return;let t=e.touches[0].clientY-this.npcBtnStartY,a=this.npcBtnStartTop+t;const s=uni.getSystemInfoSync().windowHeight-80;a<40&&(a=40),a>s&&(a=s),this.npcBtnTop=a+"px"},onNpcBtnTouchEnd(){this.npcBtnDragging=!1},onLeftBtnTouchStart(e){this.leftBtnDragging=!0,this.leftBtnStartY=e.touches[0].clientY;let t=0;t="string"==typeof this.leftBtnTop&&this.leftBtnTop.includes("%")?uni.getSystemInfoSync().windowHeight*parseFloat(this.leftBtnTop)/100:parseFloat(this.leftBtnTop),this.leftBtnStartTop=t},onLeftBtnLongPress(){this.leftBtnTop="70%"},onLeftBtnTouchMove(e){if(!this.leftBtnDragging)return;let t=e.touches[0].clientY-this.leftBtnStartY,a=this.leftBtnStartTop+t;const s=uni.getSystemInfoSync().windowHeight-80;a<40&&(a=40),a>s&&(a=s),this.leftBtnTop=a+"px"},onLeftBtnTouchEnd(){this.leftBtnDragging=!1},toggleLeftMenu(){this.leftMenuVisible=!this.leftMenuVisible},onMenuClick(e){this.leftMenuVisible=!1,"healing"===e?this.handleHealing():"meditation"===e?this.handleMeditation():"ranking"===e?this.openRanking():"redeem"===e&&this.openRedeemCode()},openChat(){this.showChatPopup=!0,this.loadChatMessages()},onChatBtnTouchStart(e){this.chatBtnDragging=!0,this.chatBtnStartY=e.touches[0].clientY;let t=0;t="string"==typeof this.chatBtnTop&&this.chatBtnTop.includes("%")?uni.getSystemInfoSync().windowHeight*parseFloat(this.chatBtnTop)/100:parseFloat(this.chatBtnTop),this.chatBtnStartTop=t},onChatBtnTouchMove(e){if(!this.chatBtnDragging)return;const t=e.touches[0].clientY-this.chatBtnStartY;let a=this.chatBtnStartTop+t;const s=uni.getSystemInfoSync().windowHeight-80;a<10&&(a=10),a>s&&(a=s),this.chatBtnTop=a+"px"},onChatBtnTouchEnd(){this.chatBtnDragging=!1},onChatBtnLongPress(){this.chatBtnTop="55%"},openRanking(){this.showRankingModal=!0,this.loadRanking()},openRedeemCode(){this.showRedeemModal=!0},switchRankingTab(e){this.rankingCurrentTab!==e&&(this.rankingCurrentTab=e,this.loadRanking())},async loadRanking(){var e;this.rankingLoading=!0;try{const t=await s.sendMessage({type:"get_ranking",data:{type:this.rankingCurrentTab,limit:30}});if(t&&("success"===t.type||"get_ranking_success"===t.type)&&t.data){this.rankingList=t.data.list||[],this.myRanking=t.data.myRanking||null;const a=(null==(e=l.player)?void 0:e.name)||l.name;this.rankingList.forEach((e=>{e.isCurrentPlayer=e.name===a}))}else uni.showToast({title:"获取排行榜失败",icon:"none"})}catch(a){t("error","at pages/index/index.vue:3350","获取排行榜失败:",a),uni.showToast({title:"网络错误",icon:"none"})}finally{this.rankingLoading=!1}},async submitRedeemCode(){var e;if(this.redeemCode.trim()){this.redeemLoading=!0;try{const t=await s.sendMessage({type:"redeem_code",data:{code:this.redeemCode.trim()}});if(t&&"success"===t.type){const e=(t.data.rewards||[]).map((e=>`${e.name} x${e.quantity}`)).join("、");uni.showToast({title:`兑换成功！获得：${e}`,icon:"success",duration:3e3}),this.redeemCode="",this.refreshPlayerData()}else{const a=(null==(e=null==t?void 0:t.data)?void 0:e.message)||"兑换失败";uni.showToast({title:a,icon:"none"})}}catch(a){t("error","at pages/index/index.vue:3391","兑换码兑换失败:",a),uni.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.redeemLoading=!1}}else uni.showToast({title:"请输入兑换码",icon:"none"})},formatMoney:e=>e>=1e4?(e/1e4).toFixed(1)+"万":e+"两",formatNumber:e=>e>=1e4?(e/1e4).toFixed(1)+"万":e.toString(),formatTime(e){const t=new Date(e),a=new Date-t;return a<6e4?"刚刚":a<36e5?Math.floor(a/6e4)+"分钟前":a<864e5?Math.floor(a/36e5)+"小时前":t.toLocaleDateString()},addMeditationMessage(e){this.healingMeditationMessages.push(e),this.$nextTick((()=>{const e=this.$el.querySelector(".healing-meditation-msg-list");e&&(e.scrollTop=e.scrollHeight)}))},endMeditation(){this.healingMeditationLoading=!1},handleReconnectCloseAllPopups(){this.showBattlePopup=!1,this.showGatheringPopup=!1,this.showMapPopup=!1,this.showNpcMenuModal=!1,this.npcSidebarVisible=!1,this.leftMenuVisible=!1,this.healingMeditationMessages=[],this.playerSidebarVisible=!1,this.showPlayerMenuModal=!1,this.showChatPopup=!1,this.showBuyModal=!1,this.chatBtnDragging=!1,this.leftBtnDragging=!1,this.npcBtnDragging=!1,this.playerBtnDragging=!1}},mounted(){l.requestMapsConfig().then((e=>{this.mapsConfig=e})),a.on("escape_battle_result",this.handleEscapeBattleResult),a.on("healing_log",this.handleHealingLog),a.on("meditation_log",this.handleMeditationLog),a.on("chat_message",this.handleChatMessage),a.on("map_players_update",this.handleMapPlayersUpdate),uni.$on&&uni.$on("ws_reconnected",this.handleReconnectCloseAllPopups)},beforeDestroy(){a.off("chat_message",this.handleChatMessage),a.off("map_players_update",this.handleMapPlayersUpdate),uni.$off&&uni.$off("ws_reconnected",this.handleReconnectCloseAllPopups)}},[["render",function(t,a,s,n,i,l){const o=e.resolveComponent("gathering-popup"),c=e.resolveComponent("BattlePopup");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"container"},[e.createElementVNode("view",{class:"map-bar"},[e.createElementVNode("view",{class:"map-bar-left"},[e.createElementVNode("text",{class:"user-name"},e.toDisplayString(i.player&&i.player.name?i.player.name:""),1),e.createElementVNode("text",{class:"user-label"},"｜"),e.createElementVNode("text",{class:"map-label"},"当前地图："),e.createElementVNode("text",{class:"map-name"},e.toDisplayString(l.currentMapName||""),1)]),e.createElementVNode("view",{class:"map-bar-right"},[e.createElementVNode("text",{class:e.normalizeClass(["connection-status",l.connectionStatusClass])},e.toDisplayString(i.connectionStatus),3),e.createElementVNode("button",{class:"map-btn",onClick:a[0]||(a[0]=e=>i.showMapPopup=!0)},"切换地图")])]),e.createElementVNode("view",{class:"announcement-bar"},[e.createElementVNode("view",{class:"announcement-icon"},"📢"),e.createElementVNode("view",{class:"announcement-content"},[e.createElementVNode("view",{class:"announcement-scroll"},[e.createElementVNode("text",{class:"announcement-text"},e.toDisplayString(i.announcementText),1),e.createElementVNode("text",{class:"announcement-text"},e.toDisplayString(i.announcementText),1)])])]),e.createElementVNode("view",{class:"character-card"},[e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("text",{class:"progress-label"},"气血"),e.createElementVNode("view",{class:"progress-bg"},[e.createElementVNode("view",{class:"progress-fill hp-fill",style:e.normalizeStyle({width:l.hpPercent+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},e.toDisplayString(Math.floor(void 0!==i.player.hp?i.player.hp:100))+"/"+e.toDisplayString(Math.floor(i.player.max_hp||i.player.base_max_hp||100)),1)]),e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("text",{class:"progress-label"},"内力"),e.createElementVNode("view",{class:"progress-bg"},[e.createElementVNode("view",{class:"progress-fill mp-fill",style:e.normalizeStyle({width:l.mpPercent+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},e.toDisplayString(Math.floor(i.player.mp||50))+"/"+e.toDisplayString(Math.floor(i.player.max_mp||i.player.base_max_mp||50)),1)]),e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("text",{class:"progress-label"},"体力"),e.createElementVNode("view",{class:"progress-bg"},[e.createElementVNode("view",{class:"progress-fill stamina-fill",style:e.normalizeStyle({width:l.staminaPercent+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},e.toDisplayString(Math.floor(i.player.energy||100))+"/"+e.toDisplayString(Math.floor(i.player.max_energy||i.player.base_max_energy||100)),1)]),e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("text",{class:"progress-label"},"精力"),e.createElementVNode("view",{class:"progress-bg"},[e.createElementVNode("view",{class:"progress-fill energy-fill",style:e.normalizeStyle({width:l.energyPercent+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},e.toDisplayString(Math.floor(i.player.spirit||100))+"/"+e.toDisplayString(Math.floor(i.player.max_spirit||100)),1)])]),l.hasTalentBonuses?(e.openBlock(),e.createElementBlock("view",{key:0,class:"talent-bonus-card"},[e.createElementVNode("view",{class:"talent-bonus-title"},"天赋增益"),e.createElementVNode("view",{class:"talent-bonus-grid"},[l.getStrengthBonus()>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"talent-bonus-item"},[e.createElementVNode("text",{class:"talent-bonus-label"},"力量"),e.createElementVNode("text",{class:"talent-bonus-value"},"+"+e.toDisplayString(Math.floor(l.getStrengthBonus()||0))+"%攻击",1)])):e.createCommentVNode("",!0),l.getIntelligenceBonus()>0?(e.openBlock(),e.createElementBlock("view",{key:1,class:"talent-bonus-item"},[e.createElementVNode("text",{class:"talent-bonus-label"},"悟性"),e.createElementVNode("text",{class:"talent-bonus-value"},"+"+e.toDisplayString(Math.floor(l.getIntelligenceBonus()||0))+"%经验",1)])):e.createCommentVNode("",!0),l.getAgilityDefenseBonus()>0?(e.openBlock(),e.createElementBlock("view",{key:2,class:"talent-bonus-item"},[e.createElementVNode("text",{class:"talent-bonus-label"},"身法"),e.createElementVNode("text",{class:"talent-bonus-value"},"+"+e.toDisplayString(Math.floor(l.getAgilityDefenseBonus()||0))+"%防御",1)])):e.createCommentVNode("",!0),l.getConstitutionHpBonus()>0?(e.openBlock(),e.createElementBlock("view",{key:3,class:"talent-bonus-item"},[e.createElementVNode("text",{class:"talent-bonus-label"},"根骨"),e.createElementVNode("text",{class:"talent-bonus-value"},"+"+e.toDisplayString(Math.floor(l.getConstitutionHpBonus()||0))+"%气血",1)])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"jianghu-section-flex-opt"},[e.createElementVNode("button",{class:"main-btn",onClick:a[1]||(a[1]=(...e)=>l.triggerAdventure&&l.triggerAdventure(...e)),disabled:!i.isAuthed||"normal"!==i.status},[e.createElementVNode("text",{class:"main-btn-text"},"闯")],8,["disabled"])]),i.showNpcMenuModal?(e.openBlock(),e.createElementBlock("view",{key:1,class:"npc-menu-modal",onClick:a[4]||(a[4]=(...e)=>l.closeNpcMenu&&l.closeNpcMenu(...e))},[e.createElementVNode("view",{class:"npc-menu-content",onClick:a[3]||(a[3]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"npc-menu-header"},[e.createElementVNode("image",{class:"npc-menu-avatar",src:i.selectedNpc.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("text",{class:"npc-menu-name"},e.toDisplayString(i.selectedNpc&&i.selectedNpc.name?i.selectedNpc.name:""),1)]),e.createElementVNode("text",{class:"npc-menu-desc"},e.toDisplayString(i.selectedNpc.desc),1),e.createElementVNode("view",{class:"npc-menu-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.selectedNpc.functions,(t=>(e.openBlock(),e.createElementBlock("button",{key:t.key,class:"npc-menu-btn",onClick:e=>l.onNpcFunction(t,i.selectedNpc)},e.toDisplayString(t.label),9,["onClick"])))),128))]),e.createElementVNode("button",{class:"npc-menu-close",onClick:a[2]||(a[2]=(...e)=>l.closeNpcMenu&&l.closeNpcMenu(...e))},"关闭")])])):e.createCommentVNode("",!0),i.showBuyModal?(e.openBlock(),e.createElementBlock("view",{key:2,class:"buy-modal-mask",onClick:a[13]||(a[13]=(...e)=>l.closeBuyModal&&l.closeBuyModal(...e))},[e.createElementVNode("view",{class:"buy-modal",onClick:a[12]||(a[12]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"buy-modal-header"},[e.createElementVNode("text",{class:"buy-modal-title"},"购买物品"),e.createElementVNode("button",{class:"buy-modal-close",onClick:a[5]||(a[5]=(...e)=>l.closeBuyModal&&l.closeBuyModal(...e))},"×")]),e.createElementVNode("view",{class:"buy-modal-content"},[e.createElementVNode("view",{class:"item-info"},[e.createElementVNode("view",{class:"item-name"},e.toDisplayString(i.buyItem.name||i.buyItem.item_id),1),e.createElementVNode("view",{class:"item-details"},[e.createElementVNode("text",{class:"item-price"},"单价: "+e.toDisplayString(i.buyItem.price||0)+" 银两",1),e.createElementVNode("text",{class:"item-stock"},"库存: "+e.toDisplayString(i.buyItem.stock||0),1)])]),e.createElementVNode("view",{class:"quantity-section"},[e.createElementVNode("text",{class:"quantity-label"},"购买数量:"),e.createElementVNode("view",{class:"quantity-input-group"},[e.createElementVNode("button",{class:"quantity-btn",onClick:a[6]||(a[6]=(...e)=>l.decreaseQuantity&&l.decreaseQuantity(...e))},"-"),e.withDirectives(e.createElementVNode("input",{class:"quantity-input",type:"number","onUpdate:modelValue":a[7]||(a[7]=e=>i.buyQuantity=e),onInput:a[8]||(a[8]=(...e)=>l.onQuantityInput&&l.onQuantityInput(...e))},null,544),[[e.vModelText,i.buyQuantity]]),e.createElementVNode("button",{class:"quantity-btn",onClick:a[9]||(a[9]=(...e)=>l.increaseQuantity&&l.increaseQuantity(...e))},"+")])]),e.createElementVNode("view",{class:"total-section"},[e.createElementVNode("text",{class:"total-label"},"总价: "+e.toDisplayString((i.buyItem.price||0)*i.buyQuantity)+" 银两",1),e.createElementVNode("text",{class:"money-info"},"当前银两: "+e.toDisplayString(i.money),1)])]),e.createElementVNode("view",{class:"buy-modal-actions"},[e.createElementVNode("button",{class:"buy-btn-cancel",onClick:a[10]||(a[10]=(...e)=>l.closeBuyModal&&l.closeBuyModal(...e))},"取消"),e.createElementVNode("button",{class:"buy-btn-confirm",onClick:a[11]||(a[11]=(...e)=>l.confirmBuy&&l.confirmBuy(...e)),disabled:!l.canBuy},"确认购买",8,["disabled"])])])])):e.createCommentVNode("",!0),"normal"!==i.status?(e.openBlock(),e.createElementBlock("view",{key:3,class:"status-display"},[e.createElementVNode("text",{class:e.normalizeClass(["status-text","status-"+i.status])},e.toDisplayString(l.getStatusText()),3)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"event-log"},[e.createElementVNode("view",{class:"log-header"},[e.createElementVNode("text",{class:"log-title"},"江湖日志"),e.createElementVNode("text",{class:"log-clear",onClick:a[14]||(a[14]=(...e)=>l.clearLog&&l.clearLog(...e))},"清空")]),e.createElementVNode("scroll-view",{class:"log-content","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.eventLog,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"log-item",key:a},[e.createElementVNode("view",{class:"log-header-line"},[e.createElementVNode("text",{class:"log-time"},e.toDisplayString(t.timestamp),1),e.createElementVNode("text",{class:"log-event"},e.toDisplayString(t&&t.name?t.name:""),1)]),e.createElementVNode("view",{class:"log-content-line"},[e.createElementVNode("view",{class:"log-desc",innerHTML:t.displayText},null,8,["innerHTML"])])])))),128)),0===i.eventLog.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"log-empty"},[e.createElementVNode("text",null,"暂无江湖记录")])):e.createCommentVNode("",!0)])]),i.showGatheringPopup?(e.openBlock(),e.createBlock(o,{key:4,visible:i.showGatheringPopup,event:i.gatheringEvent,times:i.gatheringTimes,result:i.gatheringResult,inventory:l.currentInventory,onClose:l.closeGatheringPopup,onDoGather:l.doGather},null,8,["visible","event","times","result","inventory","onClose","onDoGather"])):e.createCommentVNode("",!0),i.showMapPopup?(e.openBlock(),e.createElementBlock("view",{key:5,class:"map-popup-mask",onClick:a[17]||(a[17]=e=>i.showMapPopup=!1)},[e.createElementVNode("view",{class:"map-popup",onClick:a[16]||(a[16]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"map-popup-title"},"切换地图"),e.createElementVNode("view",{class:"map-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.mapList,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["map-item",{active:i.currentMap&&i.currentMap.id===t.id,locked:!l.canEnterMap(t)}]),onClick:e=>l.selectMap(t)},[e.createElementVNode("view",{class:"map-item-title"},e.toDisplayString(t.名称||t.name||""),1),e.createElementVNode("view",{class:"map-item-desc"},e.toDisplayString(t.描述||t.desc),1),l.getMapNpcs(t).length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"map-item-npc"},"NPC："+e.toDisplayString(l.getMapNpcs(t).join("、")),1)):e.createCommentVNode("",!0),l.getMapMonsters(t).length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"map-item-monster"},"怪物："+e.toDisplayString(l.getMapMonsters(t).join("、")),1)):e.createCommentVNode("",!0),l.getMapGatherItems(t).length?(e.openBlock(),e.createElementBlock("view",{key:2,class:"map-item-gather"},"采集物品："+e.toDisplayString(l.getMapGatherItems(t).join("、")),1)):e.createCommentVNode("",!0),l.getMapRequirements(t)?(e.openBlock(),e.createElementBlock("view",{key:3,class:"map-item-req"},"进入条件："+e.toDisplayString(l.getMapRequirements(t)),1)):e.createCommentVNode("",!0)],10,["onClick"])))),128))]),e.createElementVNode("button",{class:"close-btn",onClick:a[15]||(a[15]=e=>i.showMapPopup=!1)},"关闭")])])):e.createCommentVNode("",!0),i.showBattlePopup?(e.openBlock(),e.createBlock(c,{key:6,visible:i.showBattlePopup,battleLog:i.battleLog,player:i.battlePlayer,monster:i.battleMonster,battleStage:i.battleStage,attackMode:i.battleAttackMode,onAttack:l.handleBattleAttack,onEscape:l.handleBattleEscape,onClose:l.handleBattleClose},null,8,["visible","battleLog","player","monster","battleStage","attackMode","onAttack","onEscape","onClose"])):e.createCommentVNode("",!0),i.healingMeditationMessages.length>0?(e.openBlock(),e.createElementBlock("view",{key:7,class:"healing-meditation-popup-centered"},[i.healingMeditationLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"healing-meditation-loading"},[e.createElementVNode("view",{class:"spinner"})])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"healing-meditation-msg-list",style:{"max-height":"180px","overflow-y":"auto"}},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.healingMeditationMessages,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:"healing-meditation-msg-centered"},e.toDisplayString(t),1)))),128))])])):e.createCommentVNode("",!0),i.showChatPopup?(e.openBlock(),e.createElementBlock("view",{key:8,class:"chat-popup-mask",onClick:a[25]||(a[25]=e=>i.showChatPopup=!1)},[e.createElementVNode("view",{class:"chat-popup",onClick:a[24]||(a[24]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"chat-popup-header"},[e.createElementVNode("text",{class:"chat-popup-title"},"全服聊天"),e.createElementVNode("button",{class:"chat-popup-close",onClick:a[18]||(a[18]=e=>i.showChatPopup=!1)},"×")]),e.createElementVNode("view",{class:"chat-messages",ref:"chatMessages"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.chatMessages,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["chat-message",t.isOwn?"chat-message-own":"","chat-message-"+(t.chat_type||"world")])},[e.createElementVNode("view",{class:"chat-message-header"},[e.createElementVNode("view",{class:"chat-message-info"},[e.createElementVNode("text",{class:"chat-time"},e.toDisplayString(l.formatChatTimeWithSeconds(t.time)),1),e.createElementVNode("text",{class:e.normalizeClass(["chat-sender",[t.isOwn?"chat-sender-own":"","chat-sender-"+(t.chat_type||"world")]])},e.toDisplayString(t.sender),3)]),t.chat_type&&"world"!==t.chat_type?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["chat-type-badge","chat-type-"+t.chat_type])},e.toDisplayString(l.getChatTypeName(t.chat_type)),3)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"chat-content"},e.toDisplayString(t.content),1)],2)))),128)),0===i.chatMessages.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-messages"},[e.createElementVNode("text",null,"暂无聊天消息")])):e.createCommentVNode("",!0)],512),e.createElementVNode("view",{class:"chat-input-area"},[e.createElementVNode("view",{class:"chat-input-row"},[e.createElementVNode("view",{class:"chat-type-selector"},[e.createElementVNode("picker",{value:i.chatTypeIndex,range:l.chatTypeNames,onChange:a[19]||(a[19]=(...e)=>l.onChatTypeChange&&l.onChatTypeChange(...e)),class:"chat-type-picker"},[e.createElementVNode("view",{class:"chat-type-display"},[e.createElementVNode("text",null,e.toDisplayString(l.getCurrentChatTypeName()),1),e.createElementVNode("text",{class:"picker-arrow"},"▼")])],40,["value","range"])]),"private"===i.currentChatType?e.withDirectives((e.openBlock(),e.createElementBlock("input",{key:0,class:"chat-target-input","onUpdate:modelValue":a[20]||(a[20]=e=>i.chatTargetName=e),placeholder:"目标玩家名",maxlength:"20"},null,512)),[[e.vModelText,i.chatTargetName]]):e.createCommentVNode("",!0),e.withDirectives(e.createElementVNode("input",{class:"chat-input","onUpdate:modelValue":a[21]||(a[21]=e=>i.chatInputText=e),placeholder:l.getChatPlaceholder(),onConfirm:a[22]||(a[22]=(...e)=>l.sendChatMessage&&l.sendChatMessage(...e)),maxlength:"100"},null,40,["placeholder"]),[[e.vModelText,i.chatInputText]]),e.createElementVNode("button",{class:"chat-send-btn",onClick:a[23]||(a[23]=(...e)=>l.sendChatMessage&&l.sendChatMessage(...e)),disabled:!i.chatInputText.trim()||"private"===i.currentChatType&&!i.chatTargetName.trim()},"发送",8,["disabled"])])])])])):e.createCommentVNode("",!0),i.showPlayerMenuModal?(e.openBlock(),e.createElementBlock("view",{key:9,class:"player-menu-modal-mask",onClick:a[32]||(a[32]=e=>i.showPlayerMenuModal=!1)},[e.createElementVNode("view",{class:"player-menu-modal",onClick:a[31]||(a[31]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"player-menu-header"},[e.createElementVNode("text",{class:"player-menu-title"},e.toDisplayString(i.selectedPlayer.name||i.selectedPlayer.character_name),1),e.createElementVNode("button",{class:"player-menu-close",onClick:a[26]||(a[26]=e=>i.showPlayerMenuModal=!1)},"×")]),e.createElementVNode("view",{class:"player-menu-info"},[e.createElementVNode("text",{class:"player-menu-level"},"等级: "+e.toDisplayString(i.selectedPlayer.level||"未知"),1),e.createElementVNode("text",{class:"player-menu-status"},"状态: "+e.toDisplayString(l.getPlayerStatusText(i.selectedPlayer.status)),1)]),e.createElementVNode("view",{class:"player-menu-actions"},[e.createElementVNode("button",{class:"player-action-btn attack",onClick:a[27]||(a[27]=e=>l.playerAction("sneak_attack"))},"偷袭"),e.createElementVNode("button",{class:"player-action-btn give",onClick:a[28]||(a[28]=e=>l.playerAction("give"))},"给与"),e.createElementVNode("button",{class:"player-action-btn steal",onClick:a[29]||(a[29]=e=>l.playerAction("steal"))},"偷窃"),e.createElementVNode("button",{class:"player-action-btn view",onClick:a[30]||(a[30]=e=>l.playerAction("view"))},"查看")])])])):e.createCommentVNode("",!0),l.displayMapNpcs.length>0?(e.openBlock(),e.createElementBlock("view",{key:10,class:"npc-sidebar-btn",style:e.normalizeStyle({top:i.npcBtnTop}),onTouchstart:a[33]||(a[33]=(...e)=>t.onNpcBtnTouchStart&&t.onNpcBtnTouchStart(...e)),onTouchmove:a[34]||(a[34]=(...e)=>l.onNpcBtnTouchMove&&l.onNpcBtnTouchMove(...e)),onTouchend:a[35]||(a[35]=(...e)=>l.onNpcBtnTouchEnd&&l.onNpcBtnTouchEnd(...e)),onLongpress:a[36]||(a[36]=(...e)=>l.onNpcBtnLongPress&&l.onNpcBtnLongPress(...e)),onClick:a[37]||(a[37]=e=>i.npcSidebarVisible=!0)},[e.createElementVNode("text",null,"NPC")],36)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"player-sidebar-btn",style:e.normalizeStyle({top:i.playerBtnTop}),onTouchstart:a[38]||(a[38]=(...e)=>l.onPlayerBtnTouchStart&&l.onPlayerBtnTouchStart(...e)),onTouchmove:a[39]||(a[39]=(...e)=>l.onPlayerBtnTouchMove&&l.onPlayerBtnTouchMove(...e)),onTouchend:a[40]||(a[40]=(...e)=>l.onPlayerBtnTouchEnd&&l.onPlayerBtnTouchEnd(...e)),onLongpress:a[41]||(a[41]=(...e)=>l.onPlayerBtnLongPress&&l.onPlayerBtnLongPress(...e)),onClick:a[42]||(a[42]=(...e)=>l.showPlayerSidebar&&l.showPlayerSidebar(...e))},[e.createElementVNode("text",null,"侠客")],36),i.npcSidebarVisible?(e.openBlock(),e.createElementBlock("view",{key:11,class:"npc-sidebar-mask",onClick:a[43]||(a[43]=e=>i.npcSidebarVisible=!1)})):e.createCommentVNode("",!0),e.createElementVNode("view",{class:e.normalizeClass(["npc-sidebar",{"npc-sidebar-show":i.npcSidebarVisible}])},[e.createElementVNode("view",{class:"npc-sidebar-header"},[e.createElementVNode("text",null,"功能NPC"),e.createElementVNode("button",{class:"npc-sidebar-close",onClick:a[44]||(a[44]=e=>i.npcSidebarVisible=!1)},"×")]),e.createElementVNode("view",{class:"npc-sidebar-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.displayMapNpcs,(t=>(e.openBlock(),e.createElementBlock("view",{class:"npc-item",key:t.id,onClick:e=>l.showNpcMenu(t)},[e.createElementVNode("image",{class:"npc-avatar",src:t.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("text",{class:"npc-name"},e.toDisplayString(t&&t.name?t.name:""),1),t.desc?(e.openBlock(),e.createElementBlock("text",{key:0,class:"npc-desc"},e.toDisplayString(t.desc),1)):e.createCommentVNode("",!0)],8,["onClick"])))),128))])],2),i.playerSidebarVisible?(e.openBlock(),e.createElementBlock("view",{key:12,class:"player-sidebar-mask",onClick:a[45]||(a[45]=e=>i.playerSidebarVisible=!1)})):e.createCommentVNode("",!0),e.createElementVNode("view",{class:e.normalizeClass(["player-sidebar",{"player-sidebar-show":i.playerSidebarVisible}])},[e.createElementVNode("view",{class:"player-sidebar-header"},[e.createElementVNode("text",null,"在线侠客 ("+e.toDisplayString(i.mapPlayers.length)+")",1),e.createElementVNode("button",{class:"player-sidebar-close",onClick:a[46]||(a[46]=e=>i.playerSidebarVisible=!1)},"×")]),e.createElementVNode("view",{class:"player-sidebar-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.mapPlayers,(t=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["player-item",{"player-item-self":t.isSelf}]),key:t.id,onClick:e=>t.isSelf?null:l.showPlayerMenu(t)},[e.createElementVNode("image",{class:"player-avatar",src:t.avatar||"/static/npc/default.png",mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"player-info"},[e.createElementVNode("text",{class:e.normalizeClass(["player-name",{"player-name-self":t.isSelf}])},[e.createTextVNode(e.toDisplayString(t.name||t.character_name)+" ",1),t.isSelf?(e.openBlock(),e.createElementBlock("text",{key:0,class:"self-tag"},"（我）")):e.createCommentVNode("",!0)],2),e.createElementVNode("text",{class:"player-level"},"等级: "+e.toDisplayString(t.level||"未知"),1),e.createElementVNode("text",{class:e.normalizeClass(["player-status",t.status])},e.toDisplayString(l.getPlayerStatusText(t.status)),3)])],10,["onClick"])))),128)),0===i.mapPlayers.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-players"},[e.createElementVNode("text",null,"当前地图暂无侠客"),i.player?(e.openBlock(),e.createElementBlock("text",{key:0,class:"debug-info"},"调试：玩家数据存在 - "+e.toDisplayString(i.player.name||i.player.character_name||"未知"),1)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"debug-info"},"调试：玩家数据不存在"))])):e.createCommentVNode("",!0)])],2),e.createElementVNode("view",{class:"left-menu-btn",style:e.normalizeStyle({top:i.leftBtnTop}),onTouchstart:a[47]||(a[47]=(...e)=>l.onLeftBtnTouchStart&&l.onLeftBtnTouchStart(...e)),onTouchmove:a[48]||(a[48]=(...e)=>l.onLeftBtnTouchMove&&l.onLeftBtnTouchMove(...e)),onTouchend:a[49]||(a[49]=(...e)=>l.onLeftBtnTouchEnd&&l.onLeftBtnTouchEnd(...e)),onLongpress:a[50]||(a[50]=(...e)=>l.onLeftBtnLongPress&&l.onLeftBtnLongPress(...e)),onClick:a[51]||(a[51]=(...e)=>l.toggleLeftMenu&&l.toggleLeftMenu(...e))},[e.createElementVNode("text",null,"快捷")],36),e.createElementVNode("view",{class:"left-chat-btn",style:e.normalizeStyle({top:i.chatBtnTop}),onTouchstart:a[52]||(a[52]=(...e)=>l.onChatBtnTouchStart&&l.onChatBtnTouchStart(...e)),onTouchmove:a[53]||(a[53]=(...e)=>l.onChatBtnTouchMove&&l.onChatBtnTouchMove(...e)),onTouchend:a[54]||(a[54]=(...e)=>l.onChatBtnTouchEnd&&l.onChatBtnTouchEnd(...e)),onLongpress:a[55]||(a[55]=(...e)=>l.onChatBtnLongPress&&l.onChatBtnLongPress(...e)),onClick:a[56]||(a[56]=(...e)=>l.openChat&&l.openChat(...e))},[e.createElementVNode("text",null,"💬")],36),i.leftMenuVisible?(e.openBlock(),e.createElementBlock("view",{key:13,class:"left-menu-bar",style:e.normalizeStyle({top:i.leftBtnTop}),onClick:a[61]||(a[61]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("button",{class:"left-menu-btn-item",onClick:a[57]||(a[57]=e=>l.onMenuClick("healing"))},[e.createElementVNode("text",{class:"left-menu-btn-icon"},"🩺"),e.createElementVNode("text",{class:"left-menu-btn-text"},"疗伤")]),e.createElementVNode("button",{class:"left-menu-btn-item",onClick:a[58]||(a[58]=e=>l.onMenuClick("meditation"))},[e.createElementVNode("text",{class:"left-menu-btn-icon"},"🧘"),e.createElementVNode("text",{class:"left-menu-btn-text"},"打坐")]),e.createElementVNode("button",{class:"left-menu-btn-item",onClick:a[59]||(a[59]=e=>l.onMenuClick("ranking"))},[e.createElementVNode("text",{class:"left-menu-btn-icon"},"🏆"),e.createElementVNode("text",{class:"left-menu-btn-text"},"排行榜")]),e.createElementVNode("button",{class:"left-menu-btn-item",onClick:a[60]||(a[60]=e=>l.onMenuClick("redeem"))},[e.createElementVNode("text",{class:"left-menu-btn-icon"},"🎁"),e.createElementVNode("text",{class:"left-menu-btn-text"},"兑换码")])],4)):e.createCommentVNode("",!0),i.leftMenuVisible?(e.openBlock(),e.createElementBlock("view",{key:14,class:"left-menu-mask",onClick:a[62]||(a[62]=e=>i.leftMenuVisible=!1)})):e.createCommentVNode("",!0)]),i.showRankingModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-overlay",onClick:a[68]||(a[68]=e=>i.showRankingModal=!1)},[e.createElementVNode("view",{class:"ranking-modal",onClick:a[67]||(a[67]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"🏆 排行榜"),e.createElementVNode("text",{class:"modal-close",onClick:a[63]||(a[63]=e=>i.showRankingModal=!1)},"×")]),e.createElementVNode("view",{class:"ranking-tabs"},[e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"wealth"===i.rankingCurrentTab}]),onClick:a[64]||(a[64]=e=>l.switchRankingTab("wealth"))},[e.createElementVNode("text",null,"💰 富豪榜")],2),e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"experience"===i.rankingCurrentTab}]),onClick:a[65]||(a[65]=e=>l.switchRankingTab("experience"))},[e.createElementVNode("text",null,"⭐ 经验榜")],2),e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"adventure"===i.rankingCurrentTab}]),onClick:a[66]||(a[66]=e=>l.switchRankingTab("adventure"))},[e.createElementVNode("text",null,"🗡️ 肝帝榜")],2)]),e.createElementVNode("view",{class:"ranking-content"},[i.rankingLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-text"},"加载中...")):0===i.rankingList.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-text"},"暂无排行数据")):(e.openBlock(),e.createElementBlock("view",{key:2,class:"ranking-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.rankingList,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:t.id||a,class:e.normalizeClass(["ranking-item",{"current-player":t.isCurrentPlayer,"top-rank":a<3,"first-place":0===a,"second-place":1===a,"third-place":2===a}])},[e.createElementVNode("view",{class:e.normalizeClass(["rank-number",{"top-three":a<3}])},[0===a?(e.openBlock(),e.createElementBlock("text",{key:0,class:"rank-icon gold"},"🥇")):1===a?(e.openBlock(),e.createElementBlock("text",{key:1,class:"rank-icon silver"},"🥈")):2===a?(e.openBlock(),e.createElementBlock("text",{key:2,class:"rank-icon bronze"},"🥉")):(e.openBlock(),e.createElementBlock("text",{key:3,class:"rank-text"},e.toDisplayString(a+1),1))],2),e.createElementVNode("view",{class:"player-info"},[e.createElementVNode("text",{class:"player-name"},e.toDisplayString(t.name),1)]),e.createElementVNode("view",{class:"ranking-value"},["wealth"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:0},e.toDisplayString(l.formatMoney(t.money)),1)):"experience"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:1},e.toDisplayString(l.formatNumber(t.experience)),1)):"adventure"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:2},e.toDisplayString(l.formatNumber(t.adventureCount))+"次",1)):e.createCommentVNode("",!0)])],2)))),128))]))]),i.myRanking?(e.openBlock(),e.createElementBlock("view",{key:0,class:"my-ranking"},[e.createElementVNode("text",{class:"my-ranking-title"},"我的排名：第"+e.toDisplayString(i.myRanking.rank)+"名",1),e.createElementVNode("text",{class:"my-ranking-value"},["wealth"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:0},e.toDisplayString(l.formatMoney(i.myRanking.money)),1)):"experience"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:1},e.toDisplayString(l.formatNumber(i.myRanking.experience)),1)):"adventure"===i.rankingCurrentTab?(e.openBlock(),e.createElementBlock("text",{key:2},e.toDisplayString(l.formatNumber(i.myRanking.adventureCount))+"次",1)):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showRedeemModal?(e.openBlock(),e.createElementBlock("view",{key:1,class:"modal-overlay",onClick:a[73]||(a[73]=e=>i.showRedeemModal=!1)},[e.createElementVNode("view",{class:"redeem-modal",onClick:a[72]||(a[72]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"🎁 兑换码"),e.createElementVNode("text",{class:"modal-close",onClick:a[69]||(a[69]=e=>i.showRedeemModal=!1)},"×")]),e.createElementVNode("view",{class:"redeem-input-section"},[e.withDirectives(e.createElementVNode("input",{class:"redeem-input","onUpdate:modelValue":a[70]||(a[70]=e=>i.redeemCode=e),placeholder:"请输入兑换码",disabled:i.redeemLoading},null,8,["disabled"]),[[e.vModelText,i.redeemCode]]),e.createElementVNode("button",{class:e.normalizeClass(["redeem-btn",{disabled:!i.redeemCode.trim()||i.redeemLoading}]),onClick:a[71]||(a[71]=(...e)=>l.submitRedeemCode&&l.submitRedeemCode(...e)),disabled:!i.redeemCode.trim()||i.redeemLoading},[i.redeemLoading?(e.openBlock(),e.createElementBlock("text",{key:0},"兑换中...")):(e.openBlock(),e.createElementBlock("text",{key:1},"兑换"))],10,["disabled"])]),e.createElementVNode("view",{class:"redeem-tips"},[e.createElementVNode("text",{class:"tips-item"},"• 每个兑换码只能使用一次"),e.createElementVNode("text",{class:"tips-item"},"• 兑换码有有效期限制"),e.createElementVNode("text",{class:"tips-item"},"• 请确保输入正确的兑换码")])])])):e.createCommentVNode("",!0)],64)}],["__scopeId","data-v-1146e9d0"]]);const c=n({data:()=>({isLoading:!0,loadingText:"正在加载角色数据...",playerData:{character_name:"",name:"",gender:"男",level:1,hp:0,max_hp:0,mp:0,max_mp:0,energy:0,max_energy:0,spirit:0,max_spirit:0,experience:0,money:0,talent:{},dodge:1,crit:1,fortune:1,equipment:{},inventory_capacity:50,realm_info:{current_realm:"初出茅庐",next_realm:"不堪一击",current_min:0,current_max:5,progress:0,experience:0},skill_points:0,current_map:null},sectInfo:{has_sect:!1,sect_name:"无门派",rank_name:"",contribution:0},inventoryData:[],activeTab:"attributes",isExpanding:!1,bonusSummary:{},itemsConfig:{},mapsConfig:{},showTalentModal:!1,selectedTalent:{},showEquipmentModal:!1,selectedEquipment:{},selectedSlot:""}),computed:{mainEquipments(){const e=this.playerData.equipment||{};try{return JSON.parse(JSON.stringify({main_hand:e.main_hand||null,off_hand:e.off_hand||null,armor:e.armor||null,helmet:e.helmet||null,necklace:e.necklace||null,ring1:e.ring1||null,ring2:e.ring2||null,medal:e.medal||null}))}catch(a){return t("error","at pages/character/character.vue:382","装备数据解析失败:",a),{main_hand:null,off_hand:null,armor:null,helmet:null,necklace:null,ring1:null,ring2:null,medal:null}}},talentArr(){var e,t,a,s;return[{label:"力量",value:(null==(e=this.playerData.talent)?void 0:e.力量)??0},{label:"悟性",value:(null==(t=this.playerData.talent)?void 0:t.悟性)??0},{label:"身法",value:(null==(a=this.playerData.talent)?void 0:a.身法)??0},{label:"根骨",value:(null==(s=this.playerData.talent)?void 0:s.根骨)??0},{label:"富源",value:this.playerData.fortune??1}]},talentArrNoFortune(){var e,t,a,s;return[{label:"力量",value:(null==(e=this.playerData.talent)?void 0:e.力量)??0},{label:"悟性",value:(null==(t=this.playerData.talent)?void 0:t.悟性)??0},{label:"身法",value:(null==(a=this.playerData.talent)?void 0:a.身法)??0},{label:"根骨",value:(null==(s=this.playerData.talent)?void 0:s.根骨)??0}]},currentMapName(){if(!this.mapsConfig||!l.player.current_map)return"未知";const e=this.mapsConfig[l.player.current_map];return e?e.name:"未知"},energyRegenDetails(){return this.playerData.energy_regen_details||null}},onLoad(){this.setupEventListeners(),setTimeout((async()=>{await this.loadMapsConfigSafe(),this.initGameState()}),100),this.loadItemsConfig()},onShow(){a.isConnected?!a.isAuthed&&a.autoAuthenticate&&(a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData()}),500)):a.connect().then((()=>{!a.isAuthed&&a.autoAuthenticate&&a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData()}),500)})),this.updatePlayerData(),this.fetchBonusSummary(),l.getPlayer()||(a.isConnected&&l.isAuthed?l.requestAllData():this.initGameState())},onUnload(){this.cleanupEventListeners(),l.offUpdate(this.handleStateUpdate)},methods:{async initGameState(){try{this.isLoading=!0,this.loadingText="正在初始化游戏状态...",l.onUpdate(this.handleStateUpdate),await l.init(),l.isAuthed&&l.requestAllData(),setTimeout((()=>{this.isLoading&&(this.isLoading=!1,uni.showToast({title:"数据加载超时，请重试",icon:"none"}))}),1e4)}catch(e){this.isLoading=!1,t("error","at pages/character/character.vue:500","初始化游戏状态失败:",e),uni.showToast({title:"初始化失败: "+e.message,icon:"none"})}},updatePlayerData(){const e=l.getPlayer();e?this.playerData={...e}:l.isAuthed&&l.requestAllData();const t=l.getInventory();t&&Array.isArray(t)?this.inventoryData=[...t]:this.inventoryData=[],this.loadSectInfo()},async loadSectInfo(){try{const e=await s.sendMessage({type:"sect_action",data:{action:"get_sect_info"}});"sect_action_success"===e.type&&"get_sect_info"===e.data.action&&e.data.has_sect?this.sectInfo={has_sect:!0,sect_name:e.data.sect_name,rank_name:e.data.rank_name,contribution:e.data.contribution}:this.sectInfo={has_sect:!1,sect_name:"无门派",rank_name:"",contribution:0}}catch(e){t("error","at pages/character/character.vue:565","获取门派信息失败:",e),this.sectInfo={has_sect:!1,sect_name:"无门派",rank_name:"",contribution:0}}},handleStateUpdate(e,a){switch(e){case"player":a.player&&(this.playerData={...a.player},this.isLoading=!1,this.playerData.max_mp||0===this.playerData.max_mp||t("warn","at pages/character/character.vue:583","max_mp 为空或undefined"),this.playerData.max_energy||0===this.playerData.max_energy||t("warn","at pages/character/character.vue:586","max_energy 为空或undefined"),this.playerData.attack||0===this.playerData.attack||t("warn","at pages/character/character.vue:589","attack 为空或undefined"),this.playerData.defense||0===this.playerData.defense||t("warn","at pages/character/character.vue:592","defense 为空或undefined"),this.checkDataCompleteness());break;case"inventory":a.inventory&&Array.isArray(a.inventory)?this.inventoryData=[...a.inventory]:this.inventoryData=[]}},checkDataCompleteness(){["max_hp","max_mp","max_energy","max_spirit","attack","defense","dodge","crit"].filter((e=>{const t=this.playerData[e];return null==t||""===t})).length>0&&setTimeout((()=>{l.requestAllData()}),1e3)},formatMoney:e=>e>=1e12?(e/1e12).toFixed(1)+"万亿":e>=1e8?(e/1e8).toFixed(1)+"亿":e>=1e4?(e/1e4).toFixed(1)+"万":e>=1e3?e.toLocaleString():e.toString(),formatExperience:e=>e>=1e12?(e/1e12).toFixed(1)+"万亿":e>=1e8?(e/1e8).toFixed(1)+"亿":e>=1e4?(e/1e4).toFixed(1)+"万":e>=1e3?e.toLocaleString():e.toString(),setupEventListeners(){this.cleanupEventListeners(),this._errorHandler=e=>this.handleError(e),this._authSuccessHandler=e=>this.handleAuthSuccess(e),this._unequipSuccessHandler=e=>this.handleUnequipSuccess(e),this._unequipFailedHandler=e=>this.handleUnequipFailed(e),this._equipSuccessHandler=e=>this.handleEquipSuccess(e),this._equipFailedHandler=e=>this.handleEquipFailed(e),this._healingSuccessHandler=e=>this.handleHealingSuccess(e),this._healingFailedHandler=e=>this.handleHealingFailed(e),this._expandSuccessHandler=e=>this.handleExpandSuccess(e),this._expandFailedHandler=e=>this.handleExpandFailed(e),this._breakthroughSuccessHandler=e=>this.handleBreakthroughSuccess(e),this._breakthroughFailedHandler=e=>this.handleBreakthroughFailed(e)},cleanupEventListeners(){},async fetchBonusSummary(){try{const e=await s.sendMessage({type:"get_bonus_summary",data:{}});"bonus_summary"===e.type&&(this.bonusSummary=e.data)}catch(e){t("warn","at pages/character/character.vue:685","获取增益摘要失败",e)}},handleAuthSuccess(e){l.isAuthed&&l.requestAllData()},handleError(e){this.isLoading=!1,t("error","at pages/character/character.vue:698","角色页面收到错误:",e);const a=e.message||"网络错误，请重试";uni.showToast({title:a,icon:"none"})},handleEquipmentClick(e){const t=this.mainEquipments[e];t&&t.id?(this.selectedEquipment={...t},this.selectedSlot=e,this.showEquipmentModal=!0):uni.showToast({title:`${this.getSlotLabel(e)}为空`,icon:"none"})},closeEquipmentModal(){this.showEquipmentModal=!1,this.selectedEquipment={},this.selectedSlot=""},unequipSelectedEquipment(){if(this.selectedSlot){const e=this.selectedSlot;this.closeEquipmentModal(),this.unequipItem(e)}},getTypeText:e=>({weapon:"武器",helmet:"头盔",necklace:"项链",armor:"衣服",cloak:"披风",pants:"裤子",shoes:"鞋子",bracelet:"手镯",ring:"戒指",shield:"盾牌",medal:"勋章",accessory:"饰品"}[e]||e),getQualityText:e=>({common:"普通",uncommon:"优秀",rare:"稀有",epic:"史诗",legendary:"传说",mythic:"神话"}[e]||"普通"),getEquipmentEffects(e){if(!e)return[];const t=[],a={attack:"攻击",defense:"防御",hp:"气血",mp:"内力",energy:"精力",energy_regen:"精力回复",crit:"暴击",dodge:"闪避",hit:"命中",speed:"速度"};for(const[n,i]of Object.entries(a)){const a=e[n]||e[i];a&&a>0&&t.push({name:i,value:a})}const s=e.effects;if(s&&"string"==typeof s){const e=s.split(",");for(const s of e){const[e,n]=s.split(":");if(e&&n){const s=e.trim(),i=parseInt(n.trim()),l=a[s]||s;if(i>0){const e=t.find((e=>e.name===l));e?e.value+=i:t.push({name:l,value:i})}}}}return t},selectEquipment(e){const t=this.equipment[e];t?uni.showModal({title:t.name,content:`类型：${t.type}\n品质：${t.quality}\n攻击：${t.attack||0}\n防御：${t.defense||0}`,showCancel:!0,cancelText:"取消",confirmText:"卸下",success:t=>{t.confirm&&this.unequipItem(e)}}):uni.showToast({title:"该槽位为空",icon:"none"})},selectInventoryItem(e){const t=this.inventoryData[e];if(t){["weapon","helmet","necklace","armor","cloak","pants","shoes","bracelet","ring"].includes(t.type)?uni.showActionSheet({itemList:["装备","使用","取消"],success:t=>{0===t.tapIndex?this.equipItem(e):1===t.tapIndex&&this.useItem(e)}}):uni.showModal({title:t.name,content:`类型：${t.type}\n品质：${t.quality}\n数量：${t.quantity}`,showCancel:!0,cancelText:"取消",confirmText:"使用",success:t=>{t.confirm&&this.useItem(e)}})}},async unequipItem(e){try{this.isLoading=!0,this.loadingText="正在卸下装备...";const a=await s.sendMessage({type:"unequip",data:{slot:e}});this.isLoading=!1,"unequip_success"===a.type?(uni.showToast({title:a.data.message||"卸下装备成功",icon:"success"}),this.updatePlayerData()):"unequip_failed"===a.type?(t("error","at pages/character/character.vue:914","卸下装备失败:",a.data),uni.showToast({title:a.data.message||"卸下装备失败",icon:"none"})):"error"===a.type&&this.handleError(a.data)}catch(a){t("error","at pages/character/character.vue:924","卸下装备失败:",a),this.isLoading=!1,uni.showToast({title:"卸下装备失败: "+(a.message||"未知错误"),icon:"none"})}},async equipItem(e){const t=this.inventoryData[e];if(!t)return;const a={weapon:["main_hand","off_hand"],helmet:["helmet"],necklace:["necklace"],armor:["armor"],cloak:["cloak"],pants:["pants"],shoes:["shoes"],ring:["ring1","ring2"],off_hand:["off_hand"],shield:["off_hand"],medal:["medal"]}[t.type]||[];if(0!==a.length)if(a.length>1){const t={main_hand:"主手",off_hand:"副手",ring1:"戒指1",ring2:"戒指2"},s=a.map((e=>t[e]||e));uni.showActionSheet({itemList:s,success:t=>{const s=a[t.tapIndex];this.doEquipItem(e,s)}})}else this.doEquipItem(e,a[0]);else uni.showToast({title:"无法装备此物品",icon:"none"})},async doEquipItem(e,s){try{this.isLoading=!0,this.loadingText="正在装备...",a.sendMessage("equip_item",{item_index:e,slot_type:s})}catch(n){t("error","at pages/character/character.vue:995","装备失败:",n),this.isLoading=!1,uni.showToast({title:"装备失败: "+n.message,icon:"none"})}},useItem(e){},async handleHealing(){try{this.isLoading=!0,this.loadingText="正在疗伤...",a.sendMessage("healing",{})}catch(e){t("error","at pages/character/character.vue:1018","疗伤失败:",e),this.isLoading=!1,uni.showToast({title:"疗伤失败: "+e.message,icon:"none"})}},handleHealingSuccess(e){this.isLoading=!1,uni.showToast({title:"疗伤成功",icon:"success"}),this.updatePlayerData()},handleHealingFailed(e){this.isLoading=!1,t("error","at pages/character/character.vue:1039","疗伤失败:",e),uni.showToast({title:e.message||"疗伤失败",icon:"none"})},handleUnequipSuccess(e){this.isLoading=!1,uni.showToast({title:e.message||"卸下装备成功",icon:"success"}),this.updatePlayerData()},handleUnequipFailed(e){this.isLoading=!1,t("error","at pages/character/character.vue:1058","卸下装备失败:",e),uni.showToast({title:e.message||"卸下装备失败",icon:"none"})},handleEquipSuccess(e){this.isLoading=!1,uni.showToast({title:e.message||"装备成功",icon:"success"}),this.updatePlayerData()},handleEquipFailed(e){this.isLoading=!1,t("error","at pages/character/character.vue:1077","装备失败:",e),uni.showToast({title:e.message||"装备失败",icon:"none"})},async expandInventory(){try{this.isLoading=!0,this.loadingText="正在扩充背包...";const e=await s.sendMessage({type:"expand_inventory",data:{}});this.isLoading=!1,"expand_success"===e.type?(uni.showToast({title:e.data.message||"扩充背包成功",icon:"success"}),e.data.capacity&&(this.inventoryCapacity=e.data.capacity),this.updatePlayerData()):"expand_failed"===e.type?(t("error","at pages/character/character.vue:1109","扩充背包失败:",e.data),uni.showToast({title:e.data.message||"扩充背包失败",icon:"none"})):"error"===e.type&&this.handleError(e.data)}catch(e){t("error","at pages/character/character.vue:1119","扩充背包失败:",e),this.isLoading=!1,uni.showToast({title:"扩充背包失败: "+(e.message||"未知错误"),icon:"none"})}},handleExpandSuccess(e){this.isLoading=!1,uni.showToast({title:e.message||"扩充背包成功",icon:"success"}),e.capacity&&(this.inventoryCapacity=e.capacity),this.updatePlayerData()},handleExpandFailed(e){this.isLoading=!1,t("error","at pages/character/character.vue:1144","扩充背包失败:",e),uni.showToast({title:e.message||"扩充背包失败",icon:"none"})},handleBreakthroughSuccess(e){this.isLoading=!1,uni.showToast({title:e.message||"境界突破成功！",icon:"success"}),this.updatePlayerData()},handleBreakthroughFailed(e){this.isLoading=!1,t("error","at pages/character/character.vue:1163","境界突破失败:",e),uni.showToast({title:e.message||"境界突破失败",icon:"none"})},handleCrafting(){uni.navigateTo({url:"/pages/crafting/crafting"})},handleBreakthrough(){uni.showModal({title:"境界突破",content:"是否尝试突破当前境界？需要消耗大量历练值。",showCancel:!0,cancelText:"取消",confirmText:"突破",success:e=>{e.confirm&&this.attemptBreakthrough()}})},async attemptBreakthrough(){try{this.isLoading=!0,this.loadingText="正在突破境界...";const e=await s.sendMessage({type:"realm_breakthrough",data:{}});this.isLoading=!1,"breakthrough_success"===e.type?(uni.showToast({title:e.data.message||"境界突破成功！",icon:"success"}),this.updatePlayerData()):"breakthrough_failed"===e.type?(t("error","at pages/character/character.vue:1214","境界突破失败:",e.data),uni.showToast({title:e.data.message||"境界突破失败",icon:"none"})):"error"===e.type&&this.handleError(e.data)}catch(e){t("error","at pages/character/character.vue:1224","境界突破失败:",e),this.isLoading=!1,uni.showToast({title:"境界突破失败: "+(e.message||"未知错误"),icon:"none"})}},handleBackpack(){uni.navigateTo({url:"/pages/character/backpack"})},getItemQualityClass(e){if(!e||!e.quality)return"quality-normal";switch(e.quality){case"common":return"quality-common";case"uncommon":return"quality-uncommon";case"rare":return"quality-rare";case"epic":return"quality-epic";case"legendary":return"quality-legendary";default:return"quality-normal"}},switchTab(e){this.activeTab=e},handleItemClick(e,t){},getTalentLabel:e=>({"力量":"力量","悟性":"悟性","身法":"身法","根骨":"根骨"}[e]||e),getSlotLabel:e=>({main_hand:"主手",off_hand:"副手",armor:"衣服",helmet:"头盔",necklace:"项链",ring1:"戒指1",ring2:"戒指2",medal:"勋章"}[e]||e),getRealmProgressPercent(){const e=this.playerData.realm_info;if(!e||!e.next_min)return 0;const t=e.progress||0,a=e.next_min||0,s=e.current_max||0,n=a-s;if(n<=0)return 100;const i=t-s,l=Math.min(100,Math.max(0,i/n*100));return Math.round(l)},async loadItemsConfig(){this.itemsConfig=await l.getItemsConfig()},async loadMapsConfigSafe(){try{this.mapsConfig=await l.getMapsConfig()}catch(e){t("error","at pages/character/character.vue:1310","地图信息加载失败",e),uni.showToast({title:"地图信息错误",icon:"none"})}},showTalentDetail(e){this.selectedTalent=e,this.showTalentModal=!0},closeTalentModal(){this.showTalentModal=!1,this.selectedTalent={}},getConstitutionBonus:()=>0,getConstitutionHpBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.constitution)?void 0:a.hp_bonus_percentage)||0},getConstitutionHpBonusValue(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.constitution)?void 0:a.hp_bonus)||0},getCurrentEnergyRegen(){var e;if(this.energyRegenDetails)return this.energyRegenDetails.final_energy_regen.toFixed(2);const t=this.playerData.energy_regen_rate||.1,a=(null==(e=this.playerData.talent)?void 0:e.根骨)||15;return(t*(1+Math.min(1,.02*(a-15))*Math.log(a/15+1)/Math.log(2))).toFixed(2)},getStrengthBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.bonus_percentage)||0},getStrengthAttackBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.strength)?void 0:a.attack_bonus)||0},getIntelligenceBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.intelligence)?void 0:a.bonus_percentage)||0},getIntelligenceMultiplier(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.intelligence)?void 0:a.exp_multiplier)||1},getAgilityDefenseBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.defense_bonus_percentage)||0},getAgilityDodgeBonus(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.dodge_bonus_percentage)||0},getAgilityDefenseBonusValue(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.defense_bonus)||0},getAgilityDodgeBonusValue(){var e,t,a;return(null==(a=null==(t=null==(e=this.playerData)?void 0:e.talent_bonuses)?void 0:t.agility)?void 0:a.dodge_bonus)||0}}},[["render",function(t,a,s,n,i,l){var o,c,r,d,m,u;return e.openBlock(),e.createElementBlock("view",{class:"character-compact"},[i.isLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-overlay"},[e.createElementVNode("view",{class:"loading-content"},[e.createElementVNode("text",{class:"loading-text"},e.toDisplayString(i.loadingText),1)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"header-section"},[e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("image",{class:"avatar",src:"/static/logo.png"})]),e.createElementVNode("view",{class:"info-section"},[e.createElementVNode("view",{class:"name-row"},[e.createElementVNode("text",{class:"name"},e.toDisplayString(i.playerData.character_name||i.playerData.name||"未知"),1),e.createElementVNode("text",{class:"gender"},e.toDisplayString(i.playerData.gender||"男"),1),e.createElementVNode("text",{class:"experience"},"📈 历练 "+e.toDisplayString(l.formatExperience(i.playerData.experience||0)),1),e.createElementVNode("text",{class:"fortune"},"💎 富源 "+e.toDisplayString(i.playerData.fortune||1),1),e.createElementVNode("text",{class:"sect-info"},"🏛️ "+e.toDisplayString(i.sectInfo.sect_name),1)]),e.createElementVNode("view",{class:"stats-row"},[e.createElementVNode("text",{class:"money"},"💰 "+e.toDisplayString(l.formatMoney(i.playerData.money||0)),1),e.createElementVNode("text",{class:"skill-points"},"🎯 武学 "+e.toDisplayString(i.playerData.skill_points||0),1),e.createElementVNode("text",{class:"realm"},"⚔️ "+e.toDisplayString((null==(o=i.playerData.realm_info)?void 0:o.current_realm)||"初出茅庐"),1)])])]),e.createElementVNode("view",{class:"attributes-grid"},[e.createElementVNode("text",{class:"section-title"},"📊 基础属性"),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"气血"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.max_hp||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"内力"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.max_mp||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"体力"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.max_energy||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"精力"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.max_spirit||i.playerData.max_energy||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"攻击"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.attack||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"防御"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(i.playerData.defense||0)),1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"闪避"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString((i.playerData.dodge||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"attr-item"},[e.createElementVNode("text",{class:"attr-label"},"暴击"),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString((i.playerData.crit||0).toFixed(1))+"%",1)])]),i.bonusSummary.total?(e.openBlock(),e.createElementBlock("view",{key:1,style:{background:"#e8f4ff",padding:"10rpx",margin:"10rpx 0","font-size":"22rpx",color:"#333"}},[e.createElementVNode("text",null,"装备加成：攻击"+e.toDisplayString(Math.floor(i.bonusSummary.equipment.attack||0))+"，防御"+e.toDisplayString(Math.floor(i.bonusSummary.equipment.defense||0))+"，气血"+e.toDisplayString(Math.floor(i.bonusSummary.equipment.hp||0)),1),e.createElementVNode("br"),e.createElementVNode("text",null,"武功加成：攻击"+e.toDisplayString(Math.floor(i.bonusSummary.martial.attack||0))+"，防御"+e.toDisplayString(Math.floor(i.bonusSummary.martial.defense||0))+"，气血"+e.toDisplayString(Math.floor(i.bonusSummary.martial.hp||0)),1),e.createElementVNode("br"),e.createElementVNode("text",null,"总加成：攻击"+e.toDisplayString(Math.floor(i.bonusSummary.total.attack||0))+"，防御"+e.toDisplayString(Math.floor(i.bonusSummary.total.defense||0))+"，气血"+e.toDisplayString(Math.floor(i.bonusSummary.total.hp||0)),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"talent-section"},[e.createElementVNode("text",{class:"section-title"},"天赋属性"),e.createElementVNode("view",{class:"talent-list",style:{display:"flex","flex-direction":"row","justify-content":"space-between","align-items":"center"}},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.talentArrNoFortune,(t=>(e.openBlock(),e.createElementBlock("view",{class:"attr-item",style:{flex:"1 1 0","text-align":"center"},key:t.label,onClick:e=>l.showTalentDetail(t)},[e.createElementVNode("text",{class:"attr-label"},e.toDisplayString(t.label),1),e.createElementVNode("text",{class:"attr-value"},e.toDisplayString(Math.floor(t.value||0)),1)],8,["onClick"])))),128))])]),i.showTalentModal?(e.openBlock(),e.createElementBlock("view",{key:2,class:"talent-modal-mask",onClick:a[3]||(a[3]=(...e)=>l.closeTalentModal&&l.closeTalentModal(...e))},[e.createElementVNode("view",{class:"talent-modal-content",onClick:a[2]||(a[2]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"talent-modal-header"},[e.createElementVNode("text",{class:"talent-modal-title"},e.toDisplayString(i.selectedTalent.label)+"详情",1),e.createElementVNode("text",{class:"talent-modal-close",onClick:a[0]||(a[0]=(...e)=>l.closeTalentModal&&l.closeTalentModal(...e))},"×")]),e.createElementVNode("view",{class:"talent-modal-body"},[e.createElementVNode("view",{class:"talent-current"},[e.createElementVNode("text",{class:"talent-current-label"},"当前"+e.toDisplayString(i.selectedTalent.label)+"：",1),e.createElementVNode("text",{class:"talent-current-value"},e.toDisplayString(Math.floor(i.selectedTalent.value||0)),1)]),"根骨"===i.selectedTalent.label?(e.openBlock(),e.createElementBlock("view",{key:0,class:"talent-effects"},[e.createElementVNode("text",{class:"talent-effects-title"},"根骨增益效果："),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"气血加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getConstitutionHpBonus()||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"实际提升："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getConstitutionHpBonusValue()||0).toFixed(1))+"点",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"体力恢复加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString(Math.floor(l.getConstitutionBonus()||0))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"基础根骨值："),e.createElementVNode("text",{class:"effect-value"},"15点")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"每点根骨增加："),e.createElementVNode("text",{class:"effect-value"},"0.5%气血，2%恢复速度")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"最大加成："),e.createElementVNode("text",{class:"effect-value"},"100%")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"当前恢复速度："),e.createElementVNode("text",{class:"effect-value"},e.toDisplayString(Number(l.getCurrentEnergyRegen()||0).toFixed(1))+"/10秒",1)])])):"力量"===i.selectedTalent.label?(e.openBlock(),e.createElementBlock("view",{key:1,class:"talent-effects"},[e.createElementVNode("text",{class:"talent-effects-title"},"力量增益效果："),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"攻击力加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getStrengthBonus()||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"基础力量值："),e.createElementVNode("text",{class:"effect-value"},"15点")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"每点力量增加："),e.createElementVNode("text",{class:"effect-value"},"0.3%攻击力")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"实际提升："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getStrengthAttackBonus()||0).toFixed(1))+"点",1)])])):"悟性"===i.selectedTalent.label?(e.openBlock(),e.createElementBlock("view",{key:2,class:"talent-effects"},[e.createElementVNode("text",{class:"talent-effects-title"},"悟性增益效果："),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"经验获取加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getIntelligenceBonus()||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"基础悟性值："),e.createElementVNode("text",{class:"effect-value"},"15点")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"每点悟性增加："),e.createElementVNode("text",{class:"effect-value"},"0.5%经验获取")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"经验倍率："),e.createElementVNode("text",{class:"effect-value"},e.toDisplayString((l.getIntelligenceMultiplier()||1).toFixed(2))+"x",1)])])):"身法"===i.selectedTalent.label?(e.openBlock(),e.createElementBlock("view",{key:3,class:"talent-effects"},[e.createElementVNode("text",{class:"talent-effects-title"},"身法增益效果："),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"防御力加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getAgilityDefenseBonus()||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"实际提升："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getAgilityDefenseBonusValue()||0).toFixed(1))+"点",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"闪避加成："),e.createElementVNode("text",{class:"effect-value"},"+"+e.toDisplayString((l.getAgilityDodgeBonus()||0).toFixed(1))+"%",1)]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"基础身法值："),e.createElementVNode("text",{class:"effect-value"},"15点")]),e.createElementVNode("view",{class:"talent-effect-item"},[e.createElementVNode("text",{class:"effect-label"},"每点身法增加："),e.createElementVNode("text",{class:"effect-value"},"0.5%防御力，0.5%闪避")])])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"talent-modal-footer"},[e.createElementVNode("button",{class:"talent-modal-btn",onClick:a[1]||(a[1]=(...e)=>l.closeTalentModal&&l.closeTalentModal(...e))},"关闭")])])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"realm-section"},[e.createElementVNode("view",{class:"realm-header"},[e.createElementVNode("text",{class:"realm-title"},"下一境界："+e.toDisplayString((null==(c=i.playerData.realm_info)?void 0:c.next_realm)||"不堪一击"),1),e.createElementVNode("text",{class:"realm-progress"},e.toDisplayString(l.formatExperience(i.playerData.experience||0))+" / "+e.toDisplayString(l.formatExperience((null==(r=i.playerData.realm_info)?void 0:r.current_max)||0)),1)]),e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("view",{class:"progress-fill",style:e.normalizeStyle({width:l.getRealmProgressPercent()+"%"})},null,4)]),e.createElementVNode("text",{class:"realm-bonus"},e.toDisplayString((null==(m=null==(d=i.playerData.realm_info)?void 0:d.next_bonus)?void 0:m.description)||"无增益"),1)]),e.createElementVNode("view",{class:"equipment-section"},[e.createElementVNode("text",{class:"section-title"},"装备"),e.createElementVNode("view",{class:"equipment-grid"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["main_hand","off_hand","helmet","armor","necklace","ring1","ring2","medal"],(t=>e.createElementVNode("view",{key:t,class:"equip-slot",onClick:e=>l.handleEquipmentClick(t)},[l.mainEquipments[t]&&l.mainEquipments[t].id?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createElementVNode("text",{class:"equip-icon"},e.toDisplayString(l.mainEquipments[t].icon||"📦"),1),e.createElementVNode("text",{class:"equip-name"},e.toDisplayString(l.mainEquipments[t].name),1)],64)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"empty-text"},e.toDisplayString(l.getSlotLabel(t)),1))],8,["onClick"]))),64))])]),i.showEquipmentModal?(e.openBlock(),e.createElementBlock("view",{key:3,class:"equipment-modal-mask",onClick:a[8]||(a[8]=(...e)=>l.closeEquipmentModal&&l.closeEquipmentModal(...e))},[e.createElementVNode("view",{class:"equipment-modal-content",onClick:a[7]||(a[7]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"equipment-modal-header"},[e.createElementVNode("text",{class:"equipment-modal-title"},e.toDisplayString(i.selectedEquipment.name),1),e.createElementVNode("text",{class:"equipment-modal-close",onClick:a[4]||(a[4]=(...e)=>l.closeEquipmentModal&&l.closeEquipmentModal(...e))},"×")]),e.createElementVNode("view",{class:"equipment-modal-body"},[e.createElementVNode("view",{class:"equipment-info"},[e.createElementVNode("view",{class:"equipment-basic"},[e.createElementVNode("text",{class:"equipment-icon"},e.toDisplayString(i.selectedEquipment.icon||"📦"),1),e.createElementVNode("view",{class:"equipment-details"},[e.createElementVNode("text",{class:"equipment-type"},"类型："+e.toDisplayString(l.getTypeText(i.selectedEquipment.type)),1),e.createElementVNode("text",{class:"equipment-quality"},"品质："+e.toDisplayString(l.getQualityText(i.selectedEquipment.quality||i.selectedEquipment.品质)),1),e.createElementVNode("text",{class:"equipment-slot"},"槽位："+e.toDisplayString(l.getSlotLabel(i.selectedSlot)),1)])]),e.createElementVNode("view",{class:"equipment-stats"},[e.createElementVNode("text",{class:"stats-title"},"战斗属性"),l.getEquipmentEffects(i.selectedEquipment).length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"equipment-effects"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getEquipmentEffects(i.selectedEquipment),(t=>(e.openBlock(),e.createElementBlock("view",{key:t.name,class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},e.toDisplayString(t.name)+"：",1),e.createElementVNode("text",{class:"stat-value"},"+"+e.toDisplayString(t.value),1)])))),128))])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-effects"},[e.createElementVNode("text",{class:"no-effects-text"},"无属性加成")]))]),i.selectedEquipment.description||i.selectedEquipment.描述?(e.openBlock(),e.createElementBlock("view",{key:0,class:"equipment-description"},[e.createElementVNode("text",{class:"description-title"},"描述"),e.createElementVNode("text",{class:"description-text"},e.toDisplayString(i.selectedEquipment.description||i.selectedEquipment.描述),1)])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"equipment-modal-actions"},[e.createElementVNode("button",{class:"equipment-action-btn unequip-btn",onClick:a[5]||(a[5]=(...e)=>l.unequipSelectedEquipment&&l.unequipSelectedEquipment(...e))},[e.createElementVNode("text",null,"卸下装备")]),e.createElementVNode("button",{class:"equipment-action-btn close-btn",onClick:a[6]||(a[6]=(...e)=>l.closeEquipmentModal&&l.closeEquipmentModal(...e))},[e.createElementVNode("text",null,"关闭")])])])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"action-section"},[e.createElementVNode("button",{class:"action-btn healing-btn",onClick:a[9]||(a[9]=(...e)=>l.handleHealing&&l.handleHealing(...e))},[e.createElementVNode("text",null,"🏥 疗伤")]),e.createElementVNode("button",{class:"action-btn crafting-btn",onClick:a[10]||(a[10]=(...e)=>l.handleCrafting&&l.handleCrafting(...e))},[e.createElementVNode("text",null,"⚒️ 打造")])]),e.createElementVNode("view",{class:"action-section"},[e.createElementVNode("button",{class:"action-btn breakthrough-btn",onClick:a[11]||(a[11]=(...e)=>l.handleBreakthrough&&l.handleBreakthrough(...e)),disabled:i.playerData.experience<((null==(u=i.playerData.realm_info)?void 0:u.current_max)||0)},[e.createElementVNode("text",null,"🌟 境界突破")],8,["disabled"]),e.createElementVNode("button",{class:"action-btn backpack-btn",onClick:a[12]||(a[12]=(...e)=>l.handleBackpack&&l.handleBackpack(...e))},[e.createElementVNode("text",null,"🎒 背包")])])])}],["__scopeId","data-v-d565aa3a"]]);const r=n({data:()=>({player:{},selectedMartials:{},martialConfigs:[],martialTypes:[{key:"剑法",label:"剑法"},{key:"刀法",label:"刀法"},{key:"空手",label:"空手"},{key:"招架",label:"招架"},{key:"轻功",label:"轻功"},{key:"内功",label:"内功"},{key:"暗器",label:"暗器"}],activeTab:"equip",showDetail:!1,showMovesModal:!1,selectedSkill:null,isAuthed:!1,showMartialSelect:!1,martialSelectType:"",martialSelectTypeLabel:"",bonusSummary:{},trainResultMsg:"",showTrainResultModal:!1,trainLog:[],trainLogScrollTop:0,showTrainLoading:!1,trainLogTimeout:null,trainFinished:!1}),computed:{martialSkillsByType(){if(!this.player||!this.player.martial_skills)return{};const e={};return this.player.martial_skills.forEach((t=>{const a=t.type||t.类型||t.类别||"其他";e[a]||(e[a]=[]),e[a].push(t),"是"===t["是否可招架"]&&(e["招架"]||(e["招架"]=[]),e["招架"].push(t))})),e},learnedMartials(){return this.player&&this.player.martial_skills?this.player.martial_skills.filter((e=>{const t=e.name||e.名称||"",a=this.isLifeSkill(e)||t.includes("读书")||t.includes("写字");return(e.unlocked||e.解锁)&&!a})):[]},lifeSkills(){return this.player&&this.player.martial_skills?this.player.martial_skills.filter((e=>{const t=e.name||e.名称||"";return(e.unlocked||e.解锁)&&(this.isLifeSkill(e)||t.includes("读书")||t.includes("写字"))})):[]},learnedMartialCount(){return this.learnedMartials.length}},onLoad(){this.isAuthed=l.isAuthenticated?l.isAuthenticated():l.isAuthed,this.loadMartialSkills(),this.loadMartialConfigs(),this.fetchBonusSummary(),this._gameStateCallback||(this._gameStateCallback=(e,t)=>{"player"===e&&this.loadMartialSkills()},l.updateCallbacks.push(this._gameStateCallback))},onUnload(){if(this._gameStateCallback){const e=l.updateCallbacks.indexOf(this._gameStateCallback);e>=0&&l.updateCallbacks.splice(e,1),this._gameStateCallback=null}!this.showTrainLoading&&this.showTrainResultModal},onHide(){!this.showTrainLoading&&this.showTrainResultModal},onShow(){a.isConnected?!a.isAuthed&&a.autoAuthenticate&&(a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData()}),500)):a.connect().then((()=>{!a.isAuthed&&a.autoAuthenticate&&a.autoAuthenticate(),setTimeout((()=>{l.requestAllData&&l.requestAllData()}),500)})),this.isAuthed=l.isAuthenticated?l.isAuthenticated():l.isAuthed,this.loadMartialSkills(),this.loadMartialConfigs(),this.fetchBonusSummary()},mounted(){void 0!==a&&a.on&&a.on("train_martial_log",(e=>{e&&e.message&&(this.appendTrainLog(e.message),this.resetTrainLogTimeout(),(e.message.includes("修炼总结")||e.message.includes("修炼结束"))&&(this.showTrainLoading=!1,this.trainFinished=!0,this.clearTrainLogTimeout()))})),uni.$on&&uni.$on("ws_reconnected",this.handleReconnectCloseAllPopups)},methods:{async loadMartialSkills(){this.player=Object.assign({},l.getPlayerData()),this.player&&this.player.martial_skills&&!Array.isArray(this.player.martial_skills)&&(this.player.martial_skills=Object.values(this.player.martial_skills)),this.syncSelectedMartials(),this.updateData()},updateData(){this.player||(this.player={}),this.$forceUpdate()},convertMartialData(e){const t=[];return Object.keys(e).forEach((a=>{(e[a]||[]).forEach((e=>{t.push({...e,"类别":this.getSkillCategory(e.名称||e.name),"门派":this.getSkillSchool(e.名称||e.name),"等级":this.getSkillLevel(e.名称||e.name),"描述":this.getSkillDescription(e.名称||e.name),"效果":this.getSkillEffects(e.名称||e.name)})}))})),t},getMartialsByType(e){if(!this.player||!this.player.martial_skills)return[];let t=this.martialSkillsByType[e]||[];return Array.isArray(t)||"object"!=typeof t||(t=Object.values(t)),"招架"===e&&(t=t.filter((e=>"是"===e["是否可招架"]||!0===e.is_blockable))),t.filter((e=>(e.unlocked||e.解锁)&&!(e.name||e.名称||"").startsWith("基本")))},getSelectedMartialIndex(e){const t=this.selectedMartials[e];if(!t)return 0;const a=this.getMartialsByType(e).findIndex((e=>e.name===t));return a>=0?a:0},getSelectedMartialName(e){const t=this.selectedMartials[e];if(!t)return"未装备";const a=this.getMartialsByType(e).find((e=>e.name===t));return a?a.name:"未装备"},getSelectedMartial(e){const t=this.selectedMartials[e];if(!t)return null;return this.getMartialsByType(e).find((e=>e.name===t))||null},isMartialEquipped(e){if(!e)return!1;return!(!e.equipped&&!e.装备)},onMartialSelect(e,t){const a=e.detail.value,s=this.getMartialsByType(t);s[a]&&(this.selectedMartials[t]=s[a].name)},async fetchPlayerDataFromServer(){const e=await s.sendMessage({type:"get_player_data",data:{}});"player_data"===e.type&&(l.setPlayerData(e.data),this.updateData(),setTimeout((()=>{this.$forceUpdate()}),50))},async equipMartial(e,a){if(this.isAuthed)if(a.unlocked)try{const e=this.isMartialEquipped(a)?"unequip_martial":"use_martial",n=a.name||a.名称,i=await s.sendMessage({type:e,data:{skill_name:n}});i&&i.type===e+"_success"?(await this.fetchPlayerDataFromServer(),this.syncSelectedMartials(),this.updateData(),this.$forceUpdate(),await this.fetchBonusSummary(),uni.showToast({title:i.data.message||"装备成功",icon:"success"})):(t("warn","at pages/skills/skills.vue:541","装备失败:",i&&i.data&&i.data.message),uni.showToast({title:i&&i.data&&i.data.message||"操作失败",icon:"none"}))}catch(n){uni.showToast({title:"装备失败",icon:"none"})}else uni.showToast({title:"该武学尚未解锁",icon:"none"});else uni.showToast({title:"请先登录",icon:"none"})},async unequipMartial(e){if(!this.isAuthed)return void uni.showToast({title:"请先登录",icon:"none"});const a=this.getSelectedMartial(e);if(a)if(this.isMartialEquipped(a))try{const e=a.name||a.名称;uni.showLoading({title:"正在卸载...",mask:!0});const t=await s.sendMessage({type:"unequip_martial",data:{skill_name:e}});if(uni.hideLoading(),t&&"unequip_martial_success"===t.type){if(t.data.player){t.data.player.martial_skills&&!Array.isArray(t.data.player.martial_skills)&&(t.data.player.martial_skills=Object.keys(t.data.player.martial_skills).map((e=>({name:e,...t.data.player.martial_skills[e]})))),this.player=Object.assign({},t.data.player),l.player=t.data.player,await this.fetchPlayerDataFromServer(),this.syncSelectedMartials(),this.updateData();const a=this.player.martial_skills.find((t=>t.name===e||t.名称===e));a&&(a.equipped=!1,void 0!==a.装备&&(a.装备=!1)),this.updateData(),await this.fetchBonusSummary(),setTimeout((()=>{this.$forceUpdate()}),50),uni.showToast({title:t.data.message||`成功卸下${e}`,icon:"success"})}}else uni.showToast({title:t&&t.data&&t.data.message||"卸下失败",icon:"none"})}catch(n){uni.hideLoading(),t("error","at pages/skills/skills.vue:638","卸载武功失败:",n),uni.showToast({title:"操作异常",icon:"none"})}else uni.showToast({title:"该武功未装备",icon:"none"});else uni.showToast({title:"未选择武功",icon:"none"})},getSkillCategory:e=>({"基本拳法":"拳法","太极拳":"拳法","基本剑法":"剑法","独孤九剑":"剑法","凌波微步":"轻功","神行百变":"轻功","九阳神功":"内功","九阴真经":"内功","降龙十八掌":"掌法","打狗棒法":"棍法","采药":"生活技能","伐木":"生活技能","挖矿":"生活技能","剥皮":"生活技能"}[e]||"其他"),getSkillSchool:e=>({"独孤九剑":"华山","太极拳":"武当","九阳神功":"少林","九阴真经":"古墓派","降龙十八掌":"丐帮","打狗棒法":"丐帮","凌波微步":"逍遥派","神行百变":"逍遥派"}[e]||"无门派"),getSkillLevel:e=>({"基本拳法":"基础","基本剑法":"基础","太极拳":"高级","独孤九剑":"绝学","凌波微步":"绝学","神行百变":"绝学","九阳神功":"绝学","九阴真经":"绝学","降龙十八掌":"绝学","打狗棒法":"绝学"}[e]||"基础"),getSkillDescription:e=>({"基本拳法":"最基础的拳法，习武之初必学","太极拳":"武当派绝学，以柔克刚","独孤九剑":"华山派绝学，破尽天下武功","凌波微步":"逍遥派绝学，身法如鬼魅","九阳神功":"少林派绝学，内力深厚","九阴真经":"古墓派绝学，内力深厚","降龙十八掌":"丐帮绝学，掌力雄浑","打狗棒法":"丐帮绝学，棒法精妙","采药":"采集草药，用于炼药","伐木":"砍伐树木，用于制作装备","挖矿":"挖掘矿石，用于制作装备","剥皮":"剥取动物皮毛，用于制作装备"}[e]||"武功描述待补充"),getSkillMoves:e=>({"基本拳法":["基本拳法1","基本拳法2","基本拳法3","基本拳法4","基本拳法5"],"太极拳":["白鹤亮翅","野马分鬃","搂膝拗步","倒卷肱","揽雀尾"],"独孤九剑":["总诀式","破剑式","破刀式","破枪式","破鞭式","破索式","破掌式","破箭式","破气式"],"凌波微步":["凌波微步1","凌波微步2","凌波微步3","凌波微步4"],"九阳神功":["九阳护体","九阳真气","九阳神功"],"九阴真经":["九阴护体","九阴真气","九阴神功"],"降龙十八掌":["亢龙有悔","飞龙在天","见龙在田","鸿渐于陆","潜龙勿用"],"打狗棒法":["棒打双犬","棒打狗头","棒打狗腿","棒打狗尾"],"采药":["采药1","采药2","采药3"],"伐木":["伐木1","伐木2","伐木3"],"挖矿":["挖矿1","挖矿2","挖矿3"],"剥皮":["剥皮1","剥皮2","剥皮3"]}[e]||[]),getMoveDescription(e,t){var a;return(null==(a={"基本拳法":{"基本拳法1":"对准敌人的胸口打出一拳！","基本拳法2":"双拳齐出，敌人连连后退！","基本拳法3":"对准敌人的腹部一拳！","基本拳法4":"对准敌人的头部一拳！","基本拳法5":"对准敌人的腿部一拳！"},"太极拳":{"白鹤亮翅":"一式「白鹤亮翅」，双手成白鹤亮翅之势，敌人连连后退！","野马分鬃":"一式「野马分鬃」，双手成野马分鬃之势，敌人连连后退！","搂膝拗步":"一式「搂膝拗步」，双手成搂膝拗步之势，敌人连连后退！","倒卷肱":"一式「倒卷肱」，双手成倒卷肱之势，敌人连连后退！","揽雀尾":"一式「揽雀尾」，双手成揽雀尾之势，敌人连连后退！"},"独孤九剑":{"总诀式":"一式「总诀式」，剑势如虹，敌人连连后退！","破剑式":"一式「破剑式」，专门破解剑法，敌人连连后退！","破刀式":"一式「破刀式」，专门破解刀法，敌人连连后退！","破枪式":"一式「破枪式」，专门破解枪法，敌人连连后退！","破鞭式":"一式「破鞭式」，专门破解鞭法，敌人连连后退！"}}[e])?void 0:a[t])||"招式描述待补充"},getSkillEffects(e){if(!e||"基础"===e.等级)return"暂无效果";const t=[],a=e.等级;return"拳法"===e.类别?(t.push(`攻击 +${Math.floor(1.5*a)}`),t.push(`力量 +${Math.floor(a/10)}`)):"剑法"===e.类别?(t.push(`攻击 +${Math.floor(2*a)}`),t.push(`悟性 +${Math.floor(a/10)}`)):"轻功"===e.类别?(t.push(`防御 +${Math.floor(1.5*a)}`),t.push(`闪避 +${Math.floor(1.2*a)}`),t.push(`身法 +${Math.floor(a/10)}`)):"内功"===e.类别&&(t.push(`气血 +${Math.floor(3*a)}`),t.push(`内力 +${Math.floor(2*a)}`),t.push(`根骨 +${Math.floor(a/10)}`)),t.length>0?t.join(", "):"暂无效果"},getSkillSpecialEffects(e){if(!e)return"";const t=e.名称||e.name,a=this.getMartialConfigByName(t);return a&&a["武功特效"]&&"无"!==a["武功特效"]?a["武功特效"]:""},getSkillMovesList(e){if(!e)return[];const t=e.名称||e.name,a=e.等级||e.level||0,s=this.getMartialConfigByName(t);return s&&s["招式列表"]?s["招式列表"].map((e=>({name:e["名称"]||e.name,unlock_level:e["解锁等级"]||e.unlock_level||0,attack:e["攻击"]||e.attack,defense:e["防御"]||e.defense,unlocked:a>=(e["解锁等级"]||e.unlock_level||0)}))):[]},getMartialConfigByName(e){if(!e||!this.martialConfigs||!Array.isArray(this.martialConfigs))return null;for(const t of this.martialConfigs)if(t["武功名"]===e||t.name===e)return t;return null},async loadMartialConfigs(){try{const e=await s.sendMessage({type:"get_martial_configs",data:{}});e&&"get_martial_configs_success"===e.type&&e.data?this.martialConfigs=e.data.configs||[]:this.martialConfigs=[]}catch(e){t("error","at pages/skills/skills.vue:857","前端加载武功配置失败:",e),this.martialConfigs=[]}},switchTab(e){this.activeTab=e},onLevelChange(e){this.levelIndex=e.detail.value},onSchoolChange(e){this.schoolIndex=e.detail.value},getCategoryTitle(){return"all"===this.activeTab?"全部武功":this.activeTab},getCategoryDescription(){return{all:"所有可学习的武功","拳法":"拳法类武功，以拳为主","剑法":"剑法类武功，以剑为主","轻功":"轻功类武功，提升身法","内功":"内功类武功，提升内力"}[this.activeTab]||"武功描述"},getSkillProgress:e=>0===e.最大经验?0:Math.min(e.经验/e.最大经验*100,100),canUseMartial:e=>"基础"!==e.等级&&e.解锁||e.unlocked,async toggleMartialUse(e){if(this.isAuthed)if(e.解锁||e.unlocked)try{const t=e.装备||e.equipped?"unequip_martial":"use_martial",a=e.名称||e.name,n=await s.sendMessage({type:t,data:{skill_name:a}});n.type===t+"_success"?(n.data.player?(n.data.player.martial_skills&&!Array.isArray(n.data.player.martial_skills)&&(n.data.player.martial_skills=Object.keys(n.data.player.martial_skills).map((e=>({name:e,...n.data.player.martial_skills[e]})))),this.player=Object.assign({},n.data.player),l.player=n.data.player,await this.fetchPlayerDataFromServer(),this.syncSelectedMartials(),this.updateData(),this.$forceUpdate()):await this.fetchPlayerDataFromServer(),await this.fetchBonusSummary(),this.$forceUpdate(),uni.showToast({title:n.data.message,icon:"success"})):uni.showToast({title:n.data.message,icon:"none"})}catch(a){t("error","at pages/skills/skills.vue:945","武学操作失败:",a),uni.showToast({title:"操作失败",icon:"none"})}else uni.showToast({title:"该武学尚未解锁",icon:"none"});else uni.showToast({title:"请先登录",icon:"none"})},async showSkillDetail(e){0===this.martialConfigs.length&&await this.loadMartialConfigs(),this.selectedSkill=e,this.showDetail=!0},closeDetail(){this.showDetail=!1,this.selectedSkill=null},showMoves(e){this.selectedSkill=e,this.showMovesModal=!0},closeMovesModal(){this.showMovesModal=!1,this.selectedSkill=null},onTrainMartialClick(){this.trainMartial(this.selectedSkill)},async trainMartial(e){if(this.isAuthed)if(e.解锁||e.unlocked)if(this.player.skill_points<=0)uni.showToast({title:"武学点不足",icon:"none"});else try{this.showTrainLoading=!0,this.showTrainResultModal||(this.trainLog=[],this.showTrainResultModal=!0),this.resetTrainLogTimeout(),s.sendMessage({type:"train_martial",data:{name:e.name||e.名称}})}catch(a){t("error","at pages/skills/skills.vue:1006","【调试】修炼异常",a),this.appendTrainLog("修炼失败"),uni.showToast({title:"修炼失败",icon:"none"}),this.showTrainLoading=!1,this.clearTrainLogTimeout()}else uni.showToast({title:"该武学尚未解锁",icon:"none"});else uni.showToast({title:"请先登录",icon:"none"})},isLifeSkill(e){const t=e.名称||e.name||"";return"生活技能"===e.类别||"生活类"===e.类别||t.includes("采")||t.includes("药")||t.includes("伐木")||t.includes("挖矿")||t.includes("剥皮")},openMartialSelect(e){this.martialSelectType=e;const t=this.martialTypes.find((t=>t.key===e));this.martialSelectTypeLabel=t?t.label:e,this.showMartialSelect=!0},closeMartialSelect(){this.showMartialSelect=!1},async selectMartial(e,a){if(this.selectedMartials[a]=e.name,this.showMartialSelect=!1,e.unlocked||e.解锁){if(this.isMartialEquipped(e))return;try{const t=e.name||e.名称;uni.showLoading({title:"正在装备...",mask:!0});const a=await s.sendMessage({type:"use_martial",data:{skill_name:t}});if(uni.hideLoading(),a&&"use_martial_success"===a.type){if(a.data.player){a.data.player.martial_skills&&!Array.isArray(a.data.player.martial_skills)&&(a.data.player.martial_skills=Object.keys(a.data.player.martial_skills).map((e=>({name:e,...a.data.player.martial_skills[e]})))),this.player=Object.assign({},a.data.player),l.player=a.data.player,await this.fetchPlayerDataFromServer(),this.syncSelectedMartials(),this.updateData();const e=this.player.martial_skills.find((e=>e.name===t||e.名称===t));e&&(e.equipped=!0,void 0!==e.装备&&(e.装备=!0)),this.updateData(),await this.fetchBonusSummary(),setTimeout((()=>{this.$forceUpdate()}),50),uni.showToast({title:a.data.message||`成功装备${t}`,icon:"success"})}}else uni.showToast({title:a&&a.data&&a.data.message||"装备失败",icon:"none"})}catch(n){uni.hideLoading(),t("error","at pages/skills/skills.vue:1110","装备武功失败:",n),uni.showToast({title:"操作异常",icon:"none"})}}else uni.showToast({title:"该武学尚未解锁",icon:"none"})},async fetchBonusSummary(){try{const e=await s.sendMessage({type:"get_bonus_summary",data:{}});"bonus_summary"===e.type&&(this.bonusSummary=e.data)}catch(e){t("warn","at pages/skills/skills.vue:1127","获取增益摘要失败",e)}},isBasicMartial(e){const t=["基本剑法","基本拳法","基本刀法","基本棍法","基本招架","基本轻功","基础内功","基础暗器","基本暗器法"];return t.includes(e.name)||t.includes(e.名称)},syncSelectedMartials(){this.selectedMartials={},this.player&&this.player.martial_skills&&this.player.martial_skills.forEach((e=>{if(e.equipped){const t=e.type||e.类型||e.类别||"其他";this.selectedMartials[t]=e.name}}))},getSkillLevelUpExp(e){if(e.maxExp)return e.maxExp;const t=this.isLifeSkill(e),a=e.quality||e.品质||"普通",s=e.level||e.等级||0;let n=50;return t?n=60:"稀有"===a?n=80:"绝世"===a?n=120:"传说"===a&&(n=200),n*Math.pow(s+1,2)},closeTrainResultModal(){this.showTrainResultModal=!1,this.trainLog=[],this.clearTrainLogTimeout()},appendTrainLog(e){this.trainLog.push(e),this.$nextTick((()=>{this.trainLogScrollTop=99999+this.trainLog.length}))},resetTrainLogTimeout(){this.clearTrainLogTimeout(),this.trainLogTimeout=setTimeout((()=>{this.trainFinished||(this.showTrainLoading=!1,this.appendTrainLog("【提示】修炼超时，请检查网络或稍后重试。"),uni.showToast({title:"修炼超时，请检查网络",icon:"none"}))}),1e4)},clearTrainLogTimeout(){this.trainLogTimeout&&(clearTimeout(this.trainLogTimeout),this.trainLogTimeout=null),this.trainFinished=!1},handleReconnectCloseAllPopups(){this.showSkillDetail=!1,this.showTrainResultModal=!1,this.showTrainLoading=!1,this.trainLog=[]}}},[["render",function(t,a,s,n,i,l){var o,c;return e.openBlock(),e.createElementBlock("view",{class:"container"},[i.showTrainLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"train-loading-mask"},[e.createElementVNode("view",{class:"train-loading-content"},[e.createElementVNode("view",{class:"train-spinner"}),e.createElementVNode("text",{class:"train-loading-text"},"正在修炼...")])])):e.createCommentVNode("",!0),i.trainResultMsg?(e.openBlock(),e.createElementBlock("view",{key:1,class:"train-result-msg",style:{background:"#fffbe6",color:"#ad6800",padding:"8px 12px","margin-bottom":"12px","border-radius":"6px","font-size":"14px"}},e.toDisplayString(i.trainResultMsg),1)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"category-tabs"},[e.createElementVNode("view",{class:e.normalizeClass(["tab-item",{active:"equip"===i.activeTab}]),onClick:a[0]||(a[0]=e=>l.switchTab("equip"))},[e.createElementVNode("text",{class:"tab-text"},"使用武功")],2),e.createElementVNode("view",{class:e.normalizeClass(["tab-item",{active:"skills"===i.activeTab}]),onClick:a[1]||(a[1]=e=>l.switchTab("skills"))},[e.createElementVNode("text",{class:"tab-text"},"武功技能")],2),e.createElementVNode("view",{class:e.normalizeClass(["tab-item",{active:"life"===i.activeTab}]),onClick:a[2]||(a[2]=e=>l.switchTab("life"))},[e.createElementVNode("text",{class:"tab-text"},"生活技能")],2)]),Object.values(l.martialSkillsByType).some((e=>e.length>0))&&"equip"===i.activeTab?(e.openBlock(),e.createElementBlock("view",{key:2},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.martialTypes,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.key,class:"skill-group"},[e.createElementVNode("view",{class:"skill-item"},[e.createElementVNode("view",{class:"skill-info"},[e.createElementVNode("view",{class:"title-selector-row"},[e.createElementVNode("text",{class:"group-title"},e.toDisplayString(t.label),1),e.createElementVNode("view",{class:"equip-selector"},[e.createElementVNode("button",{class:"martial-select-tag",onClick:e=>l.openMartialSelect(t.key),disabled:0===l.getMartialsByType(t.key).length},[e.createElementVNode("text",{class:"martial-select-tag-text"},e.toDisplayString(l.getSelectedMartialName(t.key)||(0===l.getMartialsByType(t.key).length?"未装备":"请选择武功"))+" "+e.toDisplayString(l.getSelectedMartial(t.key)&&l.isMartialEquipped(l.getSelectedMartial(t.key))?"(已装备)":""),1)],8,["onClick","disabled"]),l.getSelectedMartial(t.key)&&l.isMartialEquipped(l.getSelectedMartial(t.key))?(e.openBlock(),e.createElementBlock("button",{key:0,class:"martial-clear-btn",onClick:e=>l.unequipMartial(t.key)}," × ",8,["onClick"])):e.createCommentVNode("",!0)])])])])])))),128))])):"equip"===i.activeTab?(e.openBlock(),e.createElementBlock("view",{key:3,style:{"text-align":"center",color:"#aaa",padding:"32px 0"}},"暂无武功数据")):e.createCommentVNode("",!0),"skills"===i.activeTab?(e.openBlock(),e.createElementBlock("view",{key:4},[l.learnedMartials.length?(e.openBlock(),e.createElementBlock("view",{key:0},[e.createElementVNode("view",{class:"skill-group"},[e.createElementVNode("view",{class:"group-header"},[e.createElementVNode("text",{class:"group-title"},"已学会武功技能（"+e.toDisplayString(l.learnedMartialCount)+"）",1)]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.learnedMartials,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.名称||t.name,class:"skill-item"},[e.createElementVNode("view",{class:"skill-info"},[e.createElementVNode("text",{class:"skill-name"},e.toDisplayString(t.名称||t.name),1),e.createElementVNode("text",{class:"skill-level"},"Lv."+e.toDisplayString(t.等级||t.level),1),t.门派||t.school&&"无门派"!==t.school?(e.openBlock(),e.createElementBlock("text",{key:0,class:"skill-school"},e.toDisplayString(t.门派||t.school),1)):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"detail-btn",onClick:e=>l.showSkillDetail(t)},"详情",8,["onClick"])])))),128)),0===l.learnedMartials.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-skills"},"暂无已学会武功")):e.createCommentVNode("",!0)])])):(e.openBlock(),e.createElementBlock("view",{key:1,style:{"text-align":"center",color:"#aaa",padding:"32px 0"}},"暂无武功技能"))])):e.createCommentVNode("",!0),"life"===i.activeTab?(e.openBlock(),e.createElementBlock("view",{key:5},[l.lifeSkills.length?(e.openBlock(),e.createElementBlock("view",{key:0},[e.createElementVNode("view",{class:"skill-group"},[e.createElementVNode("view",{class:"group-header"},[e.createElementVNode("text",{class:"group-title"},"生活技能")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.lifeSkills,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.名称||t.name,class:"skill-item"},[e.createElementVNode("view",{class:"skill-info"},[e.createElementVNode("text",{class:"skill-name"},e.toDisplayString(t.名称||t.name),1),e.createElementVNode("text",{class:"skill-level"},"Lv."+e.toDisplayString(t.等级||t.level),1)]),e.createElementVNode("button",{class:"detail-btn",onClick:e=>l.showSkillDetail(t)},"详情",8,["onClick"])])))),128)),0===l.lifeSkills.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-skills"},"暂无生活技能")):e.createCommentVNode("",!0)])])):(e.openBlock(),e.createElementBlock("view",{key:1,style:{"text-align":"center",color:"#aaa",padding:"32px 0"}},"暂无生活技能"))])):e.createCommentVNode("",!0),i.showDetail?(e.openBlock(),e.createElementBlock("view",{key:6,class:"modal-overlay",onClick:a[7]||(a[7]=(...e)=>l.closeDetail&&l.closeDetail(...e))},[e.createElementVNode("view",{class:"modal-content",onClick:a[6]||(a[6]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"武学详情"),e.createElementVNode("text",{class:"modal-close",onClick:a[3]||(a[3]=(...e)=>l.closeDetail&&l.closeDetail(...e))},"×")]),i.selectedSkill?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-body"},[e.createElementVNode("view",{class:"detail-header"},[e.createElementVNode("text",{class:"detail-name"},e.toDisplayString(i.selectedSkill.名称||i.selectedSkill.name),1),e.createElementVNode("view",{class:"detail-status"},[e.createElementVNode("text",{class:"detail-level"},e.toDisplayString(i.selectedSkill.等级||i.selectedSkill.level)+"级",1),i.selectedSkill.门派||i.selectedSkill.school&&"无门派"!==i.selectedSkill.school?(e.openBlock(),e.createElementBlock("text",{key:0,class:"detail-school"},e.toDisplayString(i.selectedSkill.门派||i.selectedSkill.school),1)):e.createCommentVNode("",!0),i.selectedSkill.装备||i.selectedSkill.equipped?(e.openBlock(),e.createElementBlock("text",{key:1,class:"detail-equipped"},"已装备")):e.createCommentVNode("",!0)])]),e.createElementVNode("text",{class:"detail-desc"},e.toDisplayString(i.selectedSkill.描述||i.selectedSkill.description),1),i.selectedSkill.等级>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-progress"},[e.createElementVNode("text",{class:"progress-title"},"进度:"),e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("view",{class:"progress-bg"},[e.createElementVNode("view",{class:"progress-fill",style:e.normalizeStyle({width:l.getSkillProgress(i.selectedSkill)+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},e.toDisplayString(i.selectedSkill.经验||i.selectedSkill.exp)+"/"+e.toDisplayString(i.selectedSkill.最大经验||i.selectedSkill.maxExp),1)])])):e.createCommentVNode("",!0),i.selectedSkill.等级>0?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-effects"},[e.createElementVNode("text",{class:"effects-title"},"当前效果:"),e.createElementVNode("text",{class:"effects-text"},e.toDisplayString(l.getSkillEffects(i.selectedSkill)),1)])):e.createCommentVNode("",!0),l.getSkillSpecialEffects(i.selectedSkill)?(e.openBlock(),e.createElementBlock("view",{key:2,class:"detail-special-effects"},[e.createElementVNode("text",{class:"special-effects-title"},"武功特效:"),e.createElementVNode("text",{class:"special-effects-text"},e.toDisplayString(l.getSkillSpecialEffects(i.selectedSkill)),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"detail-category"},[e.createElementVNode("text",{class:"category-title"},"升级经验:"),e.createElementVNode("text",{class:"category-text"},e.toDisplayString(i.selectedSkill.exp||i.selectedSkill.经验||0)+" / "+e.toDisplayString(l.getSkillLevelUpExp(i.selectedSkill)),1)]),l.getSkillMovesList(i.selectedSkill).length>0?(e.openBlock(),e.createElementBlock("view",{key:3,class:"detail-moves"},[e.createElementVNode("text",{class:"moves-title"},"招式列表:"),e.createElementVNode("view",{class:"moves-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getSkillMovesList(i.selectedSkill),(t=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["move-item",{"move-unlocked":t.unlocked,"move-locked":!t.unlocked}]),key:t.name},[e.createElementVNode("view",{class:"move-header"},[e.createElementVNode("text",{class:"move-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:e.normalizeClass(["move-status-badge",{"status-unlocked":t.unlocked,"status-locked":!t.unlocked}])},e.toDisplayString(t.unlocked?"已解锁":"未解锁"),3)]),e.createElementVNode("view",{class:"move-details"},[e.createElementVNode("text",{class:"move-unlock-condition"},"解锁条件: "+e.toDisplayString(t.unlock_level)+"级",1),t.attack?(e.openBlock(),e.createElementBlock("text",{key:0,class:"move-attack"},"攻击力: +"+e.toDisplayString(t.attack),1)):e.createCommentVNode("",!0),t.defense?(e.openBlock(),e.createElementBlock("text",{key:1,class:"move-defense"},"防御力: +"+e.toDisplayString(t.defense),1)):e.createCommentVNode("",!0)])],2)))),128))])])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[4]||(a[4]=(...e)=>l.closeDetail&&l.closeDetail(...e))},"关闭"),i.selectedSkill.isConfig?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("button",{key:0,class:"modal-btn confirm-btn",onClick:a[5]||(a[5]=(...e)=>l.onTrainMartialClick&&l.onTrainMartialClick(...e)),disabled:!i.isAuthed||!i.selectedSkill.unlocked||i.player.skill_points<=0}," 修炼 ",8,["disabled"]))]),i.trainResultMsg?(e.openBlock(),e.createElementBlock("view",{key:1,style:{background:"#fffbe6",color:"#ad6800",padding:"6px 10px","margin-top":"8px","border-radius":"6px","font-size":"13px"}},e.toDisplayString(i.trainResultMsg),1)):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showMovesModal?(e.openBlock(),e.createElementBlock("view",{key:7,class:"modal-overlay",onClick:a[11]||(a[11]=(...e)=>l.closeMovesModal&&l.closeMovesModal(...e))},[e.createElementVNode("view",{class:"modal-content",onClick:a[10]||(a[10]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},e.toDisplayString((null==(o=i.selectedSkill)?void 0:o.名称)||(null==(c=i.selectedSkill)?void 0:c.name))+" - 招式",1),e.createElementVNode("text",{class:"modal-close",onClick:a[8]||(a[8]=(...e)=>l.closeMovesModal&&l.closeMovesModal(...e))},"×")]),i.selectedSkill?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-body"},[e.createElementVNode("view",{class:"moves-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.selectedSkill.招式||i.selectedSkill.moves,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"move-item",key:a},[e.createElementVNode("text",{class:"move-name"},e.toDisplayString(t),1),e.createElementVNode("text",{class:"move-desc"},e.toDisplayString(l.getMoveDescription(i.selectedSkill.名称||i.selectedSkill.name,t)),1)])))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[9]||(a[9]=(...e)=>l.closeMovesModal&&l.closeMovesModal(...e))},"关闭")])])])):e.createCommentVNode("",!0),i.showMartialSelect?(e.openBlock(),e.createElementBlock("view",{key:8,class:"martial-select-modal-mask",onClick:a[14]||(a[14]=(...e)=>l.closeMartialSelect&&l.closeMartialSelect(...e))},[e.createElementVNode("view",{class:"martial-select-modal",onClick:a[13]||(a[13]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"martial-select-title"},"选择"+e.toDisplayString(i.martialSelectTypeLabel)+"武功",1),e.createElementVNode("scroll-view",{class:"martial-select-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getMartialsByType(i.martialSelectType),((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:t.name,class:e.normalizeClass(["martial-select-item",{selected:t.name===i.selectedMartials[i.martialSelectType]}]),onClick:e=>l.selectMartial(t,i.martialSelectType)},[e.createElementVNode("view",{class:"martial-select-header"},[e.createElementVNode("text",{class:"martial-select-name"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"martial-select-status"},[e.createElementVNode("text",{class:"martial-select-level"},"Lv."+e.toDisplayString(t.level||t.等级||1),1),e.createElementVNode("text",{class:"martial-select-quality"},e.toDisplayString(t.quality||t.品质||"普通")+"品质",1),l.isMartialEquipped(t)?(e.openBlock(),e.createElementBlock("text",{key:0,class:"martial-select-equipped"},"已装备")):e.createCommentVNode("",!0)])]),e.createElementVNode("text",{class:"martial-select-desc"},e.toDisplayString(t.desc||t.描述||""),1)],10,["onClick"])))),128)),0===l.getMartialsByType(i.martialSelectType).length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"martial-select-empty"},"暂无可选武功")):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"martial-select-cancel",onClick:a[12]||(a[12]=(...e)=>l.closeMartialSelect&&l.closeMartialSelect(...e))},"取消")])])):e.createCommentVNode("",!0),i.showTrainResultModal?(e.openBlock(),e.createElementBlock("view",{key:9,class:"modal-overlay",style:{"z-index":"9999"}},[e.createElementVNode("view",{class:"modal-content",style:{"max-width":"420px","min-width":"320px"}},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"修炼日志"),e.createElementVNode("text",{class:"modal-close",onClick:a[15]||(a[15]=(...e)=>l.closeTrainResultModal&&l.closeTrainResultModal(...e))},"×")]),e.createElementVNode("scroll-view",{"scroll-y":"true","scroll-top":i.trainLogScrollTop,class:"modal-body train-log-scroll",style:{"max-height":"260px"}},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.trainLog,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,style:{color:"#ad6800","font-size":"14px","margin-bottom":"6px","line-height":"1.6","text-align":"left","word-break":"break-all","white-space":"normal"}},e.toDisplayString(t),1)))),128))],8,["scroll-top"]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn confirm-btn",onClick:a[16]||(a[16]=(...e)=>l.closeTrainResultModal&&l.closeTrainResultModal(...e))},"关闭")])])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-e53f09dd"]]);const d=n({data:()=>({money:0,myItems:[],currentShopType:"market",showDetail:!1,selectedItem:null,npcId:"changan_coalboss",shops:[{type:"market",name:"市场",goods:[]}],loading:!1,itemsConfig:{},marketList:[],showListModal:!1,listItemData:null,listPrice:"",listQuantity:1,listTab:"item",myMartials:[],listMartialData:null,showMyOrdersModal:!1,myOrderList:[]}),computed:{currentShop(){return this.shops[0]},filteredMyItems(){return this.myItems.map((e=>{const t=this.itemsConfig[e.id]||{};return{...e,...t}})).filter((e=>void 0===e.is_sellable&&void 0===e.sellable||!0===e.is_sellable||1===e.is_sellable||!0===e.sellable||1===e.sellable))}},async handleNpcShopInfo(e){try{t("log","at pages/shop/shop.vue:280","处理NPC商店信息:",e),uni.showToast({title:`进入${e.npc_name}的商店`,icon:"none",duration:2e3}),"buy"===e.tab?await this.fetchNpcShopItems(e.npc_name):"sell"===e.tab&&uni.showModal({title:e.npc_name,content:"请在背包中选择要出售的物品",showCancel:!1,success:()=>{uni.navigateTo({url:"/pages/character/backpack?mode=sell&npc="+encodeURIComponent(e.npc_name)})}})}catch(a){t("error","at pages/shop/shop.vue:308","处理NPC商店信息失败:",a),uni.showToast({title:"商店加载失败",icon:"none"})}},async fetchNpcShopItems(e){try{const a=await s.sendMessage({type:"npc_function",npc_name:e,function:"shop",data:{action:"list"}});a&&"shop_items"===a.type?(t("log","at pages/shop/shop.vue:327","获取到NPC商店物品:",a.data.items),this.showNpcShopItems(a.data.items,e)):(t("error","at pages/shop/shop.vue:332","获取NPC商店物品失败:",a),uni.showToast({title:"获取商店物品失败",icon:"none"}))}catch(a){t("error","at pages/shop/shop.vue:339","获取NPC商店物品异常:",a),uni.showToast({title:"网络错误",icon:"none"})}},showNpcShopItems(e,t){const a=e.map((e=>`${e.item_id}: ${e.price}银两 (库存:${e.stock})`));uni.showActionSheet({itemList:a.slice(0,6),success:async a=>{const s=e[a.tapIndex];await this.buyNpcItem(s,t)}})},async buyNpcItem(e,a){try{const t=await this.askQuantity(e.item_id,e.price);if(t<=0)return;const n=await s.sendMessage({type:"npc_function",npc_name:a,function:"shop",data:{action:"buy",item_id:e.item_id,quantity:t}});n&&"buy_success"===n.type?(uni.showToast({title:"购买成功",icon:"success"}),this.requestPlayerData(),this.updateMoney()):n&&"error"===n.type&&uni.showToast({title:n.data.message,icon:"none"})}catch(n){t("error","at pages/shop/shop.vue:398","购买NPC物品失败:",n),uni.showToast({title:"购买失败",icon:"none"})}},askQuantity:(e,t)=>new Promise((a=>{uni.showModal({title:"购买数量",content:`${e} 单价:${t}银两\n请输入购买数量:`,editable:!0,placeholderText:"1",success:e=>{if(e.confirm){const t=parseInt(e.content)||1;a(t>0?t:1)}else a(0)}})})),onLoad(){this.requestPlayerData(),this.updateMoney(),this.updateData(),this.fetchShopGoods("changan_coalboss"),this.loadItemsConfig(),this.refreshMarket()},onShow(){this.requestPlayerData(),this.updateMoney(),this.updateData(),this.refreshMarket()},methods:{async requestPlayerData(){try{const e=await s.sendMessage({type:"get_player_data",data:{}});e&&"player_data"===e.type&&e.data&&(l.player=e.data,"number"==typeof e.data.money&&(this.money=e.data.money,l.money=e.data.money),e.data.inventory&&(l.inventory=e.data.inventory,this.myItems=[...e.data.inventory]))}catch(e){t("error","at pages/shop/shop.vue:467","获取玩家数据失败:",e)}},updateData(){this.money=l.money,this.myItems=[...l.inventory]},formatNumber:e=>s.formatNumber(e),getQualityColor:e=>s.getQualityColor(e),getQualityName:e=>({common:"普通",uncommon:"精品",rare:"稀有",epic:"史诗",legendary:"传说",mythic:"仙品"}[e]||"普通"),getTypeName:e=>({weapon:"武器",armor:"护甲",necklace:"项链",bracelet:"手镯",mount:"坐骑",material:"材料",herb:"草药"}[e]||"未知"),getSellPrice:e=>(({common:10,uncommon:50,rare:200,epic:1e3,legendary:5e3,mythic:1e4}[e.quality]||10)*(e.quantity||1)),switchShop(e){this.currentShopType=e,this.npcId="equipment"===e?"changan_coalboss":"herb"===e?"changan_herbboss":"material"===e?"changan_materialboss":"",this.npcId&&this.fetchShopGoods(this.npcId)},showItemDetail(e){this.selectedItem=e,this.showDetail=!0},closeDetail(){this.showDetail=!1,this.selectedItem=null},async buyItem(e){if(l.isAuthed)if(this.money<e.price)uni.showToast({title:"银两不足",icon:"none"});else try{const t=e.type||"",a=!(void 0!==e.sellable&&!e.sellable),s=await this.$request({type:"shop_action",data:{action:"buy",item_id:e.id,quantity:1,type:t,sellable:a}});if(s&&"error"===s.type)return void uni.showToast({title:s.data.message||"购买失败",icon:"none",duration:3e3});uni.showToast({title:"购买成功",icon:"success"}),this.closeDetail()}catch(t){uni.showToast({title:"购买失败，请重试",icon:"none"})}else uni.showToast({title:"请先登录",icon:"none"})},sellMyItem(e){if(!l.isAuthed)return void uni.showToast({title:"请先登录",icon:"none"});const t=this.getSellPrice(e);uni.showModal({title:"确认出售",content:`确定要出售 ${e.name} 吗？\n获得: ${t} 银两`,success:a=>{a.confirm&&(l.money+=t,l.removeItem(e.id,e.quantity||1),this.updateData(),l.save(),uni.showToast({title:"出售成功",icon:"success"}))}})},async listItem(){try{await this.fetchPlayerInventory()}catch(e){}this.updateData(),this.listItemData=null,this.listPrice="",this.showListModal=!0},async fetchPlayerInventory(){const e=await s.sendMessage({type:"get_inventory_data"});let t;e&&e.data&&Array.isArray(e.data.inventory)?t=e.data.inventory:e&&Array.isArray(e.inventory)?t=e.inventory:e&&e.data&&Array.isArray(e.data)&&(t=e.data),Array.isArray(t)&&(l.inventory=t)},cancelListItem(){this.showListModal=!1,this.listItemData=null,this.listPrice="",this.listQuantity=1},increaseQuantity(){var e;const t=(null==(e=this.listItemData)?void 0:e.quantity)||1;this.listQuantity<t&&this.listQuantity++},decreaseQuantity(){this.listQuantity>1&&this.listQuantity--},async confirmListItem(){if(!this.listItemData||!this.listPrice||!this.listQuantity)return void uni.showToast({title:"请选择物品、输入价格和数量",icon:"none"});const e=this.listItemData.quantity||1;if(this.listQuantity>e)uni.showToast({title:`数量不能超过库存${e}`,icon:"none"});else if(this.listQuantity<1)uni.showToast({title:"数量不能小于1",icon:"none"});else{uni.showLoading({title:"上架中..."});try{const e=await s.sendMessage({type:"market_action",data:{action:"list",item_id:this.listItemData.id,price:Number(this.listPrice),quantity:Number(this.listQuantity)}});uni.hideLoading(),!e||"success"!==e.type&&"market_action_success"!==e.type?!e||"error"!==e.type&&"market_action_failed"!==e.type?e&&e.type&&e.type.includes("timeout")?(uni.showToast({title:"请求超时，请检查是否上架成功",icon:"none"}),this.showListModal=!1,this.listItemData=null,this.listPrice="",this.listQuantity=1,this.refreshDataAfterList()):(t("error","at pages/shop/shop.vue:734","未知响应格式:",e),uni.showToast({title:"上架失败，响应格式错误",icon:"none"})):uni.showToast({title:e.data.message||"上架失败",icon:"none"}):(uni.showToast({title:e.data.message||"上架成功",icon:"success"}),this.showListModal=!1,this.listItemData=null,this.listPrice="",this.listQuantity=1,this.refreshDataAfterList())}catch(a){uni.hideLoading(),t("error","at pages/shop/shop.vue:740","上架失败:",a),uni.showToast({title:"网络错误，请重试",icon:"none"}),this.showListModal=!1,this.listItemData=null,this.listPrice="",this.listQuantity=1}}},async refreshDataAfterList(){try{await Promise.all([this.refreshMarket(),this.requestPlayerData()])}catch(e){t("error","at pages/shop/shop.vue:760","刷新数据失败:",e)}},async refreshMarket(){const e=await s.sendMessage({type:"market_action",data:{action:"get_market_list"}}),t=e&&e.data&&e.data.list||[];this.marketList=t.map((e=>({...e,item:e.item||{name:"未知物品",quality:"common"}})))},async buyMarketItem(e){var a;try{const t=await s.sendMessage({type:"market_action",data:{action:"buy",order_id:Number(e.id)}});!t||"success"!==t.type&&"market_action_success"!==t.type?uni.showToast({title:(null==(a=null==t?void 0:t.data)?void 0:a.message)||"购买失败",icon:"none"}):(uni.showToast({title:t.data.message||"购买成功",icon:"success"}),await this.refreshMarket(),await this.requestPlayerData())}catch(n){t("error","at pages/shop/shop.vue:794","购买失败:",n),uni.showToast({title:"购买失败",icon:"none"})}},fetchShopGoods(e){this.loading=!0,s.sendMessage({type:"shop_action",data:{npc_id:e}}).then((e=>{e&&"shop_items"===e.type&&e.data&&e.data.items&&(this.shops[0].goods=e.data.items.map((e=>({id:e.id,name:e.name,description:e.desc,icon:e.icon,price:e.price,stock:e.stock,attack:e.attack,defense:e.defense,hp:e.hp,mp:e.mp,type:e.type,quality:e.quality})))),this.loading=!1})).catch((e=>{}))},updateMoney(){l.player&&"number"==typeof l.player.money?(this.money=l.player.money,l.money=l.player.money):"number"==typeof l.money?this.money=l.money:this.money=0},destroyed(){},async loadItemsConfig(){this.itemsConfig=await l.getItemsConfig()},selectListItem(e){this.listItemData=e,this.listPrice="",this.listQuantity=1},selectListMartial(e){this.listMartialData=e,this.listPrice=""},async fetchMyMartials(){const e=await s.sendMessage({type:"get_player_martials"});this.myMartials=e.martials||[]},async loadMyMartials(){await this.fetchMyMartials()},async showMyOrders(){await this.refreshMarket();const e=l.player||{},t=e.name||e.username||"";this.myOrderList=this.marketList.filter((e=>e.seller===t)).slice(0,10),this.showMyOrdersModal=!0},async unlinkOrder(e){try{uni.showModal({title:"确认下架",content:`确定要下架 ${e.item.name} 吗？物品将返还到背包。`,success:async t=>{var a,n;if(t.confirm){const t=await s.sendMessage({type:"market_action",data:{action:"unlist",order_id:e.id}});if(!t||"success"!==t.type&&"market_action_success"!==t.type)uni.showToast({title:(null==(n=null==t?void 0:t.data)?void 0:n.message)||"下架失败",icon:"none"});else{uni.showToast({title:t.data.message||"下架成功",icon:"success"}),await this.refreshMarket(),await this.requestPlayerData();const e=(null==(a=l.player)?void 0:a.name)||l.name||"未知玩家";this.myOrderList.length;this.myOrderList=this.marketList.filter((t=>t.seller===e)).slice(0,10)}}}})}catch(a){t("error","at pages/shop/shop.vue:914","下架失败:",a),uni.showToast({title:"下架失败",icon:"none"})}},getTimeRemaining(e){if(!e.expires_at)return 72;const t=Date.now()/1e3,a=(e.expires_at-t)/3600;return Math.max(0,a)},formatTimeRemaining(e){const t=this.getTimeRemaining(e);if(t<=0)return"已过期";if(t<1)return"不足1小时";if(t<24)return`${Math.floor(t)}小时`;return`${Math.floor(t/24)}天${Math.floor(t%24)}小时`}}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"money-info"},[e.createElementVNode("text",{class:"money-label"},"银两:"),e.createElementVNode("text",{class:"money-value"},e.toDisplayString(l.formatNumber(i.money)),1)]),e.createElementVNode("view",{class:"shop-type"},[e.createElementVNode("text",{class:"shop-name"},e.toDisplayString(l.currentShop.name),1)])]),"market"===i.currentShopType?(e.openBlock(),e.createElementBlock("view",{key:0,class:"market-section"},[e.createElementVNode("view",{class:"market-actions"},[e.createElementVNode("button",{class:"market-btn",onClick:a[0]||(a[0]=(...e)=>l.listItem&&l.listItem(...e))},[e.createElementVNode("text",{class:"btn-text"},"上架物品")]),e.createElementVNode("button",{class:"market-btn",onClick:a[1]||(a[1]=(...e)=>l.showMyOrders&&l.showMyOrders(...e))},[e.createElementVNode("text",{class:"btn-text"},"订单列表")])]),e.createElementVNode("scroll-view",{class:"order-list-scroll","scroll-y":"true"},[e.createElementVNode("view",{class:"order-list"},[0===i.marketList.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"market-empty"},"暂无玩家上架物品")):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.marketList,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"order-card"},[e.createElementVNode("image",{class:"item-img",src:t.item&&t.item.img?t.item.img:"/static/logo.png",mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"order-info"},[e.createElementVNode("view",{class:"item-name"},e.toDisplayString(t.item&&t.item.name?t.item.name:"未知物品"),1),e.createElementVNode("view",{class:"item-detail"},[e.createElementVNode("text",null,"单价："),e.createElementVNode("text",{class:"price"},e.toDisplayString(t.price),1),e.createElementVNode("text",null," 数量："),e.createElementVNode("text",null,e.toDisplayString(t.quantity),1)]),e.createElementVNode("view",{class:"seller"},"卖家："+e.toDisplayString(t.seller),1)]),e.createElementVNode("button",{class:"buy-btn",onClick:e=>l.buyMarketItem(t)},"购买",8,["onClick"])])))),128))])])])):(e.openBlock(),e.createElementBlock("scroll-view",{key:1,class:"goods-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.currentShop.goods,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"goods-item simple-row",key:a,onClick:e=>l.showItemDetail(t)},[e.createElementVNode("text",{class:"item-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"item-quality",style:e.normalizeStyle({color:l.getQualityColor(t.quality)})},e.toDisplayString(l.getQualityName(t.quality)),5),e.createElementVNode("text",{class:"item-type"},e.toDisplayString(t.type),1),e.createElementVNode("text",{class:"item-price main-price"},e.toDisplayString(t.price)+" 银两",1),e.createElementVNode("button",{class:"buy-btn",onClick:e.withModifiers((e=>l.buyItem(t)),["stop"])},"购买",8,["onClick"])],8,["onClick"])))),128)),0===l.currentShop.goods.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-goods"},[e.createElementVNode("text",null,"暂无商品")])):e.createCommentVNode("",!0)])),i.showDetail?(e.openBlock(),e.createElementBlock("view",{key:2,class:"modal-overlay",onClick:a[6]||(a[6]=(...e)=>l.closeDetail&&l.closeDetail(...e))},[e.createElementVNode("view",{class:"modal-content",onClick:a[5]||(a[5]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"商品详情"),e.createElementVNode("text",{class:"modal-close",onClick:a[2]||(a[2]=(...e)=>l.closeDetail&&l.closeDetail(...e))},"×")]),i.selectedItem?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-body"},[e.createElementVNode("text",{class:"detail-name"},e.toDisplayString(i.selectedItem.name),1),e.createElementVNode("text",{class:"detail-quality",style:e.normalizeStyle({color:l.getQualityColor(i.selectedItem.quality)})}," 品质: "+e.toDisplayString(l.getQualityName(i.selectedItem.quality)),5),e.createElementVNode("text",{class:"detail-type"},"类型: "+e.toDisplayString(i.selectedItem.type),1),i.selectedItem.description?(e.openBlock(),e.createElementBlock("text",{key:0,class:"detail-desc"},e.toDisplayString(i.selectedItem.description),1)):e.createCommentVNode("",!0),i.selectedItem.attack||i.selectedItem.defense||i.selectedItem.hp||i.selectedItem.mp?(e.openBlock(),e.createElementBlock("view",{key:1,class:"detail-stats"},[e.createElementVNode("text",{class:"stats-title"},"属性加成:"),i.selectedItem.attack?(e.openBlock(),e.createElementBlock("text",{key:0},"攻击: "+e.toDisplayString(i.selectedItem.attack),1)):e.createCommentVNode("",!0),i.selectedItem.defense?(e.openBlock(),e.createElementBlock("text",{key:1},"防御: "+e.toDisplayString(i.selectedItem.defense),1)):e.createCommentVNode("",!0),i.selectedItem.hp?(e.openBlock(),e.createElementBlock("text",{key:2},"气血: "+e.toDisplayString(i.selectedItem.hp),1)):e.createCommentVNode("",!0),i.selectedItem.mp?(e.openBlock(),e.createElementBlock("text",{key:3},"内力: "+e.toDisplayString(i.selectedItem.mp),1)):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"detail-price"},[e.createElementVNode("text",{class:"price-title"},"价格:"),e.createElementVNode("text",{class:"price-value"},e.toDisplayString(i.selectedItem.price)+" 银两",1)])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[3]||(a[3]=(...e)=>l.closeDetail&&l.closeDetail(...e))},"关闭"),e.createElementVNode("button",{class:"modal-btn confirm-btn",onClick:a[4]||(a[4]=e=>l.buyItem(i.selectedItem))}," 购买 ")])])])):e.createCommentVNode("",!0),i.showListModal?(e.openBlock(),e.createElementBlock("view",{key:3,class:"modal-overlay",onClick:a[15]||(a[15]=e=>i.showListModal=!1)},[e.createElementVNode("view",{class:"modal-content",onClick:a[14]||(a[14]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"上架物品"),e.createElementVNode("text",{class:"modal-close",onClick:a[7]||(a[7]=e=>i.showListModal=!1)},"×")]),e.createElementVNode("view",{class:"modal-body"},[i.listItemData?(e.openBlock(),e.createElementBlock("view",{key:1,class:"price-input-section"},[e.createElementVNode("view",{class:"selected-item-info"},[e.createElementVNode("text",{class:"selected-item-label"},"选中物品："),e.createElementVNode("text",{class:"selected-item-name"},e.toDisplayString(i.listItemData.name),1),e.createElementVNode("text",{class:"selected-item-quality",style:e.normalizeStyle({color:l.getQualityColor(i.listItemData.quality)})},e.toDisplayString(l.getQualityName(i.listItemData.quality)),5),e.createElementVNode("text",{class:"item-stock"},"库存："+e.toDisplayString(i.listItemData.quantity||1),1)]),e.createElementVNode("view",{class:"quantity-input-wrapper"},[e.createElementVNode("text",{class:"quantity-label"},"出售数量："),e.createElementVNode("view",{class:"quantity-controls"},[e.createElementVNode("button",{class:"quantity-btn",onClick:a[8]||(a[8]=(...e)=>l.decreaseQuantity&&l.decreaseQuantity(...e))},"-"),e.withDirectives(e.createElementVNode("input",{class:"quantity-input","onUpdate:modelValue":a[9]||(a[9]=e=>i.listQuantity=e),type:"number",max:i.listItemData.quantity||1,min:"1"},null,8,["max"]),[[e.vModelText,i.listQuantity]]),e.createElementVNode("button",{class:"quantity-btn",onClick:a[10]||(a[10]=(...e)=>l.increaseQuantity&&l.increaseQuantity(...e))},"+")])]),e.createElementVNode("view",{class:"price-input-wrapper"},[e.createElementVNode("text",{class:"price-label"},"单价（银两）："),e.withDirectives(e.createElementVNode("input",{class:"price-input","onUpdate:modelValue":a[11]||(a[11]=e=>i.listPrice=e),type:"number",placeholder:"请输入单价"},null,512),[[e.vModelText,i.listPrice]])]),i.listPrice&&i.listQuantity?(e.openBlock(),e.createElementBlock("view",{key:0,class:"total-price-info"},[e.createElementVNode("text",{class:"total-label"},"总价："),e.createElementVNode("text",{class:"total-value"},e.toDisplayString(i.listPrice*i.listQuantity||0)+" 银两",1)])):e.createCommentVNode("",!0)])):(e.openBlock(),e.createElementBlock("view",{key:0},[0===l.filteredMyItems.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-items"},"暂无可上架物品")):e.createCommentVNode("",!0),e.createElementVNode("scroll-view",{class:"list-select-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.filteredMyItems,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"list-select-item",onClick:e=>l.selectListItem(t)},[e.createElementVNode("text",{class:"item-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"item-quality",style:e.normalizeStyle({color:l.getQualityColor(t.quality)})},e.toDisplayString(l.getQualityName(t.quality)),5),t.quantity>1?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-quantity"},"x"+e.toDisplayString(t.quantity),1)):e.createCommentVNode("",!0)],8,["onClick"])))),128))])]))]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[12]||(a[12]=(...e)=>l.cancelListItem&&l.cancelListItem(...e))},"取消"),i.listItemData?(e.openBlock(),e.createElementBlock("button",{key:0,class:"modal-btn confirm-btn",onClick:a[13]||(a[13]=(...e)=>l.confirmListItem&&l.confirmListItem(...e))},"确认上架")):e.createCommentVNode("",!0)])])])):e.createCommentVNode("",!0),i.showMyOrdersModal?(e.openBlock(),e.createElementBlock("view",{key:4,class:"modal-overlay",onClick:a[19]||(a[19]=e=>i.showMyOrdersModal=!1)},[e.createElementVNode("view",{class:"modal-content",onClick:a[18]||(a[18]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"我的订单"),e.createElementVNode("text",{class:"modal-close",onClick:a[16]||(a[16]=e=>i.showMyOrdersModal=!1)},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("scroll-view",{class:"order-list-scroll","scroll-y":"true"},[e.createElementVNode("view",{class:"order-list"},[0===i.myOrderList.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"market-empty"},"暂无订单")):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.myOrderList,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"order-card"},[e.createElementVNode("image",{class:"item-img",src:t.item&&t.item.img?t.item.img:"/static/logo.png",mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"order-info"},[e.createElementVNode("view",{class:"item-name"},e.toDisplayString(t.item&&t.item.name?t.item.name:"未知物品"),1),e.createElementVNode("view",{class:"item-detail"},[e.createElementVNode("text",null,"单价："),e.createElementVNode("text",{class:"price"},e.toDisplayString(t.price),1),e.createElementVNode("text",null," 数量："),e.createElementVNode("text",null,e.toDisplayString(t.quantity),1)]),e.createElementVNode("view",{class:"seller"},"卖家："+e.toDisplayString(t.seller),1),e.createElementVNode("view",{class:e.normalizeClass(["order-time",{"time-warning":l.getTimeRemaining(t)<24}])}," 剩余: "+e.toDisplayString(l.formatTimeRemaining(t)),3)]),e.createElementVNode("button",{class:"unlist-btn",onClick:e=>l.unlinkOrder(t)},"下架",8,["onClick"])])))),128))])])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[17]||(a[17]=e=>i.showMyOrdersModal=!1)},"关闭")])])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-d8e93a94"]]);const m=n({data:()=>({player:{},playerGuild:null,sectInfo:null,showTasksSection:!1,showSkillsSection:!1,showMembersSection:!1,showShopSection:!1,showRankingsSection:!1,showWarsSection:!1,showBuildingsSection:!1,showGuildListModal:!1,showDeclareWarModal:!1,showTaskDetail:!1,selectedTask:null,availableGuilds:[],loading:!1,availableTasks:[],guildSkills:[],guildMembers:[],guildShopItems:[],sectRankings:[],currentRankingType:"power",sectWars:[],availableTargetSects:[],selectedTargetSectIndex:0,selectedTargetSect:null,warReason:"",sectBuildings:{},sectResources:{}}),onLoad(){this.updateData(),this.loadSectInfo()},onShow(){this.updateData(),this.loadSectInfo()},onReady(){setTimeout((()=>{this.loadSectInfo()}),500)},methods:{updateData(){this.player={...l.player}},async loadSectInfo(){if(l.isAuthed)try{this.loading=!0;const e=await s.sendMessage({type:"sect_action",data:{action:"get_sect_info"}});if(t("log","at pages/guild/guild.vue:587","[门派页面] 收到门派信息响应:",e),"sect_action_success"===e.type&&"get_sect_info"===e.data.action?(t("log","at pages/guild/guild.vue:590","[门派页面] 门派信息成功，has_sect:",e.data.has_sect),e.data.has_sect?(this.sectInfo=e.data,this.playerGuild={id:e.data.sect_id,name:e.data.sect_name,level:e.data.rank,contribution:e.data.contribution,position:e.data.rank_name},t("log","at pages/guild/guild.vue:600","[门派页面] 设置playerGuild:",this.playerGuild)):(this.sectInfo=null,this.playerGuild=null,t("log","at pages/guild/guild.vue:604","[门派页面] 玩家未加入门派"))):t("log","at pages/guild/guild.vue:607","[门派页面] 门派信息响应类型错误:",e.type),!this.playerGuild){const e=await s.sendMessage({type:"sect_action",data:{action:"get_available_sects"}});"available_sects_success"===e.type&&(this.availableGuilds=e.data.sects.map((e=>({id:e.sect_id,name:e.sect_name,description:e.description,requirement:e.requirements.level||1,can_join:e.can_join,reasons:e.reasons}))))}}catch(e){t("error","at pages/guild/guild.vue:630","加载门派信息失败:",e),uni.showToast({title:"加载门派信息失败",icon:"none"})}finally{this.loading=!1}},async claimDailyReward(){var e;if(l.isAuthed)try{const t=await s.sendMessage({type:"sect_action",data:{action:"claim_daily_reward"}});if("sect_action_success"===t.type&&"claim_daily_reward"===t.data.action){const e=t.data.rewards||{};let a="领取每日奖励成功！";const s=[];e.silver&&s.push(`银两 +${e.silver}`),e.exp&&s.push(`历练 +${e.exp}`),s.length>0&&(a+="\n获得："+s.join("，")),uni.showToast({title:a,icon:"success",duration:3e3}),await this.loadSectInfo(),this.updateData()}else uni.showToast({title:(null==(e=t.data)?void 0:e.message)||"领取失败",icon:"none"})}catch(a){t("error","at pages/guild/guild.vue:680","领取每日奖励失败:",a),uni.showToast({title:"领取失败",icon:"none"})}else uni.showToast({title:"请先登录",icon:"none"})},async loadSectSkills(){var e;if(l.isAuthed&&this.playerGuild)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_skills"}});"sect_action_success"===a.type&&"get_sect_skills"===a.data.action?this.guildSkills=a.data.skills.map((e=>({id:e.skill_name,name:e.skill_name,type:e.skill_type,quality:e.quality,weapon:e.weapon,description:`攻击+${e.attack} 防御+${e.defense} 内力+${e.internal_power}`,rank_requirement:e.rank_requirement,contribution_requirement:e.contribution_requirement,can_learn:e.can_learn,reason:e.reason.join("，"),learned:this.hasLearnedSkill(e.skill_name)}))):t("error","at pages/guild/guild.vue:715","加载门派武功失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:718","加载门派武功失败:",a)}},async loadSectQuests(){var e;if(l.isAuthed&&this.playerGuild)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_quests"}});"sect_action_success"===a.type&&"get_sect_quests"===a.data.action?(this.availableTasks=a.data.quests.map((e=>({id:e.id,name:e.name,description:e.description,difficulty:e.difficulty,status:e.status,progress:e.progress||0,maxProgress:e.max_progress||1,rewards:e.rewards,reward:this.formatRewards(e.rewards),canAccept:"available"===e.status}))),t("log","at pages/guild/guild.vue:747","门派任务数据:",this.availableTasks)):t("error","at pages/guild/guild.vue:749","加载门派任务失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:752","加载门派任务失败:",a)}},formatRewards(e){if(!e)return"无奖励";const t=[];return e.contribution&&t.push(`贡献 ${e.contribution}`),e.exp&&t.push(`经验 ${e.exp}`),e.silver&&t.push(`银两 ${e.silver}`),t.join(", ")||"无奖励"},async loadSectMembers(){var e;if(l.isAuthed&&this.playerGuild)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_members"}});"sect_action_success"===a.type&&"get_sect_members"===a.data.action?this.guildMembers=a.data.members.map((e=>({id:e.user_id,name:e.name,level:e.level,position:e.rank_name,contribution:e.contribution,joinTime:e.join_time,isSelf:e.is_self}))):t("error","at pages/guild/guild.vue:791","加载门派成员失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:794","加载门派成员失败:",a)}},async loadSectShop(){var e;if(l.isAuthed&&this.playerGuild)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_shop"}});"sect_action_success"===a.type&&"get_sect_shop"===a.data.action?this.guildShopItems=a.data.items.map((e=>({id:e.id,name:e.name,description:e.description,price:e.price,currency:e.currency,canBuy:e.can_buy,reasons:e.reasons||[]}))):t("error","at pages/guild/guild.vue:821","加载门派商店失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:824","加载门派商店失败:",a)}},getPositionName:e=>({master:"掌门",elder:"长老",disciple:"弟子",outer:"外门弟子"}[e]||"弟子"),showGuildList(){this.showGuildListModal=!0},closeGuildList(){this.showGuildListModal=!1},async selectGuild(e){if(e.can_join)uni.showModal({title:"确认加入",content:`确定要加入 ${e.name} 吗？`,success:async a=>{var n;if(a.confirm)try{const t=await s.sendMessage({type:"sect_action",data:{action:"join_sect",sect_id:e.id}});"join_sect_success"===t.type?(this.closeGuildList(),uni.showToast({title:t.data.message,icon:"success"}),await this.loadSectInfo()):uni.showToast({title:(null==(n=t.data)?void 0:n.message)||"加入门派失败",icon:"none"})}catch(i){t("error","at pages/guild/guild.vue:885","加入门派失败:",i),uni.showToast({title:"加入门派失败",icon:"none"})}}});else{const t=e.reasons.join("，");uni.showToast({title:`无法加入：${t}`,icon:"none"})}},async showTasks(){this.showTasksSection=!0,this.showSkillsSection=!1,this.showMembersSection=!1,this.showShopSection=!1,this.showRankingsSection=!1,this.showWarsSection=!1,this.showBuildingsSection=!1,await this.loadSectQuests()},hideTasks(){this.showTasksSection=!1},async showSkills(){this.showSkillsSection=!0,this.showTasksSection=!1,this.showMembersSection=!1,this.showShopSection=!1,this.showRankingsSection=!1,this.showWarsSection=!1,this.showBuildingsSection=!1,await this.loadSectSkills()},hideSkills(){this.showSkillsSection=!1},async showMembers(){this.showMembersSection=!0,this.showTasksSection=!1,this.showSkillsSection=!1,this.showShopSection=!1,this.showRankingsSection=!1,this.showWarsSection=!1,this.showBuildingsSection=!1,await this.loadSectMembers()},hideMembers(){this.showMembersSection=!1},async showShop(){this.showShopSection=!0,this.showTasksSection=!1,this.showSkillsSection=!1,this.showMembersSection=!1,this.showRankingsSection=!1,this.showWarsSection=!1,this.showBuildingsSection=!1,await this.loadSectShop()},hideShop(){this.showShopSection=!1},hideOtherSections(){this.showTasksSection=!1,this.showSkillsSection=!1,this.showMembersSection=!1,this.showRankingsSection=!1,this.showWarsSection=!1,this.showBuildingsSection=!1},getDifficultyClass:e=>({easy:"difficulty-easy",medium:"difficulty-medium",hard:"difficulty-hard"}[e]||"difficulty-easy"),getDifficultyName:e=>({easy:"简单",medium:"中等",hard:"困难"}[e]||"简单"),getSkillTypeName:e=>({external:"外功",internal:"内功",light:"轻功",heart:"心法",special:"特技"}[e]||"武功"),canAcceptTask:e=>!0,canLearnSkill:e=>e.can_learn&&!e.learned,hasLearnedSkill(e){try{const t=l.getPlayer();if(!t||!e)return!1;const a=["skills","martial_skills","wugong"];for(const s of a){const a=t[s];if(a){if("object"==typeof a&&!Array.isArray(a)&&a[e])return!0;if(Array.isArray(a))for(const t of a)if(t&&"object"==typeof t&&t.name===e)return!0}}return!1}catch(a){return t("error","at pages/guild/guild.vue:1028","检查武功学习状态失败:",a),!1}},canBuyItem(e){return this.playerGuild.contribution>=e.price},showTaskDetail(e){this.selectedTask=e,this.showTaskDetail=!0},closeTaskDetail(){this.showTaskDetail=!1,this.selectedTask=null},acceptTask(e){l.isAuthed?this.canAcceptTask(e)?uni.showModal({title:"接受任务",content:`确定要接受任务 "${e.name}" 吗？`,success:e=>{e.confirm&&(uni.showToast({title:"任务已接受",icon:"success"}),this.closeTaskDetail())}}):uni.showToast({title:"不满足任务要求",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})},showSkillDetail(e){},async learnSkill(e){l.isAuthed?e.can_learn?e.learned?uni.showToast({title:"开始修炼",icon:"success"}):uni.showModal({title:"学习武功",content:`确定要学习 ${e.name} 吗？\n消耗贡献: ${e.contribution_requirement}`,success:async a=>{var n;if(a.confirm)try{const t=await s.sendMessage({type:"sect_action",data:{action:"learn_sect_skill",skill_name:e.name}});"learn_skill_success"===t.type?(uni.showToast({title:t.data.message,icon:"success"}),await this.loadSectInfo(),await this.loadSectSkills()):uni.showToast({title:(null==(n=t.data)?void 0:n.message)||"学习失败",icon:"none"})}catch(i){t("error","at pages/guild/guild.vue:1130","学习武功失败:",i),uni.showToast({title:"学习失败",icon:"none"})}}}):uni.showToast({title:e.reason||"无法学习",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})},showShopItemDetail(e){},buyShopItem(e){l.isAuthed?this.canBuyItem(e)?uni.showModal({title:"购买物品",content:`确定要购买 ${e.name} 吗？\n消耗贡献: ${e.price}`,success:t=>{if(t.confirm){this.playerGuild.contribution-=e.price;const t=e.type||"",a=!(void 0!==e.sellable&&!e.sellable),s=e.unique_id||`${e.id}_${Date.now()}_${Math.floor(1e4*Math.random())}`;l.addItem({...e,type:t,sellable:a,unique_id:s}),l.save(),uni.showToast({title:"购买成功！",icon:"success"})}}}):uni.showToast({title:"贡献不足",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})},async showRankings(){this.showRankingsSection=!0,await this.loadSectRankings()},hideRankings(){this.showRankingsSection=!1},async switchRankingType(e){this.currentRankingType=e,await this.loadSectRankings()},async loadSectRankings(){var e,a;if(l.isAuthed)try{const n=await s.sendMessage({type:"sect_action",data:{action:"get_sect_rankings",ranking_type:this.currentRankingType}});"sect_action_success"===n.type&&"get_sect_rankings"===n.data.action?(this.sectRankings=n.data.rankings||[],t("log","at pages/guild/guild.vue:1213","门派排行榜数据:",this.sectRankings)):(t("error","at pages/guild/guild.vue:1215","加载门派排行榜失败:",null==(e=n.data)?void 0:e.message),uni.showToast({title:(null==(a=n.data)?void 0:a.message)||"加载排行榜失败",icon:"none"}))}catch(n){t("error","at pages/guild/guild.vue:1222","加载门派排行榜失败:",n),uni.showToast({title:"加载排行榜失败",icon:"none"})}},async showWars(){this.showWarsSection=!0,await this.loadSectWars()},hideWars(){this.showWarsSection=!1},async loadSectWars(){var e;if(l.isAuthed)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_wars"}});"sect_action_success"===a.type&&"get_sect_wars"===a.data.action?this.sectWars=a.data.wars||[]:t("error","at pages/guild/guild.vue:1256","加载门派战争失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:1259","加载门派战争失败:",a)}},showDeclareWarModal(){this.showDeclareWarModal=!0,this.loadAvailableTargetSects()},closeDeclareWar(){this.showDeclareWarModal=!1,this.selectedTargetSect=null,this.selectedTargetSectIndex=0,this.warReason=""},async loadAvailableTargetSects(){var e,t;const a=Object.values((null==(t=null==(e=this.sectInfo)?void 0:e.sect_config)?void 0:t.sects)||{});this.availableTargetSects=a.filter((e=>{var t;return e.id!==(null==(t=this.sectInfo)?void 0:t.sect_id)})).map((e=>({id:e.id,name:e.name})))},onTargetSectChange(e){const t=e.detail.value;this.selectedTargetSectIndex=t,this.selectedTargetSect=this.availableTargetSects[t]},async confirmDeclareWar(){var e;if(this.selectedTargetSect&&this.warReason.trim())try{const t=await s.sendMessage({type:"sect_action",data:{action:"declare_war",target_sect_id:this.selectedTargetSect.id,war_reason:this.warReason.trim()}});"declare_war_success"===t.type?(uni.showToast({title:t.data.message,icon:"success"}),this.closeDeclareWar(),await this.loadSectWars()):uni.showToast({title:(null==(e=t.data)?void 0:e.message)||"宣战失败",icon:"none"})}catch(t){uni.showToast({title:"宣战失败: "+t.message,icon:"none"})}},getWarStatusText:e=>({declared:"已宣战",active:"战争中",ended:"已结束"}[e]||"未知"),formatTime(e){if(!e)return"";return new Date(e).toLocaleString("zh-CN")},async showBuildings(){this.showBuildingsSection=!0,await this.loadSectBuildings()},hideBuildings(){this.showBuildingsSection=!1},async loadSectBuildings(){var e;if(l.isAuthed)try{const a=await s.sendMessage({type:"sect_action",data:{action:"get_sect_buildings"}});"sect_action_success"===a.type&&"get_sect_buildings"===a.data.action?(this.sectBuildings=a.data.buildings||{},this.sectResources=a.data.resources||{}):t("error","at pages/guild/guild.vue:1377","加载门派建筑失败:",null==(e=a.data)?void 0:e.message)}catch(a){t("error","at pages/guild/guild.vue:1380","加载门派建筑失败:",a)}},async upgradeBuilding(e){var t;if(this.canUpgradeBuilding(e))try{const a=await s.sendMessage({type:"sect_action",data:{action:"upgrade_building",building_type:e}});"upgrade_building_success"===a.type?(uni.showToast({title:a.data.message,icon:"success"}),await this.loadSectBuildings()):uni.showToast({title:(null==(t=a.data)?void 0:t.message)||"升级失败",icon:"none"})}catch(a){uni.showToast({title:"升级失败: "+a.message,icon:"none"})}},canUpgradeBuilding(e){const t=this.sectBuildings[e];if(!t)return!1;const a=this.getBuildingUpgradeCost(e,t.level+1);if(!a)return!1;for(const[s,n]of Object.entries(a))if((this.sectResources[s]||0)<n)return!1;return!0},getBuildingUpgradeCost(e,t){const a={main_hall:{wood:100,stone:150,iron:50,gold:200},training_ground:{wood:80,stone:100,iron:120,gold:150},library:{wood:120,stone:80,iron:60,gold:180},warehouse:{wood:60,stone:120,iron:80,gold:100}}[e];if(!a)return null;const s=Math.pow(1.5,t-1),n={};for(const[i,l]of Object.entries(a))n[i]=Math.floor(l*s);return n},getResourceName:e=>({wood:"木材",stone:"石料",iron:"铁矿",gold:"金币"}[e]||e)}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"container"},[i.playerGuild?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guild-info"},[e.createElementVNode("view",{class:"guild-header"},[e.createElementVNode("text",{class:"guild-name"},e.toDisplayString(i.playerGuild.name),1),e.createElementVNode("text",{class:"guild-level"},"等级 "+e.toDisplayString(i.playerGuild.level),1)]),e.createElementVNode("view",{class:"guild-stats"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"声望:"),e.createElementVNode("text",{class:"stat-value"},e.toDisplayString(i.player.reputation||0),1)]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"贡献:"),e.createElementVNode("text",{class:"stat-value"},e.toDisplayString(i.playerGuild.contribution),1)]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-label"},"职位:"),e.createElementVNode("text",{class:"stat-value"},e.toDisplayString(l.getPositionName(i.playerGuild.position)),1)])]),i.sectInfo?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guild-actions"},[e.createElementVNode("button",{class:"daily-reward-btn",onClick:a[0]||(a[0]=(...e)=>l.claimDailyReward&&l.claimDailyReward(...e)),disabled:!i.sectInfo.can_claim_daily},e.toDisplayString(i.sectInfo.can_claim_daily?"领取每日奖励":"今日已领取"),9,["disabled"])])):e.createCommentVNode("",!0)])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-guild"},[e.createElementVNode("text",{class:"no-guild-title"},"尚未加入门派"),e.createElementVNode("text",{class:"no-guild-desc"},"加入门派可以获得专属武功和任务"),e.createElementVNode("button",{class:"join-guild-btn",onClick:a[1]||(a[1]=(...e)=>l.showGuildList&&l.showGuildList(...e))},"加入门派")])),i.playerGuild?(e.openBlock(),e.createElementBlock("view",{key:2,class:"guild-functions"},[e.createElementVNode("view",{class:"function-grid"},[e.createElementVNode("view",{class:"function-item",onClick:a[2]||(a[2]=(...e)=>l.showTasks&&l.showTasks(...e))},[e.createElementVNode("text",{class:"function-icon"},"📋"),e.createElementVNode("text",{class:"function-name"},"门派任务")]),e.createElementVNode("view",{class:"function-item",onClick:a[3]||(a[3]=(...e)=>l.showSkills&&l.showSkills(...e))},[e.createElementVNode("text",{class:"function-icon"},"⚔️"),e.createElementVNode("text",{class:"function-name"},"门派武功")]),e.createElementVNode("view",{class:"function-item",onClick:a[4]||(a[4]=(...e)=>l.showMembers&&l.showMembers(...e))},[e.createElementVNode("text",{class:"function-icon"},"👥"),e.createElementVNode("text",{class:"function-name"},"门派成员")]),e.createElementVNode("view",{class:"function-item",onClick:a[5]||(a[5]=(...e)=>l.showShop&&l.showShop(...e))},[e.createElementVNode("text",{class:"function-icon"},"🏪"),e.createElementVNode("text",{class:"function-name"},"门派商店")]),e.createElementVNode("view",{class:"function-item",onClick:a[6]||(a[6]=(...e)=>l.showRankings&&l.showRankings(...e))},[e.createElementVNode("text",{class:"function-icon"},"🏆"),e.createElementVNode("text",{class:"function-name"},"门派排行")]),e.createElementVNode("view",{class:"function-item",onClick:a[7]||(a[7]=(...e)=>l.showWars&&l.showWars(...e))},[e.createElementVNode("text",{class:"function-icon"},"⚔️"),e.createElementVNode("text",{class:"function-name"},"门派战争")]),e.createElementVNode("view",{class:"function-item",onClick:a[8]||(a[8]=(...e)=>l.showBuildings&&l.showBuildings(...e))},[e.createElementVNode("text",{class:"function-icon"},"🏗️"),e.createElementVNode("text",{class:"function-name"},"门派建设")])])])):e.createCommentVNode("",!0),i.showTasksSection?(e.openBlock(),e.createElementBlock("view",{key:3,class:"tasks-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派任务"),e.createElementVNode("text",{class:"section-close",onClick:a[9]||(a[9]=(...e)=>l.hideTasks&&l.hideTasks(...e))},"×")]),e.createElementVNode("scroll-view",{class:"tasks-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.availableTasks,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"task-item",key:a,onClick:e=>l.showTaskDetail(t)},[e.createElementVNode("view",{class:"task-info"},[e.createElementVNode("text",{class:"task-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"task-desc"},e.toDisplayString(t.description),1),e.createElementVNode("text",{class:"task-reward"},"奖励: "+e.toDisplayString(t.reward),1)]),e.createElementVNode("view",{class:"task-status"},[e.createElementVNode("text",{class:e.normalizeClass(["task-difficulty",l.getDifficultyClass(t.difficulty)])},e.toDisplayString(l.getDifficultyName(t.difficulty)),3),e.createElementVNode("button",{class:"accept-task-btn",onClick:e.withModifiers((e=>l.acceptTask(t)),["stop"]),disabled:!l.canAcceptTask(t)}," 接受 ",8,["onClick","disabled"])])],8,["onClick"])))),128)),0===i.availableTasks.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tasks"},[e.createElementVNode("text",null,"暂无可接任务")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showSkillsSection?(e.openBlock(),e.createElementBlock("view",{key:4,class:"skills-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派武功"),e.createElementVNode("text",{class:"section-close",onClick:a[10]||(a[10]=(...e)=>l.hideSkills&&l.hideSkills(...e))},"×")]),e.createElementVNode("scroll-view",{class:"skills-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.guildSkills,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"skill-item",key:a,onClick:e=>l.showSkillDetail(t)},[e.createElementVNode("view",{class:"skill-info"},[e.createElementVNode("text",{class:"skill-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"skill-type"},e.toDisplayString(l.getSkillTypeName(t.type)),1),e.createElementVNode("text",{class:"skill-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("view",{class:"skill-status"},[t.level?(e.openBlock(),e.createElementBlock("text",{key:0,class:"skill-level"},"等级 "+e.toDisplayString(t.level),1)):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"learn-skill-btn",onClick:e.withModifiers((e=>l.learnSkill(t)),["stop"]),disabled:!l.canLearnSkill(t)},e.toDisplayString(t.learned?"已学习":"学习"),9,["onClick","disabled"])])],8,["onClick"])))),128)),0===i.guildSkills.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-skills"},[e.createElementVNode("text",null,"暂无门派武功")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showMembersSection?(e.openBlock(),e.createElementBlock("view",{key:5,class:"members-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派成员"),e.createElementVNode("text",{class:"section-close",onClick:a[11]||(a[11]=(...e)=>l.hideMembers&&l.hideMembers(...e))},"×")]),e.createElementVNode("scroll-view",{class:"members-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.guildMembers,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"member-item",key:a},[e.createElementVNode("view",{class:"member-info"},[e.createElementVNode("text",{class:"member-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"member-position"},e.toDisplayString(l.getPositionName(t.position)),1),e.createElementVNode("text",{class:"member-level"},"等级 "+e.toDisplayString(t.level),1)]),e.createElementVNode("view",{class:"member-contribution"},[e.createElementVNode("text",{class:"contribution-label"},"贡献:"),e.createElementVNode("text",{class:"contribution-value"},e.toDisplayString(t.contribution),1)])])))),128)),0===i.guildMembers.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-members"},[e.createElementVNode("text",null,"暂无门派成员")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showShopSection?(e.openBlock(),e.createElementBlock("view",{key:6,class:"shop-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派商店"),e.createElementVNode("text",{class:"section-close",onClick:a[12]||(a[12]=(...e)=>l.hideShop&&l.hideShop(...e))},"×")]),e.createElementVNode("scroll-view",{class:"shop-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.guildShopItems,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"shop-item",key:a,onClick:e=>l.showShopItemDetail(t)},[e.createElementVNode("view",{class:"item-info"},[e.createElementVNode("text",{class:"item-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"item-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("view",{class:"item-price"},[e.createElementVNode("text",{class:"price-value"},e.toDisplayString(t.price),1),e.createElementVNode("text",{class:"price-unit"},"贡献")]),e.createElementVNode("button",{class:"buy-item-btn",onClick:e.withModifiers((e=>l.buyShopItem(t)),["stop"]),disabled:!l.canBuyItem(t)}," 购买 ",8,["onClick","disabled"])],8,["onClick"])))),128)),0===i.guildShopItems.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-shop"},[e.createElementVNode("text",null,"暂无商品")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showRankingsSection?(e.openBlock(),e.createElementBlock("view",{key:7,class:"rankings-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派排行榜"),e.createElementVNode("text",{class:"section-close",onClick:a[13]||(a[13]=(...e)=>l.hideRankings&&l.hideRankings(...e))},"×")]),e.createElementVNode("view",{class:"ranking-tabs"},[e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"power"===i.currentRankingType}]),onClick:a[14]||(a[14]=e=>l.switchRankingType("power"))}," 实力排行 ",2),e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"martial"===i.currentRankingType}]),onClick:a[15]||(a[15]=e=>l.switchRankingType("martial"))}," 武功排行 ",2),e.createElementVNode("view",{class:e.normalizeClass(["ranking-tab",{active:"contribution"===i.currentRankingType}]),onClick:a[16]||(a[16]=e=>l.switchRankingType("contribution"))}," 贡献排行 ",2)]),e.createElementVNode("scroll-view",{class:"rankings-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.sectRankings,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"ranking-item",key:a},[e.createElementVNode("view",{class:"ranking-rank"},[e.createElementVNode("text",{class:"rank-number"},e.toDisplayString(t.rank),1)]),e.createElementVNode("view",{class:"ranking-info"},[e.createElementVNode("text",{class:"player-name"},e.toDisplayString(t.player_name),1),e.createElementVNode("view",{class:"player-stats"},["power"===i.currentRankingType?(e.openBlock(),e.createElementBlock("text",{key:0,class:"stat-text"}," 历练: "+e.toDisplayString(t.experience)+" | 贡献: "+e.toDisplayString(t.contribution),1)):"martial"===i.currentRankingType?(e.openBlock(),e.createElementBlock("text",{key:1,class:"stat-text"}," 武功总分: "+e.toDisplayString(t.martial_score)+" | 武功数量: "+e.toDisplayString(t.skill_count),1)):"contribution"===i.currentRankingType?(e.openBlock(),e.createElementBlock("text",{key:2,class:"stat-text"}," 贡献: "+e.toDisplayString(t.contribution)+" | 门派等级: "+e.toDisplayString(t.sect_rank),1)):e.createCommentVNode("",!0)])])])))),128)),0===i.sectRankings.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-rankings"},[e.createElementVNode("text",null,"暂无排行数据")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showWarsSection?(e.openBlock(),e.createElementBlock("view",{key:8,class:"wars-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派战争"),e.createElementVNode("text",{class:"section-close",onClick:a[17]||(a[17]=(...e)=>l.hideWars&&l.hideWars(...e))},"×")]),e.createElementVNode("view",{class:"war-actions"},[e.createElementVNode("button",{class:"declare-war-btn",onClick:a[18]||(a[18]=(...e)=>l.showDeclareWarModal&&l.showDeclareWarModal(...e))},"宣战")]),e.createElementVNode("scroll-view",{class:"wars-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.sectWars,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"war-item",key:a},[e.createElementVNode("view",{class:"war-header"},[e.createElementVNode("view",{class:"war-sides"},[e.createElementVNode("text",{class:e.normalizeClass(["sect-name attacker",{"own-sect":t.is_attacker}])},e.toDisplayString(t.attacker_sect_name),3),e.createElementVNode("text",{class:"vs-text"},"VS"),e.createElementVNode("text",{class:e.normalizeClass(["sect-name defender",{"own-sect":!t.is_attacker}])},e.toDisplayString(t.defender_sect_name),3)]),e.createElementVNode("view",{class:e.normalizeClass(["war-status","status-"+t.status])},e.toDisplayString(l.getWarStatusText(t.status)),3)]),e.createElementVNode("view",{class:"war-info"},[e.createElementVNode("text",{class:"war-reason"},"战争原因: "+e.toDisplayString(t.war_reason),1),e.createElementVNode("text",{class:"war-time"},"宣战时间: "+e.toDisplayString(l.formatTime(t.declare_time)),1),"ended"===t.status?(e.openBlock(),e.createElementBlock("view",{key:0,class:"war-result"},[e.createElementVNode("text",{class:"winner"},"胜利者: "+e.toDisplayString(t.winner_sect_name||"平局"),1),e.createElementVNode("text",{class:"score"},"比分: "+e.toDisplayString(t.attacker_score)+" : "+e.toDisplayString(t.defender_score),1)])):e.createCommentVNode("",!0)])])))),128)),0===i.sectWars.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-wars"},[e.createElementVNode("text",null,"暂无战争记录")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),l.showDeclareWarModal?(e.openBlock(),e.createElementBlock("view",{key:9,class:"modal-overlay",onClick:a[25]||(a[25]=(...e)=>l.closeDeclareWar&&l.closeDeclareWar(...e))},[e.createElementVNode("view",{class:"modal-content",onClick:a[24]||(a[24]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"宣战"),e.createElementVNode("text",{class:"modal-close",onClick:a[19]||(a[19]=(...e)=>l.closeDeclareWar&&l.closeDeclareWar(...e))},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"选择目标门派:"),e.createElementVNode("picker",{value:i.selectedTargetSectIndex,range:i.availableTargetSects,"range-key":"name",onChange:a[20]||(a[20]=(...e)=>l.onTargetSectChange&&l.onTargetSectChange(...e))},[e.createElementVNode("view",{class:"picker-display"},e.toDisplayString(i.selectedTargetSect?i.selectedTargetSect.name:"请选择门派"),1)],40,["value","range"])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"战争原因:"),e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":a[21]||(a[21]=e=>i.warReason=e),placeholder:"请输入宣战理由...",maxlength:"100",class:"war-reason-input"},null,512),[[e.vModelText,i.warReason]])])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"cancel-btn",onClick:a[22]||(a[22]=(...e)=>l.closeDeclareWar&&l.closeDeclareWar(...e))},"取消"),e.createElementVNode("button",{class:"confirm-btn",onClick:a[23]||(a[23]=(...e)=>l.confirmDeclareWar&&l.confirmDeclareWar(...e)),disabled:!i.selectedTargetSect||!i.warReason.trim()}," 宣战 ",8,["disabled"])])])])):e.createCommentVNode("",!0),i.showBuildingsSection?(e.openBlock(),e.createElementBlock("view",{key:10,class:"buildings-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"门派建设"),e.createElementVNode("text",{class:"section-close",onClick:a[26]||(a[26]=(...e)=>l.hideBuildings&&l.hideBuildings(...e))},"×")]),e.createElementVNode("view",{class:"resources-display"},[e.createElementVNode("text",{class:"resources-title"},"门派资源"),e.createElementVNode("view",{class:"resources-grid"},[e.createElementVNode("view",{class:"resource-item"},[e.createElementVNode("text",{class:"resource-icon"},"🪵"),e.createElementVNode("text",{class:"resource-name"},"木材"),e.createElementVNode("text",{class:"resource-value"},e.toDisplayString(i.sectResources.wood||0),1)]),e.createElementVNode("view",{class:"resource-item"},[e.createElementVNode("text",{class:"resource-icon"},"🪨"),e.createElementVNode("text",{class:"resource-name"},"石料"),e.createElementVNode("text",{class:"resource-value"},e.toDisplayString(i.sectResources.stone||0),1)]),e.createElementVNode("view",{class:"resource-item"},[e.createElementVNode("text",{class:"resource-icon"},"⚒️"),e.createElementVNode("text",{class:"resource-name"},"铁矿"),e.createElementVNode("text",{class:"resource-value"},e.toDisplayString(i.sectResources.iron||0),1)]),e.createElementVNode("view",{class:"resource-item"},[e.createElementVNode("text",{class:"resource-icon"},"💰"),e.createElementVNode("text",{class:"resource-name"},"金币"),e.createElementVNode("text",{class:"resource-value"},e.toDisplayString(i.sectResources.gold||0),1)])])]),e.createElementVNode("scroll-view",{class:"buildings-list","scroll-y":"true"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.sectBuildings,((t,a)=>(e.openBlock(),e.createElementBlock("view",{class:"building-item",key:a},[e.createElementVNode("view",{class:"building-header"},[e.createElementVNode("view",{class:"building-info"},[e.createElementVNode("text",{class:"building-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"building-level"},"等级 "+e.toDisplayString(t.level),1)]),e.createElementVNode("button",{class:"upgrade-btn",onClick:e=>l.upgradeBuilding(a),disabled:!l.canUpgradeBuilding(a)}," 升级 ",8,["onClick","disabled"])]),l.getBuildingUpgradeCost(a,t.level+1)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"building-cost"},[e.createElementVNode("text",{class:"cost-title"},"升级消耗:"),e.createElementVNode("view",{class:"cost-items"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getBuildingUpgradeCost(a,t.level+1),((t,a)=>(e.openBlock(),e.createElementBlock("text",{key:a,class:e.normalizeClass(["cost-item",{insufficient:(i.sectResources[a]||0)<t}])},e.toDisplayString(l.getResourceName(a))+": "+e.toDisplayString(t),3)))),128))])])):e.createCommentVNode("",!0)])))),128)),0===Object.keys(i.sectBuildings).length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-buildings"},[e.createElementVNode("text",null,"暂无建筑数据")])):e.createCommentVNode("",!0)])])):e.createCommentVNode("",!0),i.showGuildListModal?(e.openBlock(),e.createElementBlock("view",{key:11,class:"modal-overlay",onClick:a[30]||(a[30]=(...e)=>l.closeGuildList&&l.closeGuildList(...e))},[e.createElementVNode("view",{class:"modal-content join-sect-guide",onClick:a[29]||(a[29]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"如何加入门派"),e.createElementVNode("text",{class:"modal-close",onClick:a[27]||(a[27]=(...e)=>l.closeGuildList&&l.closeGuildList(...e))},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-title"},"📜 门派令牌"),e.createElementVNode("text",{class:"guide-text"},"每个门派都需要对应的令牌才能加入，令牌可通过以下方式获得：")]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-subtitle"},"🎯 获取方式"),e.createElementVNode("view",{class:"guide-list"},[e.createElementVNode("text",{class:"guide-item"},"• 完成特定任务获得门派令牌"),e.createElementVNode("text",{class:"guide-item"},"• 击败特定NPC有几率掉落令牌"),e.createElementVNode("text",{class:"guide-item"},"• 在商店购买门派令牌"),e.createElementVNode("text",{class:"guide-item"},"• 参与活动获得令牌奖励")])]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-subtitle"},"⚔️ 主要门派"),e.createElementVNode("view",{class:"sect-list"},[e.createElementVNode("view",{class:"sect-item"},[e.createElementVNode("text",{class:"sect-name"},"少林派"),e.createElementVNode("text",{class:"sect-desc"},"以内功和拳法闻名，需要少林令牌")]),e.createElementVNode("view",{class:"sect-item"},[e.createElementVNode("text",{class:"sect-name"},"武当派"),e.createElementVNode("text",{class:"sect-desc"},"太极剑法和内功修为，需要武当令牌")]),e.createElementVNode("view",{class:"sect-item"},[e.createElementVNode("text",{class:"sect-name"},"峨眉派"),e.createElementVNode("text",{class:"sect-desc"},"剑法精妙，医术高超，需要峨眉令牌")]),e.createElementVNode("view",{class:"sect-item"},[e.createElementVNode("text",{class:"sect-name"},"华山派"),e.createElementVNode("text",{class:"sect-desc"},"剑法独步天下，需要华山令牌")])])]),e.createElementVNode("view",{class:"guide-section"},[e.createElementVNode("text",{class:"guide-subtitle"},"💡 使用方法"),e.createElementVNode("text",{class:"guide-text"},"获得门派令牌后，在背包中使用即可自动加入对应门派。加入成功后将有全服公告。")])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn confirm-btn",onClick:a[28]||(a[28]=(...e)=>l.closeGuildList&&l.closeGuildList(...e))},"我知道了")])])])):e.createCommentVNode("",!0),l.showTaskDetail?(e.openBlock(),e.createElementBlock("view",{key:12,class:"modal-overlay",onClick:a[35]||(a[35]=(...e)=>l.closeTaskDetail&&l.closeTaskDetail(...e))},[e.createElementVNode("view",{class:"modal-content",onClick:a[34]||(a[34]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"任务详情"),e.createElementVNode("text",{class:"modal-close",onClick:a[31]||(a[31]=(...e)=>l.closeTaskDetail&&l.closeTaskDetail(...e))},"×")]),i.selectedTask?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal-body"},[e.createElementVNode("text",{class:"detail-name"},e.toDisplayString(i.selectedTask.name),1),e.createElementVNode("text",{class:"detail-desc"},e.toDisplayString(i.selectedTask.description),1),e.createElementVNode("text",{class:"detail-requirement"},"要求: "+e.toDisplayString(i.selectedTask.requirement),1),e.createElementVNode("text",{class:"detail-reward"},"奖励: "+e.toDisplayString(i.selectedTask.reward),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[32]||(a[32]=(...e)=>l.closeTaskDetail&&l.closeTaskDetail(...e))},"关闭"),e.createElementVNode("button",{class:"modal-btn confirm-btn",onClick:a[33]||(a[33]=e=>l.acceptTask(i.selectedTask)),disabled:!l.canAcceptTask(i.selectedTask)}," 接受任务 ",8,["disabled"])])])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-7cadce57"]]);const u=n({data:()=>({inventoryData:[],inventoryCapacity:50,showItemDetail:!1,selectedItem:null,selectedIndex:-1,isLoading:!1,itemsConfig:{}}),onLoad(){t("log","at pages/character/backpack.vue:142","[背包页面] onLoad 被调用"),this.loadInventoryData(),this.loadItemsConfig()},onShow(){t("log","at pages/character/backpack.vue:148","[背包页面] onShow 被调用"),this.loadInventoryData()},onUnload(){this.cleanupEventListeners()},methods:{async loadInventoryData(){try{t("log","at pages/character/backpack.vue:159","[背包页面] 开始加载背包数据"),this.isLoading=!0;const e=await s.sendMessage({type:"get_inventory_data",data:{}});t("log","at pages/character/backpack.vue:168","[背包页面] 收到响应:",e),this.isLoading=!1,"inventory_data"===e.type?(t("log","at pages/character/backpack.vue:172","收到背包数据:",e.data),this.inventoryData=e.data.inventory||[],this.inventoryCapacity=e.data.capacity||50,t("log","at pages/character/backpack.vue:175","[背包页面] 背包数据已更新:",this.inventoryData)):"error"===e.type&&(t("error","at pages/character/backpack.vue:177","WebSocket错误:",e.data),e.data&&e.data.message&&e.data.message.includes("背包已满")?uni.showToast({title:e.data.message,icon:"none",duration:3e3}):uni.showToast({title:"网络错误，请重试",icon:"none"}))}catch(e){t("error","at pages/character/backpack.vue:194","加载背包数据失败:",e),this.isLoading=!1,uni.showToast({title:"加载失败: "+(e.message||"未知错误"),icon:"none"})}},handleItemClick(e,a){t("log","at pages/character/backpack.vue:204","[背包] 选中物品:",e,"索引:",a);const s=this.itemsConfig[e.id]||{},n=s.type||e.type||"",i=!!(void 0!==s.sellable?s.sellable:e.sellable);this.selectedItem={...e,...s,type:n,sellable:i},this.selectedIndex=a,this.showItemDetail=!0},closeItemDetail(){this.showItemDetail=!1,this.selectedItem=null,this.selectedIndex=-1},getItemQualityClass(e){if(!e||!e.品质)return"quality-normal";switch(e.品质){case"common":return"quality-common";case"uncommon":return"quality-uncommon";case"rare":return"quality-rare";case"epic":return"quality-epic";case"legendary":return"quality-legendary";default:return"quality-normal"}},getQualityText:e=>({common:"普通",uncommon:"优秀",rare:"稀有",epic:"史诗",legendary:"传说",mythic:"神话"}[e]||"普通"),getTypeText:e=>({weapon:"武器",helmet:"头盔",necklace:"项链",armor:"衣服",cloak:"披风",pants:"裤子",shoes:"鞋子",bracelet:"手镯",ring:"戒指",shield:"盾牌",consumable:"消耗品",medicine:"药品",pill:"丹药",material:"材料",ore:"矿石",wood:"木材",herb:"草药",fur:"兽皮",tool:"工具",pickaxe:"矿镐",axe:"斧头",sickle:"镰刀",knife:"小刀",special:"特殊",quest:"任务",currency:"货币"}[e]||e),canEquip:e=>(1===e.equipable||!0===e.equipable)&&"gather_tool"!==e.type||["weapon","helmet","necklace","armor","cloak","pants","shoes","bracelet","ring","shield","medal","accessory"].includes(e.type),canUse:e=>1===e.usable||!0===e.usable||["consumable","medicine","pill"].includes(e.type),getItemEffects(e){const t=[],a={attack:"攻击",defense:"防御",hp:"气血",mp:"内力",energy:"精力",energy_regen:"精力回复",crit:"暴击",dodge:"闪避",hit:"命中",speed:"速度"};for(const[n,i]of Object.entries(a)){const a=e[n]||e[i];a&&a>0&&t.push({name:i,value:a})}const s=e.effects;if(s&&"string"==typeof s){const e=s.split(",");for(const s of e){const[e,n]=s.split(":");if(e&&n){const s=e.trim(),i=parseInt(n.trim()),l=a[s]||s;if(i>0){const e=t.find((e=>e.name===l));e?e.value+=i:t.push({name:l,value:i})}}}}return t},async equipItem(){if(t("log","at pages/character/backpack.vue:351","[装备] 当前selectedItem:",this.selectedItem,"selectedIndex:",this.selectedIndex),this.selectedItem&&this.selectedItem.unique_id)try{this.isLoading=!0;const e=this.getDefaultSlot(this.selectedItem.type);t("log","at pages/character/backpack.vue:359","[装备] 发送equip_item:",{unique_id:this.selectedItem.unique_id,slot_type:e});const a=await s.sendMessage({type:"equip_item",data:{unique_id:this.selectedItem.unique_id,type:this.selectedItem.type,sellable:this.selectedItem.sellable,slot_type:e}});this.isLoading=!1,"equip_success"===a.type?(uni.showToast({title:a.data.message||"装备成功",icon:"success"}),this.closeItemDetail(),a.data.inventory?this.inventoryData=a.data.inventory:this.loadInventoryData()):("equip_failed"===a.type||"error"===a.type)&&(uni.showToast({title:a.data.message||"装备失败",icon:"none"}),this.closeItemDetail())}catch(e){t("error","at pages/character/backpack.vue:405","装备失败:",e),this.isLoading=!1,uni.showToast({title:"装备失败: "+(e.message||"未知错误"),icon:"none"}),this.closeItemDetail()}else t("warn","at pages/character/backpack.vue:353","[装备] 缺少unique_id，无法发送装备请求")},getDefaultSlot(e){const t=l.playerData||{};if("bracelet"===e||"accessory"===e)return t.equipment&&!t.equipment.bracelet1?"bracelet1":t.equipment&&!t.equipment.bracelet2?"bracelet2":"bracelet1";if("ring"===e)return t.equipment&&!t.equipment.ring1?"ring1":t.equipment&&!t.equipment.ring2?"ring2":"ring1";const a={weapon:"main_hand",helmet:"helmet",necklace:"necklace",armor:"armor",cloak:"cloak",pants:"pants",shoes:"shoes",bracelet:["bracelet1","bracelet2"],ring:["ring1","ring2"],shield:"off_hand",off_hand:"off_hand",medal:"medal",accessory:["bracelet1","bracelet2"]}[e];return Array.isArray(a)?a[0]:a||"main_hand"},useItem(){this.selectedItem&&-1!==this.selectedIndex&&uni.showModal({title:"使用物品",content:`确定要使用 ${this.selectedItem.name||this.selectedItem.名称} 吗？`,showCancel:!0,cancelText:"取消",confirmText:"使用",success:e=>{e.confirm&&this.sendUseItem()}})},async sendUseItem(){try{this.isLoading=!0;const e=await s.sendMessage({type:"use_item",data:{unique_id:this.selectedItem.unique_id,type:this.selectedItem.type,sellable:this.selectedItem.sellable}});this.isLoading=!1,"use_item_success"===e.type?(uni.showToast({title:e.data.message||"使用成功",icon:"success"}),this.closeItemDetail(),e.data.inventory?this.inventoryData=e.data.inventory:this.loadInventoryData()):("use_item_failed"===e.type||"error"===e.type)&&(uni.showToast({title:e.data.message||"使用失败",icon:"none"}),this.closeItemDetail())}catch(e){t("error","at pages/character/backpack.vue:511","使用物品失败:",e),this.isLoading=!1,uni.showToast({title:"使用失败: "+(e.message||"未知错误"),icon:"none"}),this.closeItemDetail()}},destroyItem(){this.selectedItem&&-1!==this.selectedIndex&&uni.showModal({title:"销毁物品",content:`确定要销毁 ${this.selectedItem.name||this.selectedItem.名称} 吗？此操作不可撤销！`,showCancel:!0,cancelText:"取消",confirmText:"销毁",success:e=>{e.confirm&&this.sendDestroyItem()}})},async sendDestroyItem(){try{this.isLoading=!0;const e=await s.sendMessage({type:"destroy_item",data:{unique_id:this.selectedItem.unique_id,type:this.selectedItem.type,sellable:this.selectedItem.sellable}});this.isLoading=!1,"destroy_item_success"===e.type?(uni.showToast({title:e.data.message||"销毁成功",icon:"success"}),this.showItemDetail=!1,this.selectedItem=null,this.selectedIndex=-1,e.data.inventory?this.inventoryData=e.data.inventory:this.loadInventoryData()):("destroy_item_failed"===e.type||"error"===e.type)&&(uni.showToast({title:e.data.message||"销毁失败",icon:"none"}),this.closeItemDetail())}catch(e){t("error","at pages/character/backpack.vue:585","销毁物品失败:",e),this.isLoading=!1,uni.showToast({title:"销毁失败: "+(e.message||"未知错误"),icon:"none"}),this.closeItemDetail()}},async expandInventory(){try{this.isLoading=!0;const e=await s.sendMessage({type:"expand_inventory",data:{}});this.isLoading=!1,"expand_success"===e.type?(uni.showToast({title:e.data.message||"扩充背包成功",icon:"success"}),e.data.inventory&&(this.inventoryData=e.data.inventory),e.data.capacity&&(this.inventoryCapacity=e.data.capacity),e.data.inventory||this.loadInventoryData()):("expand_failed"===e.type||"error"===e.type)&&uni.showToast({title:e.data.message||"扩充背包失败",icon:"none"})}catch(e){t("error","at pages/character/backpack.vue:636","扩充背包失败:",e),this.isLoading=!1,uni.showToast({title:"扩充失败: "+(e.message||"未知错误"),icon:"none"})}},sortInventory(){this.inventoryData.sort(((e,t)=>{const a=["weapon","shield","helmet","necklace","armor","cloak","pants","shoes","bracelet","ring","consumable","medicine","pill","material","ore","wood","herb","fur","tool","pickaxe","axe","sickle","knife","special","quest","currency"],s=a.indexOf(e.type)||999,n=a.indexOf(t.type)||999;if(s!==n)return s-n;const i=["mythic","legendary","epic","rare","uncommon","common"];return(i.indexOf(e.品质||e.quality)||999)-(i.indexOf(t.品质||t.quality)||999)})),uni.showToast({title:"背包已整理",icon:"success"})},clearInventory(){uni.showModal({title:"确认清空背包",content:`确定要清空背包中的所有物品吗？\n此操作不可撤销！\n当前背包中有 ${this.inventoryData.length} 个物品。`,confirmText:"确认清空",confirmColor:"#F44336",cancelText:"取消",success:e=>{e.confirm&&this.performClearInventory()}})},async performClearInventory(){try{this.isLoading=!0,uni.showToast({title:"开始清空背包...",icon:"none",duration:1e3});const a=[...this.inventoryData];let n=0;for(let i=0;i<a.length;i++){const l=a[i];try{await s.sendMessage({type:"destroy_item",data:{unique_id:l.unique_id||l.id,slot:i}}),n++,n%5!=0&&n!==a.length||uni.showToast({title:`已清空 ${n}/${a.length} 个物品`,icon:"none",duration:1e3}),await new Promise((e=>setTimeout(e,100)))}catch(e){t("error","at pages/character/backpack.vue:725","销毁物品失败:",e)}}setTimeout((()=>{this.loadInventoryData(),uni.showToast({title:`背包清空完成！共清空 ${n} 个物品`,icon:"success",duration:2e3}),this.isLoading=!1}),1500)}catch(e){t("error","at pages/character/backpack.vue:741","清空背包失败:",e),this.isLoading=!1,uni.showToast({title:"清空失败: "+(e.message||"未知错误"),icon:"none"})}},async loadItemsConfig(){this.itemsConfig=await l.getItemsConfig()},goBack(){uni.navigateBack({delta:1})}}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"backpack-container"},[e.createElementVNode("view",{style:{background:"red",color:"white",padding:"10rpx",margin:"10rpx"}},[e.createElementVNode("text",null,"调试信息：背包页面已加载"),e.createElementVNode("text",null,"背包数据长度："+e.toDisplayString(i.inventoryData.length),1),e.createElementVNode("text",null,"背包容量："+e.toDisplayString(i.inventoryCapacity),1)]),e.createElementVNode("view",{class:"backpack-header"},[e.createElementVNode("text",{class:"backpack-title"},"🎒 背包"),e.createElementVNode("text",{class:"backpack-info"},e.toDisplayString(i.inventoryData.length)+"/"+e.toDisplayString(i.inventoryCapacity),1)]),e.createElementVNode("view",{class:"backpack-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.inventoryData,((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["item-slot",l.getItemQualityClass(t)]),onClick:e=>l.handleItemClick(t,a)},[e.createElementVNode("text",{class:"item-icon"},e.toDisplayString(t.icon||"📦"),1),e.createElementVNode("text",{class:"item-name"},e.toDisplayString(t.name||t.名称),1),t.数量>1||t.quantity>1?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-quantity"},e.toDisplayString(t.数量||t.quantity),1)):e.createCommentVNode("",!0)],10,["onClick"])))),128)),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Math.max(0,i.inventoryCapacity-i.inventoryData.length),(t=>(e.openBlock(),e.createElementBlock("view",{key:`empty-${t}`,class:"item-slot empty"},[e.createElementVNode("text",{class:"empty-text"},"空")])))),128))]),e.createElementVNode("view",{class:"backpack-actions"},[e.createElementVNode("button",{class:"action-btn expand-btn",onClick:a[0]||(a[0]=(...e)=>l.expandInventory&&l.expandInventory(...e))},[e.createElementVNode("text",null,"📦 扩充背包")]),e.createElementVNode("button",{class:"action-btn sort-btn",onClick:a[1]||(a[1]=(...e)=>l.sortInventory&&l.sortInventory(...e))},[e.createElementVNode("text",null,"🔄 整理背包")]),e.createElementVNode("button",{class:"action-btn clear-btn",onClick:a[2]||(a[2]=(...e)=>l.clearInventory&&l.clearInventory(...e)),disabled:0===i.inventoryData.length||i.isLoading},[e.createElementVNode("text",null,e.toDisplayString(i.isLoading?"处理中...":"🗑️ 清空背包"),1)],8,["disabled"])]),e.createElementVNode("view",{class:"backpack-actions back-actions"},[e.createElementVNode("button",{class:"action-btn back-btn",onClick:a[3]||(a[3]=(...e)=>l.goBack&&l.goBack(...e))},[e.createElementVNode("text",null,"⬅️ 返回角色")])]),i.showItemDetail?(e.openBlock(),e.createElementBlock("view",{key:0,class:"item-detail-modal",onClick:a[9]||(a[9]=(...e)=>l.closeItemDetail&&l.closeItemDetail(...e))},[e.createElementVNode("view",{class:"item-detail-content",onClick:a[8]||(a[8]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"item-detail-header"},[e.createElementVNode("text",{class:"item-detail-title"},e.toDisplayString(i.selectedItem.name||i.selectedItem.名称),1),e.createElementVNode("text",{class:"item-detail-quality"},e.toDisplayString(l.getQualityText(i.selectedItem.品质||i.selectedItem.quality)),1)]),e.createElementVNode("view",{class:"item-detail-info"},[e.createElementVNode("text",{class:"item-detail-type"},"类型："+e.toDisplayString(l.getTypeText(i.selectedItem.类型||i.selectedItem.type)),1),e.createElementVNode("text",{class:"item-detail-quality-text"},"品质："+e.toDisplayString(l.getQualityText(i.selectedItem.品质||i.selectedItem.quality)),1),i.selectedItem.数量>1||i.selectedItem.quantity>1?(e.openBlock(),e.createElementBlock("text",{key:0,class:"item-detail-quantity"},"数量："+e.toDisplayString(i.selectedItem.数量||i.selectedItem.quantity),1)):e.createCommentVNode("",!0),l.getItemEffects(i.selectedItem).length>0?(e.openBlock(),e.createElementBlock("view",{key:1,class:"item-effects"},[e.createElementVNode("text",{class:"effects-title"},"属性加成："),e.createElementVNode("view",{class:"effects-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getItemEffects(i.selectedItem),(t=>(e.openBlock(),e.createElementBlock("text",{key:t.name,class:"effect-item"},e.toDisplayString(t.name)+"：+"+e.toDisplayString(t.value),1)))),128))])])):e.createCommentVNode("",!0),i.selectedItem.description||i.selectedItem.描述?(e.openBlock(),e.createElementBlock("text",{key:2,class:"item-detail-desc"},e.toDisplayString(i.selectedItem.description||i.selectedItem.描述),1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"item-detail-actions"},[l.canEquip(i.selectedItem)?(e.openBlock(),e.createElementBlock("button",{key:0,class:"detail-action-btn equip-btn",onClick:a[4]||(a[4]=(...e)=>l.equipItem&&l.equipItem(...e))},[e.createElementVNode("text",null,"⚔️ 装备")])):e.createCommentVNode("",!0),l.canUse(i.selectedItem)?(e.openBlock(),e.createElementBlock("button",{key:1,class:"detail-action-btn use-btn",onClick:a[5]||(a[5]=(...e)=>l.useItem&&l.useItem(...e))},[e.createElementVNode("text",null,"💊 使用")])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"detail-action-btn destroy-btn",onClick:a[6]||(a[6]=(...e)=>l.destroyItem&&l.destroyItem(...e))},[e.createElementVNode("text",null,"🗑️ 销毁")]),e.createElementVNode("button",{class:"detail-action-btn cancel-btn",onClick:a[7]||(a[7]=(...e)=>l.closeItemDetail&&l.closeItemDetail(...e))},[e.createElementVNode("text",null,"❌ 取消")])])])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-cd4b3612"]]);const h=n({data:()=>({player:{},craftingLevel:1,craftableItems:[],showDetail:!1,selectedRecipe:null,isLoading:!1}),computed:{},onLoad(){this.updateData(),this.loadCraftableItems()},onShow(){this.updateData(),this.loadCraftableItems()},onUnload(){},methods:{updateData(){this.player={...l.player},this.isAuthed=l.isAuthed||!1,this.craftingLevel=1},async loadCraftableItems(){try{this.isLoading=!0;let e=await s.sendMessage({type:"crafting_action",data:{action:"get_craftable_items"}});"get_craftable_success"!==e.type&&e.data&&e.data.craftable_items&&(e={type:"get_craftable_success",data:e.data}),this.isLoading=!1,"get_craftable_success"===e.type?(this.craftableItems=e.data.craftable_items||[],this.player.energy=e.data.energy||0):("get_craftable_failed"===e.type||"error"===e.type)&&uni.showToast({title:e.data.message||"获取可合成物品失败",icon:"none"})}catch(e){this.isLoading=!1,uni.showToast({title:"加载失败: "+(e.message||"未知错误"),icon:"none"})}},parseRecipe(e){const t={};if(!e)return t;if("object"==typeof e)return e;if("string"==typeof e&&e.trim().startsWith("{"))try{return JSON.parse(e)}catch(s){try{const t=e.replace(/'/g,'"');return JSON.parse(t)}catch(n){}}const a=e.split(",");for(const i of a)if(i.includes(":")){const[e,a]=i.split(":");t[e.trim()]=parseInt(a.trim())}return t},getMaterialCount(e){let t=0;return l.inventory.forEach((a=>{(a.name===e||a.id&&a.id.includes(e.replace("残页","_canyie")))&&(t+=a.quantity||1)})),t},getMissingMaterials(e){if(!e||!e.craft_recipe)return[];const t=this.parseRecipe(e.craft_recipe),a=[];for(const[s,n]of Object.entries(t))this.getMaterialCount(s)<n&&a.push(`${s}(需要${n}，现有${this.getMaterialCount(s)})`);return a},getQualityColor:e=>s.getQualityColor(e),getQualityName:e=>({common:"普通",fine:"精良",rare:"稀有",epic:"传说",legendary:"神品"}[e]||"普通"),getTypeName:e=>({book:"秘籍",consumable:"消耗品",weapon:"武器",armor:"防具",necklace:"项链",helmet:"头盔",offhand:"副手",accessory:"饰品",medal:"勋章"}[e]||e),canCraft(e){if(!e||!e.craft_recipe)return!1;if(void 0!==e.can_craft)return!(this.player.energy<10)&&e.can_craft;const t=this.parseRecipe(e.craft_recipe);for(const[a,s]of Object.entries(t))if(this.getMaterialCount(a)<s)return!1;return!(this.player.energy<10)},showRecipeDetail(e){this.selectedRecipe=e,this.showDetail=!0},closeDetail(){this.showDetail=!1,this.selectedRecipe=null},craftItem(e){l.isAuthed?this.canCraft(e)?uni.showModal({title:"确认合成",content:`确定要合成 ${e.name} 吗？\n消耗体力: 10点`,success:t=>{t.confirm&&this.performCraft(e)}}):uni.showToast({title:"材料或体力不足",icon:"none"}):uni.showToast({title:"请先登录",icon:"none"})},async performCraft(e){try{this.isLoading=!0;const t=await s.sendMessage({type:"crafting_action",data:{action:"craft_item",item_id:e.id}});this.isLoading=!1,"craft_success"===t.type?(uni.showToast({title:t.data.message||"合成成功",icon:"success"}),t.data.inventory&&(l.inventory=t.data.inventory),void 0!==t.data.energy&&(this.player.energy=t.data.energy,l.player.energy=t.data.energy),this.closeDetail(),this.loadCraftableItems()):("craft_failed"===t.type||"error"===t.type)&&uni.showToast({title:t.data.message||"合成失败",icon:"none"})}catch(a){t("error","at pages/crafting/crafting.vue:394","合成失败:",a),this.isLoading=!1,uni.showToast({title:"合成失败: "+(a.message||"未知错误"),icon:"none"})}}}},[["render",function(t,a,s,n,i,l){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("view",{class:"crafting-level"},[e.createElementVNode("text",{class:"level-label"},"打造等级:"),e.createElementVNode("text",{class:"level-value"},e.toDisplayString(i.craftingLevel),1)]),e.createElementVNode("view",{class:"stamina-info"},[e.createElementVNode("text",{class:"stamina-label"},"体力:"),e.createElementVNode("text",{class:"stamina-value"},e.toDisplayString(i.player.energy||0)+"/"+e.toDisplayString(i.player.max_energy||100),1)])]),e.createElementVNode("view",{class:"recipes-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"可合成物品 ("+e.toDisplayString(i.craftableItems.length)+")",1),e.createElementVNode("button",{class:"refresh-btn",onClick:a[0]||(a[0]=(...e)=>l.loadCraftableItems&&l.loadCraftableItems(...e)),disabled:i.isLoading},e.toDisplayString(i.isLoading?"加载中...":"刷新"),9,["disabled"])]),i.isLoading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"loading-container"},[e.createElementVNode("text",{class:"loading-text"},"正在加载可合成物品...")])):0===i.craftableItems.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-container"},[e.createElementVNode("text",{class:"empty-icon"},"🔧"),e.createElementVNode("text",{class:"empty-text"},"暂无可合成物品"),e.createElementVNode("text",{class:"empty-desc"},"请收集更多材料来解锁合成配方")])):(e.openBlock(),e.createElementBlock("view",{key:2,class:"recipes-grid"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.craftableItems,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["recipe-item",{"can-craft":t.can_craft,"cannot-craft":!t.can_craft}]),onClick:e=>l.showRecipeDetail(t)},[e.createElementVNode("text",{class:"recipe-icon"},e.toDisplayString(t.icon||"📦"),1),e.createElementVNode("text",{class:"recipe-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"recipe-quality"},e.toDisplayString(l.getQualityName(t.quality)),1),e.createElementVNode("view",{class:"recipe-type"},e.toDisplayString(l.getTypeName(t.type)),1),e.createElementVNode("view",{class:"craft-status"},[t.can_craft?(e.openBlock(),e.createElementBlock("text",{key:0,class:"can-craft-text"},"可合成")):(e.openBlock(),e.createElementBlock("text",{key:1,class:"cannot-craft-text"},"材料不足"))])],10,["onClick"])))),128))]))]),i.showDetail?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-modal",onClick:a[4]||(a[4]=(...e)=>l.closeDetail&&l.closeDetail(...e))},[e.createElementVNode("view",{class:"detail-content",onClick:a[3]||(a[3]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"detail-header"},[e.createElementVNode("text",{class:"detail-title"},e.toDisplayString(i.selectedRecipe.name),1),e.createElementVNode("text",{class:"detail-quality"},e.toDisplayString(l.getQualityName(i.selectedRecipe.quality)),1)]),e.createElementVNode("view",{class:"detail-info"},[e.createElementVNode("text",{class:"detail-desc"},e.toDisplayString(i.selectedRecipe.description),1),e.createElementVNode("text",{class:"detail-stamina"},"消耗体力: 10点"),e.createElementVNode("view",{class:"materials-section"},[e.createElementVNode("text",{class:"materials-title"},"所需材料:"),e.createElementVNode("view",{class:"materials-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.parseRecipe(i.selectedRecipe.craft_recipe),((t,a)=>(e.openBlock(),e.createElementBlock("view",{key:a,class:e.normalizeClass(["material-item",{sufficient:l.getMaterialCount(a)>=t,insufficient:l.getMaterialCount(a)<t}])},[e.createElementVNode("text",{class:"material-name"},e.toDisplayString(a),1),e.createElementVNode("text",{class:"material-quantity"},e.toDisplayString(l.getMaterialCount(a))+"/"+e.toDisplayString(t),1)],2)))),128))]),l.getMissingMaterials(i.selectedRecipe).length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"missing-materials"},[e.createElementVNode("text",{class:"missing-title"},"缺少材料:"),e.createElementVNode("view",{class:"missing-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.getMissingMaterials(i.selectedRecipe),(t=>(e.openBlock(),e.createElementBlock("text",{key:t,class:"missing-item"},e.toDisplayString(t),1)))),128))])])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modal-btn cancel-btn",onClick:a[1]||(a[1]=(...e)=>l.closeDetail&&l.closeDetail(...e))},"关闭"),e.createElementVNode("button",{class:"modal-btn confirm-btn",onClick:a[2]||(a[2]=e=>l.craftItem(i.selectedRecipe)),disabled:!l.canCraft(i.selectedRecipe)}," 合成 ",8,["disabled"])])])])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-e9ecef63"]]);__definePage("pages/login/login",i),__definePage("pages/index/index",o),__definePage("pages/character/character",c),__definePage("pages/skills/skills",r),__definePage("pages/shop/shop",d),__definePage("pages/guild/guild",m),__definePage("pages/character/backpack",u),__definePage("pages/crafting/crafting",h);const p={onLaunch:function(){t("log","at App.vue:4","App Launch")},onShow:function(){t("log","at App.vue:7","App Show")},onHide:function(){t("log","at App.vue:10","App Hide");try{const e=require("./utils/websocket.js").default||require("./utils/websocket.js");e&&"function"==typeof e.disconnect&&(e.disconnect(),t("log","at App.vue:16","全局 WebSocket 已断开"))}catch(e){t("error","at App.vue:19","断开 WebSocket 失败:",e)}}};const{app:g,Vuex:y,Pinia:k}={app:e.createVueApp(p)};uni.Vuex=y,uni.Pinia=k,g.provide("__globalStyles",__uniConfig.styles),g._component.mpType="app",g._component.render=()=>{},g.mount("#app")}(Vue);
