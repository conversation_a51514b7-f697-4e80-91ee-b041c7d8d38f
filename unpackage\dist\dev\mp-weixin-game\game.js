/**
 * 仗剑江湖行 - 微信小游戏简化版入口
 * 确保编译通过的版本
 */

console.log('=== 仗剑江湖行小游戏启动 ===');

// 检查微信小游戏环境
if (typeof wx === 'undefined') {
  console.error('未检测到微信小游戏环境');
  throw new Error('此游戏只能在微信小游戏环境中运行');
}

// 获取系统信息
let systemInfo;
try {
  systemInfo = wx.getSystemInfoSync();
  console.log('系统信息:', systemInfo);
} catch (error) {
  console.error('获取系统信息失败:', error);
  systemInfo = { screenWidth: 375, screenHeight: 667 };
}

// 创建Canvas
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 设置Canvas尺寸
canvas.width = systemInfo.screenWidth;
canvas.height = systemInfo.screenHeight;

console.log('Canvas创建成功:', canvas.width, 'x', canvas.height);

// 游戏状态
const gameState = {
  currentScene: 'login',
  isLoggedIn: false,
  playerData: null,
  formData: {
    username: '',
    password: ''
  },
  isRegistering: false,
  registerData: {
    username: '',
    password: '',
    confirmPassword: '',
    characterName: '',
    gender: 'male'
  }
};

// UI配置
const uiConfig = {
  colors: {
    primary: '#2c3e50',
    secondary: '#3498db',
    success: '#27ae60',
    warning: '#f39c12',
    danger: '#e74c3c',
    white: '#ffffff',
    text: '#2c3e50',
    textSecondary: '#7f8c8d',
    background: '#ecf0f1',
    border: '#bdc3c7'
  },
  fonts: {
    title: 'bold 32px Arial',
    large: 'bold 24px Arial',
    normal: '18px Arial',
    small: '14px Arial'
  }
};

// 网络管理
let socket = null;
let isConnected = false;

// 连接WebSocket
function connectWebSocket() {
  try {
    console.log('连接WebSocket服务器...');
    
    socket = wx.connectSocket({
      url: 'ws://localhost:8080',
      success: () => {
        console.log('WebSocket连接请求发送成功');
      },
      fail: (error) => {
        console.error('WebSocket连接失败:', error);
        showToast('网络连接失败');
      }
    });
    
    socket.onOpen(() => {
      console.log('WebSocket连接已建立');
      isConnected = true;
      showToast('网络连接成功');
    });
    
    socket.onMessage((res) => {
      handleMessage(res.data);
    });
    
    socket.onClose(() => {
      console.log('WebSocket连接已关闭');
      isConnected = false;
      showToast('网络连接断开');
    });
    
    socket.onError((error) => {
      console.error('WebSocket连接错误:', error);
      isConnected = false;
      showToast('网络连接错误');
    });
    
  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    showToast('网络初始化失败');
  }
}

// 处理WebSocket消息
function handleMessage(data) {
  try {
    const message = JSON.parse(data);
    console.log('收到服务器消息:', message);
    
    switch (message.type) {
      case 'login_success':
        handleLoginSuccess(message.data);
        break;
      case 'login_failed':
        handleLoginFailed(message.message);
        break;
      case 'register_success':
        handleRegisterSuccess(message.data);
        break;
      case 'register_failed':
        handleRegisterFailed(message.message);
        break;
      case 'player_data':
        handlePlayerData(message.data);
        break;
      default:
        console.log('未处理的消息类型:', message.type);
    }
  } catch (error) {
    console.error('解析服务器消息失败:', error);
  }
}

// 发送消息
function sendMessage(data) {
  if (!isConnected || !socket) {
    showToast('网络未连接');
    return;
  }
  
  try {
    const message = JSON.stringify(data);
    socket.send({
      data: message,
      success: () => {
        console.log('消息发送成功:', data);
      },
      fail: (error) => {
        console.error('消息发送失败:', error);
        showToast('消息发送失败');
      }
    });
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}

// 处理登录成功
function handleLoginSuccess(data) {
  console.log('登录成功:', data);
  gameState.isLoggedIn = true;
  gameState.playerData = data;
  gameState.currentScene = 'index';
  showToast('登录成功');
  render();
}

// 处理登录失败
function handleLoginFailed(message) {
  console.log('登录失败:', message);
  showToast('登录失败: ' + message);
}

// 处理注册成功
function handleRegisterSuccess(data) {
  console.log('注册成功:', data);
  showToast('注册成功，请登录');
  gameState.isRegistering = false;
  render();
}

// 处理注册失败
function handleRegisterFailed(message) {
  console.log('注册失败:', message);
  showToast('注册失败: ' + message);
}

// 处理玩家数据
function handlePlayerData(data) {
  console.log('更新玩家数据:', data);
  gameState.playerData = { ...gameState.playerData, ...data };
  render();
}

// 绘制文本
function drawText(text, x, y, options = {}) {
  const {
    font = uiConfig.fonts.normal,
    color = uiConfig.colors.text,
    align = 'left',
    baseline = 'top'
  } = options;
  
  ctx.font = font;
  ctx.fillStyle = color;
  ctx.textAlign = align;
  ctx.textBaseline = baseline;
  ctx.fillText(text, x, y);
}

// 绘制按钮
function drawButton(text, x, y, width, height, options = {}) {
  const {
    backgroundColor = uiConfig.colors.primary,
    textColor = uiConfig.colors.white,
    font = uiConfig.fonts.normal
  } = options;
  
  // 绘制按钮背景
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(x, y, width, height);
  
  // 绘制按钮文本
  drawText(text, x + width / 2, y + height / 2, {
    font,
    color: textColor,
    align: 'center',
    baseline: 'middle'
  });
  
  return { x, y, width, height };
}

// 绘制输入框
function drawInputBox(x, y, width, height, value, placeholder) {
  // 绘制背景
  ctx.fillStyle = uiConfig.colors.white;
  ctx.fillRect(x, y, width, height);
  
  // 绘制边框
  ctx.strokeStyle = uiConfig.colors.border;
  ctx.lineWidth = 1;
  ctx.strokeRect(x, y, width, height);
  
  // 绘制文本
  const displayText = value || placeholder;
  const textColor = value ? uiConfig.colors.text : uiConfig.colors.textSecondary;
  
  drawText(displayText, x + 10, y + height / 2, {
    color: textColor,
    baseline: 'middle'
  });
  
  return { x, y, width, height };
}

// 检查点是否在矩形内
function isPointInRect(x, y, rect) {
  return x >= rect.x && x <= rect.x + rect.width &&
         y >= rect.y && y <= rect.y + rect.height;
}

// 显示输入对话框
function showInputDialog(title, placeholder, callback) {
  wx.showModal({
    title: title,
    editable: true,
    placeholderText: placeholder,
    success: (res) => {
      if (res.confirm && callback) {
        callback(res.content || '');
      }
    }
  });
}

// 显示提示
function showToast(message) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}

// 渲染当前场景
function render() {
  switch (gameState.currentScene) {
    case 'login':
      renderLoginPage();
      break;
    case 'index':
      renderIndexPage();
      break;
  }
}

// 渲染登录页面
function renderLoginPage() {
  // 清空画布并绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制标题
  drawText('仗剑江湖行', canvas.width / 2, 80, {
    font: uiConfig.fonts.title,
    color: uiConfig.colors.white,
    align: 'center'
  });
  
  drawText('微信小游戏版', canvas.width / 2, 130, {
    font: uiConfig.fonts.normal,
    color: uiConfig.colors.white,
    align: 'center'
  });
  
  // 绘制表单
  const formX = 30;
  const formY = 180;
  const formWidth = canvas.width - 60;
  
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.fillRect(formX, formY, formWidth, gameState.isRegistering ? 450 : 320);
  
  if (gameState.isRegistering) {
    renderRegisterForm(formX, formY, formWidth);
  } else {
    renderLoginForm(formX, formY, formWidth);
  }
}

// 渲染登录表单
function renderLoginForm(formX, formY, formWidth) {
  // 表单标题
  drawText('登录江湖', formX + formWidth / 2, formY + 30, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.text,
    align: 'center'
  });
  
  // 账号输入框
  drawText('账号:', formX + 20, formY + 80);
  gameState.usernameInput = drawInputBox(
    formX + 20, formY + 105, formWidth - 40, 40,
    gameState.formData.username, '请输入账号'
  );
  
  // 密码输入框
  drawText('密码:', formX + 20, formY + 160);
  gameState.passwordInput = drawInputBox(
    formX + 20, formY + 185, formWidth - 40, 40,
    gameState.formData.password ? '••••••••' : '', '请输入密码'
  );
  
  // 登录按钮
  gameState.loginButton = drawButton(
    '登录', formX + 20, formY + 245, formWidth - 40, 45,
    { backgroundColor: uiConfig.colors.primary, font: 'bold 20px Arial' }
  );
  
  // 注册链接
  drawText('还没有账号？点击注册', formX + formWidth / 2, formY + 310, {
    font: uiConfig.fonts.small,
    color: uiConfig.colors.secondary,
    align: 'center'
  });
  
  gameState.registerLink = {
    x: formX + 20,
    y: formY + 300,
    width: formWidth - 40,
    height: 20
  };
}

// 渲染注册表单
function renderRegisterForm(formX, formY, formWidth) {
  // 实现注册表单渲染
  drawText('创建角色', formX + formWidth / 2, formY + 30, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.text,
    align: 'center'
  });
  
  // 简化版注册表单
  drawText('注册功能开发中...', formX + formWidth / 2, formY + 100, {
    font: uiConfig.fonts.normal,
    color: uiConfig.colors.textSecondary,
    align: 'center'
  });
  
  // 返回登录按钮
  gameState.backToLoginButton = drawButton(
    '返回登录', formX + 20, formY + 150, formWidth - 40, 45,
    { backgroundColor: uiConfig.colors.secondary }
  );
}

// 渲染主页面
function renderIndexPage() {
  // 清空画布
  ctx.fillStyle = uiConfig.colors.background;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制头部
  ctx.fillStyle = uiConfig.colors.primary;
  ctx.fillRect(0, 0, canvas.width, 60);
  
  drawText('仗剑江湖行', canvas.width / 2, 30, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.white,
    align: 'center',
    baseline: 'middle'
  });
  
  // 绘制玩家信息
  const contentY = 100;
  
  drawText('角色信息', 30, contentY, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.text
  });
  
  if (gameState.playerData) {
    drawText(`角色名: ${gameState.playerData.characterName || '测试玩家'}`, 30, contentY + 35);
    drawText(`等级: ${gameState.playerData.level || 1}`, 30, contentY + 65);
    drawText(`银两: ${gameState.playerData.money || 1000}`, 30, contentY + 95);
  }
  
  // 功能按钮
  gameState.adventureButton = drawButton(
    '闯江湖', 30, contentY + 150, canvas.width - 60, 50,
    { backgroundColor: uiConfig.colors.success, font: 'bold 20px Arial' }
  );
  
  gameState.logoutButton = drawButton(
    '退出登录', 30, contentY + 220, canvas.width - 60, 40,
    { backgroundColor: uiConfig.colors.danger }
  );
}

// 处理触摸事件
function handleTap(x, y) {
  console.log('点击事件:', x, y, '当前场景:', gameState.currentScene);
  
  if (gameState.currentScene === 'login') {
    if (gameState.isRegistering) {
      // 注册页面处理
      if (gameState.backToLoginButton && isPointInRect(x, y, gameState.backToLoginButton)) {
        gameState.isRegistering = false;
        render();
      }
    } else {
      // 登录页面处理
      if (gameState.usernameInput && isPointInRect(x, y, gameState.usernameInput)) {
        showInputDialog('输入账号', '请输入账号', (value) => {
          gameState.formData.username = value;
          render();
        });
      } else if (gameState.passwordInput && isPointInRect(x, y, gameState.passwordInput)) {
        showInputDialog('输入密码', '请输入密码', (value) => {
          gameState.formData.password = value;
          render();
        });
      } else if (gameState.loginButton && isPointInRect(x, y, gameState.loginButton)) {
        handleLogin();
      } else if (gameState.registerLink && isPointInRect(x, y, gameState.registerLink)) {
        gameState.isRegistering = true;
        render();
      }
    }
  } else if (gameState.currentScene === 'index') {
    // 主页面处理
    if (gameState.adventureButton && isPointInRect(x, y, gameState.adventureButton)) {
      showToast('开始冒险...');
      sendMessage({ type: 'adventure', action: 'start' });
    } else if (gameState.logoutButton && isPointInRect(x, y, gameState.logoutButton)) {
      gameState.currentScene = 'login';
      gameState.isLoggedIn = false;
      gameState.playerData = null;
      render();
    }
  }
}

// 处理登录
function handleLogin() {
  const { username, password } = gameState.formData;
  
  if (!username || !password) {
    showToast('请输入账号和密码');
    return;
  }
  
  console.log('执行登录:', username);
  showToast('正在登录...');
  
  sendMessage({
    type: 'login',
    data: { username, password }
  });
}

// 注册触摸事件
wx.onTouchEnd((e) => {
  if (!e.touches || e.touches.length === 0) return;
  
  const touch = e.touches[0];
  const x = touch.clientX;
  const y = touch.clientY;
  
  handleTap(x, y);
});

// 生命周期事件
wx.onShow(() => {
  console.log('游戏进入前台');
});

wx.onHide(() => {
  console.log('游戏进入后台');
});

// 初始化游戏
function initGame() {
  console.log('初始化游戏...');
  
  // 连接WebSocket
  connectWebSocket();
  
  // 渲染初始页面
  render();
  
  console.log('游戏初始化完成');
}

// 启动游戏
initGame();

console.log('=== 仗剑江湖行小游戏启动完成 ===');
