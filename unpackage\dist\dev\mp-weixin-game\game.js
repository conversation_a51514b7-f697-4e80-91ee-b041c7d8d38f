/**
 * 仗剑江湖行 - 调试版本
 * 专门用于解决渲染和输入问题
 */

console.log('=== 调试版本启动 ===');

// 检查微信小游戏环境
if (typeof wx === 'undefined') {
  console.error('未检测到微信小游戏环境');
  throw new Error('此游戏只能在微信小游戏环境中运行');
}

// 获取系统信息
const systemInfo = wx.getSystemInfoSync();
console.log('系统信息:', systemInfo);

// 创建Canvas
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 设置Canvas尺寸
canvas.width = systemInfo.screenWidth;
canvas.height = systemInfo.screenHeight;

console.log('Canvas创建成功:', canvas.width, 'x', canvas.height);

// 游戏状态
const gameState = {
  currentScene: 'login',
  isRegistering: false,
  formData: {
    username: '',
    password: ''
  },
  registerData: {
    username: '',
    password: '',
    confirmPassword: '',
    characterName: '',
    gender: 'male'
  },
  connectionStatus: '未连接',
  isLoggedIn: false,
  player: {
    character_name: '',
    level: 1,
    money: 0
  }
};

// 网络管理
let socket = null;

// 连接WebSocket
function connectWebSocket() {
  try {
    console.log('连接WebSocket服务器...');
    
    socket = wx.connectSocket({
      url: 'ws://localhost:8080',
      success: () => {
        console.log('WebSocket连接请求发送成功');
      },
      fail: (error) => {
        console.error('WebSocket连接失败:', error);
        gameState.connectionStatus = '连接失败';
        showToast('网络连接失败');
      }
    });
    
    socket.onOpen(() => {
      console.log('WebSocket连接已建立');
      gameState.connectionStatus = '已连接';
      showToast('网络连接成功');
      render(); // 连接成功后重新渲染
    });
    
    socket.onMessage((res) => {
      handleMessage(res.data);
    });
    
    socket.onClose(() => {
      console.log('WebSocket连接已关闭');
      gameState.connectionStatus = '连接断开';
      render();
    });
    
    socket.onError((error) => {
      console.error('WebSocket连接错误:', error);
      gameState.connectionStatus = '连接错误';
      showToast('网络连接错误');
    });
    
  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    gameState.connectionStatus = '初始化失败';
    showToast('网络初始化失败');
  }
}

// 处理WebSocket消息
function handleMessage(data) {
  try {
    const message = JSON.parse(data);
    console.log('收到服务器消息:', message);
    
    switch (message.type) {
      case 'login_success':
        handleLoginSuccess(message.data);
        break;
      case 'login_failed':
        handleLoginFailed(message.message);
        break;
      case 'register_success':
        handleRegisterSuccess(message.data);
        break;
      case 'register_failed':
        handleRegisterFailed(message.message);
        break;
      default:
        console.log('未处理的消息类型:', message.type);
    }
  } catch (error) {
    console.error('解析服务器消息失败:', error);
  }
}

// 发送消息
function sendMessage(data) {
  if (!socket || gameState.connectionStatus !== '已连接') {
    showToast('网络未连接');
    return;
  }
  
  try {
    const message = JSON.stringify(data);
    socket.send({
      data: message,
      success: () => {
        console.log('消息发送成功:', data);
      },
      fail: (error) => {
        console.error('消息发送失败:', error);
        showToast('消息发送失败');
      }
    });
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}

// 处理登录成功
function handleLoginSuccess(data) {
  console.log('登录成功:', data);
  gameState.isLoggedIn = true;
  gameState.player = { ...gameState.player, ...data };
  gameState.currentScene = 'index';
  showToast('登录成功');
  render();
}

// 处理登录失败
function handleLoginFailed(message) {
  console.log('登录失败:', message);
  showToast('登录失败: ' + message);
}

// 处理注册成功
function handleRegisterSuccess(data) {
  console.log('注册成功:', data);
  showToast('注册成功，请登录');
  gameState.isRegistering = false;
  render();
}

// 处理注册失败
function handleRegisterFailed(message) {
  console.log('注册失败:', message);
  showToast('注册失败: ' + message);
}

// UI工具方法
function showToast(message) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}

function showInputDialog(title, placeholder, callback) {
  wx.showModal({
    title: title,
    editable: true,
    placeholderText: placeholder,
    success: (res) => {
      if (res.confirm && callback) {
        callback(res.content || '');
      }
    }
  });
}

// 检查点是否在矩形内
function isPointInRect(x, y, rect) {
  return x >= rect.x && x <= rect.x + rect.width &&
         y >= rect.y && y <= rect.y + rect.height;
}

// 渲染方法
function render() {
  console.log('=== 开始渲染 ===');
  console.log('当前场景:', gameState.currentScene);
  
  try {
    switch (gameState.currentScene) {
      case 'login':
        renderLoginScene();
        break;
      case 'index':
        renderIndexScene();
        break;
      default:
        renderLoginScene();
    }
    console.log('=== 渲染完成 ===');
  } catch (error) {
    console.error('渲染失败:', error);
    renderErrorScene(error.message);
  }
}

// 渲染登录场景
function renderLoginScene() {
  console.log('渲染登录场景...');
  
  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制标题
  ctx.font = 'bold 32px Arial';
  ctx.fillStyle = '#ffffff';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 100);
  
  ctx.font = '18px Arial';
  ctx.fillText('微信小游戏版', canvas.width / 2, 140);
  
  // 绘制表单背景
  const formX = 30;
  const formY = 180;
  const formWidth = canvas.width - 60;
  const formHeight = gameState.isRegistering ? 400 : 300;
  
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.fillRect(formX, formY, formWidth, formHeight);
  
  if (gameState.isRegistering) {
    renderRegisterForm(formX, formY, formWidth);
  } else {
    renderLoginForm(formX, formY, formWidth);
  }
  
  console.log('登录场景渲染完成');
}

// 渲染登录表单
function renderLoginForm(formX, formY, formWidth) {
  console.log('渲染登录表单...');
  
  // 表单标题
  ctx.font = 'bold 24px Arial';
  ctx.fillStyle = '#2c3e50';
  ctx.textAlign = 'center';
  ctx.fillText('登录江湖', formX + formWidth / 2, formY + 40);
  
  // 账号输入框
  ctx.font = '16px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('账号:', formX + 20, formY + 80);
  
  // 绘制输入框
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(formX + 20, formY + 100, formWidth - 40, 40);
  ctx.strokeStyle = '#bdc3c7';
  ctx.lineWidth = 2;
  ctx.strokeRect(formX + 20, formY + 100, formWidth - 40, 40);
  
  // 输入框文本
  ctx.fillStyle = gameState.formData.username ? '#2c3e50' : '#7f8c8d';
  ctx.fillText(gameState.formData.username || '请输入账号', formX + 30, formY + 125);
  
  gameState.usernameInput = {
    x: formX + 20,
    y: formY + 100,
    width: formWidth - 40,
    height: 40
  };
  
  // 密码输入框
  ctx.fillStyle = '#2c3e50';
  ctx.fillText('密码:', formX + 20, formY + 160);
  
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(formX + 20, formY + 180, formWidth - 40, 40);
  ctx.strokeStyle = '#bdc3c7';
  ctx.strokeRect(formX + 20, formY + 180, formWidth - 40, 40);
  
  ctx.fillStyle = gameState.formData.password ? '#2c3e50' : '#7f8c8d';
  const passwordText = gameState.formData.password ? '••••••••' : '请输入密码';
  ctx.fillText(passwordText, formX + 30, formY + 205);
  
  gameState.passwordInput = {
    x: formX + 20,
    y: formY + 180,
    width: formWidth - 40,
    height: 40
  };
  
  // 登录按钮
  ctx.fillStyle = '#2c3e50';
  ctx.fillRect(formX + 20, formY + 240, formWidth - 40, 45);
  
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 20px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('登录', formX + formWidth / 2, formY + 267);
  
  gameState.loginButton = {
    x: formX + 20,
    y: formY + 240,
    width: formWidth - 40,
    height: 45
  };
  
  // 注册链接
  ctx.fillStyle = '#3498db';
  ctx.font = '14px Arial';
  ctx.fillText('还没有账号？点击注册', formX + formWidth / 2, formY + 310);
  
  gameState.registerLink = {
    x: formX + 20,
    y: formY + 300,
    width: formWidth - 40,
    height: 20
  };
  
  console.log('登录表单渲染完成，输入框位置:', gameState.usernameInput, gameState.passwordInput);
}

// 渲染注册表单
function renderRegisterForm(formX, formY, formWidth) {
  console.log('渲染注册表单...');
  
  // 简化的注册表单
  ctx.font = 'bold 24px Arial';
  ctx.fillStyle = '#2c3e50';
  ctx.textAlign = 'center';
  ctx.fillText('创建角色', formX + formWidth / 2, formY + 40);
  
  // 返回登录按钮
  ctx.fillStyle = '#3498db';
  ctx.fillRect(formX + 20, formY + 100, formWidth - 40, 45);
  
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 20px Arial';
  ctx.fillText('返回登录', formX + formWidth / 2, formY + 127);
  
  gameState.backToLoginButton = {
    x: formX + 20,
    y: formY + 100,
    width: formWidth - 40,
    height: 45
  };
  
  console.log('注册表单渲染完成');
}

// 渲染主页面
function renderIndexScene() {
  console.log('渲染主页面...');
  
  // 清空画布
  ctx.fillStyle = '#ecf0f1';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制头部
  ctx.fillStyle = '#2c3e50';
  ctx.fillRect(0, 0, canvas.width, 60);
  
  ctx.font = 'bold 24px Arial';
  ctx.fillStyle = '#ffffff';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 30);
  
  // 绘制玩家信息
  const player = gameState.player;
  ctx.font = '18px Arial';
  ctx.fillStyle = '#2c3e50';
  ctx.textAlign = 'left';
  ctx.fillText(`角色: ${player.character_name || '测试玩家'}`, 20, 100);
  ctx.fillText(`等级: ${player.level || 1}`, 20, 130);
  ctx.fillText(`银两: ${player.money || 0}`, 20, 160);
  
  console.log('主页面渲染完成');
}

// 渲染错误场景
function renderErrorScene(message) {
  console.log('渲染错误场景:', message);
  
  ctx.fillStyle = '#ff6b6b';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  ctx.font = 'bold 24px Arial';
  ctx.fillStyle = '#ffffff';
  ctx.textAlign = 'center';
  ctx.fillText('渲染错误', canvas.width / 2, 100);
  
  ctx.font = '16px Arial';
  ctx.fillText(message, canvas.width / 2, 150);
}

// 处理触摸事件
function handleTap(x, y) {
  console.log('=== 点击事件 ===');
  console.log('点击坐标:', x, y);
  console.log('当前场景:', gameState.currentScene);
  console.log('是否注册页面:', gameState.isRegistering);
  
  if (gameState.currentScene === 'login') {
    if (gameState.isRegistering) {
      // 注册页面处理
      console.log('处理注册页面点击');
      if (gameState.backToLoginButton && isPointInRect(x, y, gameState.backToLoginButton)) {
        console.log('点击返回登录按钮');
        gameState.isRegistering = false;
        render();
      }
    } else {
      // 登录页面处理
      console.log('处理登录页面点击');
      console.log('账号输入框位置:', gameState.usernameInput);
      console.log('密码输入框位置:', gameState.passwordInput);
      
      if (gameState.usernameInput && isPointInRect(x, y, gameState.usernameInput)) {
        console.log('点击了账号输入框');
        showInputDialog('输入账号', '请输入账号', (value) => {
          console.log('账号输入:', value);
          gameState.formData.username = value;
          render();
        });
      } else if (gameState.passwordInput && isPointInRect(x, y, gameState.passwordInput)) {
        console.log('点击了密码输入框');
        showInputDialog('输入密码', '请输入密码', (value) => {
          console.log('密码输入:', value);
          gameState.formData.password = value;
          render();
        });
      } else if (gameState.loginButton && isPointInRect(x, y, gameState.loginButton)) {
        console.log('点击了登录按钮');
        handleLogin();
      } else if (gameState.registerLink && isPointInRect(x, y, gameState.registerLink)) {
        console.log('点击了注册链接');
        gameState.isRegistering = true;
        render();
      } else {
        console.log('点击了空白区域');
      }
    }
  }
  
  console.log('=== 点击事件处理完成 ===');
}

// 处理登录
function handleLogin() {
  const { username, password } = gameState.formData;
  
  if (!username || !password) {
    showToast('请输入账号和密码');
    return;
  }
  
  console.log('执行登录:', username);
  showToast('正在登录...');
  
  sendMessage({
    type: 'login',
    data: { username, password }
  });
}

// 触摸事件已在 initGame() 中注册

// 生命周期事件
wx.onShow(() => {
  console.log('游戏进入前台');
});

wx.onHide(() => {
  console.log('游戏进入后台');
});

// 初始化游戏
function initGame() {
  console.log('=== 初始化调试版游戏 ===');

  // 注册触摸事件（提前注册）
  console.log('注册触摸事件...');

  wx.onTouchEnd((e) => {
    console.log('触摸事件触发:', e);
    console.log('事件对象键:', Object.keys(e));

    let x, y;

    // 尝试多种方式获取触摸坐标
    if (e.touches && e.touches.length > 0) {
      console.log('使用 e.touches[0]');
      const touch = e.touches[0];
      x = touch.clientX || touch.x || touch.pageX;
      y = touch.clientY || touch.y || touch.pageY;
    } else if (e.changedTouches && e.changedTouches.length > 0) {
      console.log('使用 e.changedTouches[0]');
      const touch = e.changedTouches[0];
      x = touch.clientX || touch.x || touch.pageX;
      y = touch.clientY || touch.y || touch.pageY;
    } else if (e.x !== undefined && e.y !== undefined) {
      console.log('使用 e.x, e.y');
      x = e.x;
      y = e.y;
    } else if (e.clientX !== undefined && e.clientY !== undefined) {
      console.log('使用 e.clientX, e.clientY');
      x = e.clientX;
      y = e.clientY;
    } else {
      console.log('无法获取触摸坐标，事件对象:', e);
      console.log('尝试使用默认坐标');
      x = 100;
      y = 100;
    }

    console.log('最终触摸坐标:', x, y);

    if (x !== undefined && y !== undefined) {
      handleTap(x, y);
    } else {
      console.log('坐标获取失败');
    }
  });

  // 也注册 onTouchStart 事件作为备用
  wx.onTouchStart((e) => {
    console.log('触摸开始事件:', e);
    console.log('开始事件对象键:', Object.keys(e));

    // 尝试从开始事件获取坐标
    if (e.touches && e.touches.length > 0) {
      console.log('开始事件有touches:', e.touches[0]);
    } else if (e.x !== undefined && e.y !== undefined) {
      console.log('开始事件坐标:', e.x, e.y);
    }
  });

  console.log('触摸事件注册完成');

  // 尝试备用的触摸事件注册方式
  try {
    if (typeof wx.onTap === 'function') {
      console.log('注册备用点击事件...');
      wx.onTap((e) => {
        console.log('点击事件触发:', e);
        const x = e.x || e.clientX || 100;
        const y = e.y || e.clientY || 100;
        console.log('点击坐标:', x, y);
        handleTap(x, y);
      });
    }
  } catch (error) {
    console.log('备用点击事件注册失败:', error);
  }

  // 测试触摸事件是否正常工作
  setTimeout(() => {
    console.log('=== 触摸事件测试 ===');
    console.log('请点击屏幕任意位置测试触摸事件...');
    console.log('如果看到这条消息后点击屏幕没有反应，说明触摸事件有问题');
  }, 2000);

  // 连接WebSocket
  connectWebSocket();

  // 首次渲染
  console.log('执行首次渲染...');
  render();

  // 延迟再次渲染，确保界面显示
  setTimeout(() => {
    console.log('执行延迟渲染...');
    render();
  }, 500);

  console.log('=== 调试版游戏初始化完成 ===');
}

// 启动游戏
initGame();

console.log('=== 调试版本启动完成 ===');
