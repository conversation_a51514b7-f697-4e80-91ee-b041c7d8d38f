// 仗剑江湖行 - 小游戏入口文件
console.log('=== 仗剑江湖行小游戏启动 ===');

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');
  
  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight
    });
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');
  });

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告:', res.level);
      if (wx.triggerGC) {
        wx.triggerGC();
      }
    });
  }

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');
}

// 引入主应用
require('./main.js');

console.log('=== 仗剑江湖行小游戏启动完成 ===');
