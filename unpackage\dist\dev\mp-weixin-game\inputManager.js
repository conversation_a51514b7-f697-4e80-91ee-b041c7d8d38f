/**
 * 输入管理器 - 处理所有用户输入
 */

class InputManager {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.touchStartPos = null;
    this.touchEndPos = null;
    this.lastTouchTime = 0;
    this.doubleTapThreshold = 300; // 双击时间阈值
  }
  
  // 处理触摸开始
  handleTouchStart(e) {
    if (!e.touches || e.touches.length === 0) return;
    
    const touch = e.touches[0];
    this.touchStartPos = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
  }
  
  // 处理触摸移动
  handleTouchMove(e) {
    // 可以在这里处理拖拽等操作
  }
  
  // 处理触摸结束
  handleTouchEnd(e) {
    if (!e.changedTouches || e.changedTouches.length === 0) return;
    
    const touch = e.changedTouches[0];
    this.touchEndPos = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };
    
    // 检查是否是有效的点击
    if (this.isValidTap()) {
      this.handleTap(this.touchEndPos.x, this.touchEndPos.y);
    }
  }
  
  // 检查是否是有效的点击
  isValidTap() {
    if (!this.touchStartPos || !this.touchEndPos) return false;
    
    const distance = Math.sqrt(
      Math.pow(this.touchEndPos.x - this.touchStartPos.x, 2) +
      Math.pow(this.touchEndPos.y - this.touchStartPos.y, 2)
    );
    
    const duration = this.touchEndPos.time - this.touchStartPos.time;
    
    // 移动距离小于30像素，持续时间小于500毫秒
    return distance < 30 && duration < 500;
  }
  
  // 处理点击事件
  handleTap(x, y) {
    console.log('点击事件:', x, y, '当前场景:', this.gameEngine.gameState.currentScene);
    
    // 将点击事件传递给当前场景
    if (this.gameEngine.sceneManager) {
      this.gameEngine.sceneManager.handleTap(x, y);
    }
  }
  
  // 显示输入对话框
  showInputDialog(title, placeholder, callback, options = {}) {
    const {
      maxLength = 20,
      inputType = 'text'
    } = options;
    
    wx.showModal({
      title: title,
      editable: true,
      placeholderText: placeholder,
      success: (res) => {
        if (res.confirm && callback) {
          let value = res.content || '';
          
          // 长度限制
          if (value.length > maxLength) {
            value = value.substring(0, maxLength);
          }
          
          callback(value);
        }
      }
    });
  }
  
  // 显示选择对话框
  showActionSheet(title, items, callback) {
    wx.showActionSheet({
      itemList: items,
      success: (res) => {
        if (callback) {
          callback(res.tapIndex);
        }
      }
    });
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = InputManager;
} else {
  window.InputManager = InputManager;
}
