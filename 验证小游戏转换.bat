@echo off
chcp 65001 >nul
echo ========================================
echo      仗剑江湖行 - 小游戏转换验证工具
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

echo [1/4] 检查编译目录...
if not exist "%BUILD_DIR%" (
    echo ❌ 编译目录不存在！
    echo 请先通过 HBuilderX 编译项目：
    echo   运行 ^> 运行到小程序模拟器 ^> 微信开发者工具
    echo.
    pause
    exit /b 1
)
echo ✅ 编译目录存在

echo.
echo [2/4] 检查必需文件...
set "MISSING_FILES="

if not exist "%BUILD_DIR%\app.js" (
    set "MISSING_FILES=%MISSING_FILES% app.js"
)
if not exist "%BUILD_DIR%\app.json" (
    set "MISSING_FILES=%MISSING_FILES% app.json"
)
if not exist "%BUILD_DIR%\game.js" (
    set "MISSING_FILES=%MISSING_FILES% game.js"
)
if not exist "%BUILD_DIR%\game.json" (
    set "MISSING_FILES=%MISSING_FILES% game.json"
)
if not exist "%BUILD_DIR%\mini-game-patch.js" (
    set "MISSING_FILES=%MISSING_FILES% mini-game-patch.js"
)

if not "%MISSING_FILES%"=="" (
    echo ❌ 缺少以下文件：%MISSING_FILES%
    echo 请重新编译项目或运行转换脚本
    pause
    exit /b 1
)
echo ✅ 所有必需文件都存在

echo.
echo [3/4] 检查兼容性补丁...
findstr /C:"mini-game-patch" "%BUILD_DIR%\app.js" >nul
if %errorlevel% equ 0 (
    echo ✅ app.js 包含兼容性补丁引用
) else (
    echo ⚠️  app.js 未包含兼容性补丁引用
)

findstr /C:"小游戏兼容性补丁" "%BUILD_DIR%\mini-game-patch.js" >nul
if %errorlevel% equ 0 (
    echo ✅ 兼容性补丁文件正常
) else (
    echo ⚠️  兼容性补丁文件可能有问题
)

echo.
echo [4/4] 检查小游戏配置...
findstr /C:"deviceOrientation" "%BUILD_DIR%\game.json" >nul
if %errorlevel% equ 0 (
    echo ✅ game.json 配置正常
) else (
    echo ❌ game.json 配置有问题
)

echo.
echo ========================================
echo           验证完成！
echo ========================================
echo.
echo 📁 小游戏目录：%cd%\%BUILD_DIR%
echo.
echo 🎮 在微信开发者工具中导入小游戏：
echo 1. 打开微信开发者工具
echo 2. 选择"小游戏"项目类型
echo 3. 项目目录：%cd%\%BUILD_DIR%
echo 4. 填入您的小游戏 AppID
echo 5. 点击"导入"
echo.
echo 📋 检查清单：
echo ✅ 编译目录存在
echo ✅ 必需文件完整
echo ✅ 兼容性补丁已应用
echo ✅ 小游戏配置正确
echo.
echo 🚀 您的项目已准备好作为小游戏运行！
echo.
pause
