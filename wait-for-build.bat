@echo off
echo ========================================
echo     Waiting for HBuilderX Compilation
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

echo Please compile the project in HBuilderX:
echo 1. Open HBuilderX
echo 2. Open project: %cd%
echo 3. Run -^> Run to Mini Program Simulator -^> WeChat DevTools
echo.
echo This script will check every 10 seconds if compilation is complete...
echo Press Ctrl+C to stop checking
echo.

:check_loop
if exist "%BUILD_DIR%" (
    echo.
    echo ========================================
    echo Compilation detected!
    echo ========================================
    echo.
    
    if exist "%BUILD_DIR%\app.js" (
        echo OK: app.js found
    ) else (
        echo WARNING: app.js not found
    )
    
    if exist "%BUILD_DIR%\app.json" (
        echo OK: app.json found
    ) else (
        echo WARNING: app.json not found
    )
    
    if exist "%BUILD_DIR%\pages\index\index.js" (
        echo OK: index page found
    ) else (
        echo WARNING: index page not found
    )
    
    echo.
    echo Compilation appears to be complete!
    echo Now running post-build configuration...
    echo.
    
    REM Run the build script automatically
    call check-build.bat
    
    echo.
    echo ========================================
    echo Ready for WeChat DevTools!
    echo ========================================
    echo.
    echo Next steps:
    echo 1. Open WeChat DevTools
    echo 2. Select "Mini Game" project type
    echo 3. Import directory: %cd%\%BUILD_DIR%
    echo 4. Enter your Mini Game AppID
    echo.
    pause
    exit /b 0
) else (
    echo Waiting for compilation... ^(checking every 10 seconds^)
    timeout /t 10 /nobreak >nul
    goto check_loop
)
