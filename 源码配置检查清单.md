# 小游戏源码配置检查清单

## ✅ 已完成的源码级别修改

### 1. manifest.json - 小游戏基础配置
- ✅ `"gameSubpackage": true` - 启用小游戏分包
- ✅ `"gamePlugin": true` - 启用小游戏插件
- ✅ `"plugins": {}` - 插件配置

### 2. pages.json - 页面路由配置
- ✅ 首页改为 `pages/index/index`（更适合小游戏）
- ✅ 登录页面 `pages/login/login` 移到第二位
- ✅ 保持所有原有页面配置

### 3. main.js - 应用入口
- ✅ 导入小游戏兼容性补丁：`import './mini-game-patch.js'`
- ✅ 补丁在所有其他模块之前加载

### 4. mini-game-patch.js - 兼容性补丁
- ✅ `wx.canIUse` 方法实现
- ✅ `global` 对象确保存在
- ✅ `Page`, `Component`, `App`, `getApp` 全局函数
- ✅ 基础 API 兼容性（`getAppBaseInfo`, `getWindowInfo`, `getDeviceInfo`）

### 5. App.vue - 应用生命周期
- ✅ 添加小游戏环境检测
- ✅ 优化 WebSocket 连接管理
- ✅ 添加小游戏特有的启动逻辑

### 6. pages/index/index.vue - 首页
- ✅ 修改登录检查逻辑，使用 `navigateTo` 而不是 `reLaunch`
- ✅ 添加小游戏环境的错误处理
- ✅ 保持所有原有游戏功能

### 7. game.json - 小游戏配置文件
- ✅ 设备方向：竖屏
- ✅ 隐藏状态栏
- ✅ 网络超时配置
- ✅ 不可调整大小

### 8. game.js - 小游戏入口文件
- ✅ 导入 uni-app 适配器
- ✅ 小游戏环境检测
- ✅ 引入主应用
- ✅ 启动日志

## 🔧 构建流程

### 源码到小游戏的完整流程：

1. **清理缓存**
   ```bash
   删除 unpackage 目录
   ```

2. **源码检查**
   ```bash
   运行 "重新构建小游戏.bat"
   ```

3. **HBuilderX 编译**
   ```
   运行 -> 运行到小程序模拟器 -> 微信开发者工具
   ```

4. **自动后处理**
   ```
   脚本自动创建 game.json 和 game.js
   ```

5. **微信开发者工具导入**
   ```
   选择"小游戏" -> 导入编译目录
   ```

## 🎯 关键优势

### 源码级别的优势：
1. **持久性** - 每次编译都会自动包含修改
2. **可维护性** - 所有修改都在源码中，便于管理
3. **完整性** - 涵盖了从配置到代码的所有层面
4. **兼容性** - 同时支持小程序和小游戏环境

### 解决的核心问题：
1. **wx.canIUse is not a function** - 源码级别的 API 补丁
2. **global is not defined** - 全局对象兼容性
3. **页面路由问题** - 优化的导航逻辑
4. **黑屏问题** - 正确的首页配置和错误处理

## 📋 验证清单

### 编译后验证：
- [ ] `unpackage/dist/dev/mp-weixin/game.json` 存在
- [ ] `unpackage/dist/dev/mp-weixin/game.js` 存在
- [ ] `unpackage/dist/dev/mp-weixin/app.js` 包含补丁代码
- [ ] 微信开发者工具无语法错误
- [ ] 控制台显示补丁应用成功日志

### 运行时验证：
- [ ] 首页正常显示（不再黑屏）
- [ ] 登录功能正常
- [ ] WebSocket 连接正常
- [ ] 游戏功能完整

## 🚀 下一步

1. **运行构建脚本**：`重新构建小游戏.bat`
2. **在 HBuilderX 中编译项目**
3. **在微信开发者工具中导入为小游戏**
4. **测试所有功能**

现在所有修改都在源码级别，确保了项目的可维护性和一致性！
