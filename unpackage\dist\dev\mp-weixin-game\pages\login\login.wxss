
.login-container.data-v-e4e4508d {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.game-title.data-v-e4e4508d {
	text-align: center;
	margin-bottom: 80rpx;
}
.title-text.data-v-e4e4508d {
	font-size: 60rpx;
	font-weight: bold;
	color: white;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
	display: block;
	margin-bottom: 20rpx;
}
.subtitle-text.data-v-e4e4508d {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}
.login-form.data-v-e4e4508d,
.register-form.data-v-e4e4508d {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	width: 100%;
	max-width: 600rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
	position: relative;
	z-index: 10;
}
.form-title.data-v-e4e4508d {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 60rpx;
}
.input-group.data-v-e4e4508d {
	margin-bottom: 40rpx;
	position: relative;
	z-index: 20;
}
.input-label.data-v-e4e4508d {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

/* 确保输入框可点击 */
.input-wrapper.data-v-e4e4508d {
	position: relative;
	z-index: 999;
	margin-bottom: 10rpx;
}
.input-field.data-v-e4e4508d {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	background: white;
	box-sizing: border-box;
	color: #333;
	position: relative;
	z-index: 1000;
}
.input-field.data-v-e4e4508d:focus {
	border-color: #667eea;
	z-index: 1001;
}
.gender-options.data-v-e4e4508d {
	display: flex;
	gap: 20rpx;
	position: relative;
	z-index: 20;
}
.gender-option.data-v-e4e4508d {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	transition: all 0.3s ease;
	position: relative;
	z-index: 20;
}
.gender-option.active.data-v-e4e4508d {
	border-color: #667eea;
	background: #667eea;
}
.gender-text.data-v-e4e4508d {
	font-size: 28rpx;
	color: #333;
}
.gender-option.active .gender-text.data-v-e4e4508d {
	color: white;
}
.character-preview.data-v-e4e4508d {
	margin: 40rpx 0;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}
.preview-label.data-v-e4e4508d {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}
.character-card.data-v-e4e4508d {
	text-align: center;
}
.character-name.data-v-e4e4508d {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.character-level.data-v-e4e4508d {
	font-size: 24rpx;
	color: #666;
	display: block;
}
.primary-btn.data-v-e4e4508d {
	width: 100%;
	height: 90rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 45rpx;
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	position: relative;
	z-index: 20;
}
.primary-btn.data-v-e4e4508d:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}
.primary-btn.data-v-e4e4508d:disabled {
	background: #ccc;
	box-shadow: none;
}
.form-footer.data-v-e4e4508d {
	text-align: center;
}
.footer-text.data-v-e4e4508d {
	font-size: 28rpx;
	color: #666;
}
.footer-link.data-v-e4e4508d {
	font-size: 28rpx;
	color: #667eea;
	margin-left: 10rpx;
}
.loading-overlay.data-v-e4e4508d {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}
.loading-content.data-v-e4e4508d {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}
.loading-text.data-v-e4e4508d {
	font-size: 32rpx;
	color: #333;
}
.talent-desc.data-v-e4e4508d {
	text-align: center;
	margin-top: 30rpx;
	color: #888;
	font-size: 24rpx;
	line-height: 1.7;
}
