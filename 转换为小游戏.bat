@echo off
chcp 65001 >nul
echo ========================================
echo        仗剑江湖行 - 小游戏转换工具
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

echo [1/4] 检查编译目录...
if not exist "%SOURCE_DIR%" (
    echo 错误：编译目录不存在！
    echo 请先通过 HBuilderX 编译项目：
    echo   运行 ^> 运行到小程序模拟器 ^> 微信开发者工具
    echo.
    pause
    exit /b 1
)
echo ✓ 编译目录存在

echo.
echo [2/4] 复制小游戏配置文件...
if exist "game.json" (
    copy "game.json" "%SOURCE_DIR%\game.json" >nul
    echo ✓ game.json 已复制
) else (
    echo 警告：源码中的 game.json 不存在，创建默认配置...
    echo {> "%SOURCE_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
    echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
    echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
    echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
    echo   },>> "%SOURCE_DIR%\game.json"
    echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
    echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
    echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
    echo   "resizable": false>> "%SOURCE_DIR%\game.json"
    echo }>> "%SOURCE_DIR%\game.json"
    echo ✓ 默认 game.json 已创建
)

echo.
echo [3/4] 复制小游戏入口文件...
if exist "game.js" (
    copy "game.js" "%SOURCE_DIR%\game.js" >nul
    echo ✓ game.js 已复制
) else if exist "%SOURCE_DIR%\app.js" (
    copy "%SOURCE_DIR%\app.js" "%SOURCE_DIR%\game.js" >nul
    echo ✓ game.js 已从 app.js 创建
) else (
    echo 错误：无法创建 game.js 文件
    pause
    exit /b 1
)

echo.
echo [4/4] 复制兼容性补丁...
if exist "mini-game-patch.js" (
    copy "mini-game-patch.js" "%SOURCE_DIR%\mini-game-patch.js" >nul
    echo ✓ mini-game-patch.js 已复制
) else (
    echo 警告：mini-game-patch.js 不存在
)

echo.
echo ========================================
echo           转换完成！
echo ========================================
echo.
echo 现在可以在微信开发者工具中导入小游戏：
echo.
echo 1. 打开微信开发者工具
echo 2. 选择"小游戏"项目类型
echo 3. 项目目录：%cd%\%SOURCE_DIR%
echo 4. 填入您的小游戏 AppID
echo 5. 点击"导入"
echo.
echo 注意事项：
echo - 确保已在微信公众平台注册小游戏
echo - 在 manifest.json 中填入正确的 AppID
echo - 所有兼容性补丁已自动应用到源码中
echo.
echo 如果遇到问题，请查看"小游戏启动说明.md"文档
echo.
pause
