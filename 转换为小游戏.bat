@echo off
chcp 65001 >nul
echo ========================================
echo        仗剑江湖行 - 小游戏转换工具
echo ========================================
echo.

echo 正在调用 PowerShell 脚本应用兼容性补丁...
echo.

powershell -ExecutionPolicy Bypass -File "应用小游戏补丁.ps1"

if %errorlevel% neq 0 (
    echo.
    echo PowerShell 脚本执行失败，尝试备用方案...
    echo.

    set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

    if not exist "%SOURCE_DIR%" (
        echo 错误：编译目录不存在！
        echo 请先通过 HBuilderX 编译项目
        pause
        exit /b 1
    )

    echo 手动创建 game.json...
    echo {> "%SOURCE_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
    echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
    echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
    echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
    echo   },>> "%SOURCE_DIR%\game.json"
    echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
    echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
    echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
    echo   "resizable": false>> "%SOURCE_DIR%\game.json"
    echo }>> "%SOURCE_DIR%\game.json"

    if exist "%SOURCE_DIR%\app.js" (
        copy "%SOURCE_DIR%\app.js" "%SOURCE_DIR%\game.js" >nul
        echo game.js 已创建
    )

    echo.
    echo 注意：需要手动应用兼容性补丁！
    echo 请参考"最终解决方案说明.md"文档
)
