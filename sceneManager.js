/**
 * 场景管理器 - 管理所有游戏场景
 */

class SceneManager {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.currentScene = null;
    this.scenes = {};
    
    this.initScenes();
  }
  
  // 初始化所有场景
  initScenes() {
    try {
      this.scenes = {
        login: new LoginScene(this.gameEngine),
        index: new IndexScene(this.gameEngine),
        character: new CharacterScene(this.gameEngine),
        skills: new SkillsScene(this.gameEngine),
        shop: new ShopScene(this.gameEngine),
        guild: new GuildScene(this.gameEngine),
        backpack: new BackpackScene(this.gameEngine)
      };

      console.log('所有场景初始化完成');

      // 设置初始场景
      this.switchTo('login');
    } catch (error) {
      console.error('场景初始化失败:', error);
      throw error;
    }
  }
  
  // 切换场景
  switchTo(sceneName, data = null) {
    if (this.scenes[sceneName]) {
      console.log('切换到场景:', sceneName);
      
      // 退出当前场景
      if (this.currentScene) {
        this.currentScene.onExit();
      }
      
      // 进入新场景
      this.currentScene = this.scenes[sceneName];
      this.currentScene.onEnter(data);
    } else {
      console.error('场景不存在:', sceneName);
    }
  }
  
  // 更新当前场景
  update(deltaTime) {
    if (this.currentScene) {
      this.currentScene.update(deltaTime);
    }
  }
  
  // 渲染当前场景
  render() {
    if (this.currentScene) {
      this.currentScene.render();
    }
  }
  
  // 处理点击事件
  handleTap(x, y) {
    if (this.currentScene) {
      this.currentScene.handleTap(x, y);
    }
  }
}

/**
 * 基础场景类
 */
class BaseScene {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.ui = gameEngine.uiManager;
    this.input = gameEngine.inputManager;
    this.network = gameEngine.networkManager;
    this.isActive = false;
    this.elements = []; // UI元素列表
  }
  
  // 进入场景
  onEnter(data = null) {
    this.isActive = true;
    this.elements = [];
    console.log('进入场景:', this.constructor.name);
  }
  
  // 退出场景
  onExit() {
    this.isActive = false;
    this.elements = [];
    console.log('退出场景:', this.constructor.name);
  }
  
  // 更新场景
  update(deltaTime) {
    // 子类可以重写此方法
  }
  
  // 渲染场景
  render() {
    // 子类必须实现此方法
  }
  
  // 处理点击事件
  handleTap(x, y) {
    // 检查所有可点击元素
    for (const element of this.elements) {
      if (element.clickable && this.ui.isPointInRect(x, y, element)) {
        if (element.onClick) {
          element.onClick();
        }
        return;
      }
    }
  }
  
  // 添加可点击元素
  addClickableElement(rect, onClick) {
    this.elements.push({
      ...rect,
      clickable: true,
      onClick: onClick
    });
  }
  
  // 清空元素
  clearElements() {
    this.elements = [];
  }
}

/**
 * 登录场景
 */
class LoginScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.isRegistering = false;
    this.formData = {
      username: '',
      password: '',
      registerData: {
        username: '',
        password: '',
        confirmPassword: '',
        characterName: '',
        gender: 'male'
      }
    };
  }
  
  render() {
    this.clearElements();
    
    // 绘制渐变背景
    this.ui.drawGradientBackground();
    
    // 绘制标题
    this.ui.drawText('仗剑江湖行', this.ui.canvas.width / 2, 80, {
      font: this.ui.config.fonts.title,
      color: this.ui.config.colors.white,
      align: 'center'
    });
    
    this.ui.drawText('微信小游戏版', this.ui.canvas.width / 2, 130, {
      font: this.ui.config.fonts.normal,
      color: this.ui.config.colors.white,
      align: 'center'
    });
    
    if (this.isRegistering) {
      this.renderRegisterForm();
    } else {
      this.renderLoginForm();
    }
  }
  
  renderLoginForm() {
    const formX = 30;
    const formY = 180;
    const formWidth = this.ui.canvas.width - 60;
    
    // 绘制表单背景
    this.ui.drawCard(formX, formY, formWidth, 320);
    
    // 表单标题
    this.ui.drawText('登录江湖', formX + formWidth / 2, formY + 30, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text,
      align: 'center'
    });
    
    // 账号输入框
    this.ui.drawText('账号:', formX + 20, formY + 80);
    const usernameInput = this.ui.drawInputBox(
      formX + 20, formY + 105, formWidth - 40, 40,
      this.formData.username, '请输入账号'
    );
    this.addClickableElement(usernameInput, () => this.inputUsername());
    
    // 密码输入框
    this.ui.drawText('密码:', formX + 20, formY + 160);
    const passwordInput = this.ui.drawInputBox(
      formX + 20, formY + 185, formWidth - 40, 40,
      this.formData.password ? '••••••••' : '', '请输入密码'
    );
    this.addClickableElement(passwordInput, () => this.inputPassword());
    
    // 登录按钮
    const loginButton = this.ui.drawButton(
      '登录', formX + 20, formY + 245, formWidth - 40, 45,
      { backgroundColor: this.ui.config.colors.primary, font: 'bold 20px Arial' }
    );
    this.addClickableElement(loginButton, () => this.handleLogin());
    
    // 注册链接
    this.ui.drawText('还没有账号？点击注册', formX + formWidth / 2, formY + 310, {
      font: this.ui.config.fonts.small,
      color: this.ui.config.colors.secondary,
      align: 'center'
    });
    
    const registerLink = {
      x: formX + 20,
      y: formY + 300,
      width: formWidth - 40,
      height: 20
    };
    this.addClickableElement(registerLink, () => this.switchToRegister());
  }
  
  renderRegisterForm() {
    const formX = 30;
    const formY = 150;
    const formWidth = this.ui.canvas.width - 60;
    
    // 绘制表单背景
    this.ui.drawCard(formX, formY, formWidth, 450);
    
    // 表单标题
    this.ui.drawText('创建角色', formX + formWidth / 2, formY + 30, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text,
      align: 'center'
    });
    
    let currentY = formY + 70;
    const fieldHeight = 65;
    
    // 账号输入框
    this.ui.drawText('账号:', formX + 20, currentY);
    const regUsernameInput = this.ui.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.registerData.username, '请输入账号'
    );
    this.addClickableElement(regUsernameInput, () => this.inputRegisterUsername());
    currentY += fieldHeight;
    
    // 密码输入框
    this.ui.drawText('密码:', formX + 20, currentY);
    const regPasswordInput = this.ui.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.registerData.password ? '••••••••' : '', '请输入密码'
    );
    this.addClickableElement(regPasswordInput, () => this.inputRegisterPassword());
    currentY += fieldHeight;
    
    // 确认密码输入框
    this.ui.drawText('确认密码:', formX + 20, currentY);
    const regConfirmPasswordInput = this.ui.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.registerData.confirmPassword ? '••••••••' : '', '请再次输入密码'
    );
    this.addClickableElement(regConfirmPasswordInput, () => this.inputRegisterConfirmPassword());
    currentY += fieldHeight;
    
    // 角色名输入框
    this.ui.drawText('角色名:', formX + 20, currentY);
    const regCharacterNameInput = this.ui.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.registerData.characterName, '请输入角色名'
    );
    this.addClickableElement(regCharacterNameInput, () => this.inputRegisterCharacterName());
    currentY += fieldHeight;
    
    // 性别选择
    this.ui.drawText('性别:', formX + 20, currentY);
    const maleButton = this.ui.drawButton(
      '男', formX + 20, currentY + 20, 80, 35,
      {
        backgroundColor: this.formData.registerData.gender === 'male' ? 
          this.ui.config.colors.primary : this.ui.config.colors.background,
        textColor: this.formData.registerData.gender === 'male' ? 
          this.ui.config.colors.white : this.ui.config.colors.text
      }
    );
    this.addClickableElement(maleButton, () => this.selectGender('male'));
    
    const femaleButton = this.ui.drawButton(
      '女', formX + 120, currentY + 20, 80, 35,
      {
        backgroundColor: this.formData.registerData.gender === 'female' ? 
          this.ui.config.colors.primary : this.ui.config.colors.background,
        textColor: this.formData.registerData.gender === 'female' ? 
          this.ui.config.colors.white : this.ui.config.colors.text
      }
    );
    this.addClickableElement(femaleButton, () => this.selectGender('female'));
    currentY += 70;
    
    // 注册按钮
    const registerButton = this.ui.drawButton(
      '创建角色', formX + 20, currentY, formWidth - 40, 45,
      { backgroundColor: this.ui.config.colors.success, font: 'bold 20px Arial' }
    );
    this.addClickableElement(registerButton, () => this.handleRegister());
    
    // 返回登录链接
    this.ui.drawText('已有账号？返回登录', formX + formWidth / 2, currentY + 65, {
      font: this.ui.config.fonts.small,
      color: this.ui.config.colors.secondary,
      align: 'center'
    });
    
    const backToLoginLink = {
      x: formX + 20,
      y: currentY + 55,
      width: formWidth - 40,
      height: 20
    };
    this.addClickableElement(backToLoginLink, () => this.switchToLogin());
  }
  
  // 输入方法
  inputUsername() {
    this.input.showInputDialog('输入账号', '请输入账号', (value) => {
      this.formData.username = value;
    });
  }
  
  inputPassword() {
    this.input.showInputDialog('输入密码', '请输入密码', (value) => {
      this.formData.password = value;
    });
  }
  
  inputRegisterUsername() {
    this.input.showInputDialog('输入账号', '请输入账号', (value) => {
      this.formData.registerData.username = value;
    });
  }
  
  inputRegisterPassword() {
    this.input.showInputDialog('输入密码', '请输入密码', (value) => {
      this.formData.registerData.password = value;
    });
  }
  
  inputRegisterConfirmPassword() {
    this.input.showInputDialog('确认密码', '请再次输入密码', (value) => {
      this.formData.registerData.confirmPassword = value;
    });
  }
  
  inputRegisterCharacterName() {
    this.input.showInputDialog('输入角色名', '请输入角色名', (value) => {
      this.formData.registerData.characterName = value;
    });
  }
  
  selectGender(gender) {
    this.formData.registerData.gender = gender;
  }
  
  switchToRegister() {
    this.isRegistering = true;
  }
  
  switchToLogin() {
    this.isRegistering = false;
  }
  
  // 处理登录
  handleLogin() {
    const { username, password } = this.formData;
    
    if (!username || !password) {
      this.gameEngine.showToast('请输入账号和密码');
      return;
    }
    
    console.log('执行登录:', username);
    this.network.send({
      type: 'login',
      data: { username, password }
    });
    
    this.gameEngine.showToast('正在登录...');
  }
  
  // 处理注册
  handleRegister() {
    const { username, password, confirmPassword, characterName, gender } = this.formData.registerData;
    
    if (!username || !password || !confirmPassword || !characterName) {
      this.gameEngine.showToast('请填写完整信息');
      return;
    }
    
    if (password !== confirmPassword) {
      this.gameEngine.showToast('两次密码输入不一致');
      return;
    }
    
    console.log('执行注册:', { username, characterName, gender });
    this.network.send({
      type: 'register',
      data: { username, password, characterName, gender }
    });
    
    this.gameEngine.showToast('正在注册...');
  }
}

/**
 * 主页面场景
 */
class IndexScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();

    // 绘制头部
    this.ui.drawHeader('仗剑江湖行');

    const contentY = 80;

    // 绘制玩家信息
    this.renderPlayerInfo(contentY);

    // 绘制闯江湖按钮
    const adventureButton = this.ui.drawButton(
      '闯江湖', 30, contentY + 200, this.ui.canvas.width - 60, 50,
      { backgroundColor: this.ui.config.colors.success, font: 'bold 20px Arial' }
    );
    this.addClickableElement(adventureButton, () => this.handleAdventure());

    // 绘制最近事件
    this.renderRecentEvents(contentY + 280);

    // 绘制底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'index');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }

  renderPlayerInfo(y) {
    const infoY = y + 20;

    this.ui.drawText('角色信息', 30, infoY, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text
    });

    const playerData = this.gameEngine.gameState.playerData;
    if (playerData) {
      this.ui.drawText(`角色名: ${playerData.characterName || '无名侠客'}`, 30, infoY + 35);
      this.ui.drawText(`等级: ${playerData.level || 1}`, 30, infoY + 65);
      this.ui.drawText(`银两: ${playerData.money || 0}`, 30, infoY + 95);
      this.ui.drawText(`经验: ${playerData.exp || 0}`, 30, infoY + 125);
    }
  }

  renderRecentEvents(y) {
    this.ui.drawText('最近事件', 30, y, {
      font: this.ui.config.fonts.normal,
      color: this.ui.config.colors.text
    });

    const events = [
      '你在江湖中闯荡，获得了一些经验',
      '遇到了一位神秘商人',
      '在客栈中听到了江湖传说'
    ];

    events.forEach((event, index) => {
      this.ui.drawText(`• ${event}`, 30, y + 30 + index * 25, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.textSecondary
      });
    });
  }

  handleAdventure() {
    this.gameEngine.showToast('开始冒险...');
    this.network.send({
      type: 'adventure',
      action: 'start'
    });
  }
}

/**
 * 角色场景
 */
class CharacterScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();
    this.ui.drawHeader('角色信息');

    const contentY = 100;
    const playerData = this.gameEngine.gameState.playerData;

    if (playerData) {
      // 基本信息
      this.ui.drawText('基本信息', 30, contentY, {
        font: this.ui.config.fonts.large,
        color: this.ui.config.colors.text
      });

      const fields = [
        { label: '角色名', value: playerData.characterName || '无名侠客' },
        { label: '等级', value: playerData.level || 1 },
        { label: '经验', value: `${playerData.exp || 0}/${playerData.maxExp || 100}` },
        { label: '银两', value: playerData.money || 0 },
        { label: '生命值', value: `${playerData.hp || 100}/${playerData.maxHp || 100}` },
        { label: '内力', value: `${playerData.mp || 50}/${playerData.maxMp || 50}` }
      ];

      fields.forEach((field, index) => {
        const y = contentY + 40 + index * 35;
        this.ui.drawText(`${field.label}:`, 30, y);
        this.ui.drawText(field.value.toString(), 150, y, {
          color: this.ui.config.colors.textSecondary
        });
      });

      // 属性信息
      this.ui.drawText('属性', 30, contentY + 280, {
        font: this.ui.config.fonts.large,
        color: this.ui.config.colors.text
      });

      const attributes = [
        { label: '攻击力', value: playerData.attack || 10 },
        { label: '防御力', value: playerData.defense || 5 },
        { label: '敏捷', value: playerData.agility || 8 },
        { label: '幸运', value: playerData.luck || 5 }
      ];

      attributes.forEach((attr, index) => {
        const y = contentY + 320 + index * 35;
        this.ui.drawText(`${attr.label}:`, 30, y);
        this.ui.drawText(attr.value.toString(), 150, y, {
          color: this.ui.config.colors.textSecondary
        });
      });
    }

    // 底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'character');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }
}

/**
 * 武功场景
 */
class SkillsScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();
    this.ui.drawHeader('武功秘籍');

    const contentY = 100;

    this.ui.drawText('已学武功', 30, contentY, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text
    });

    const playerData = this.gameEngine.gameState.playerData;
    const skills = playerData?.skills || [
      { name: '基础剑法', level: 1, description: '最基础的剑法' },
      { name: '轻功', level: 1, description: '提升移动速度' }
    ];

    skills.forEach((skill, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制技能卡片
      this.ui.drawCard(30, y, this.ui.canvas.width - 60, 70);

      this.ui.drawText(skill.name, 50, y + 20, {
        font: this.ui.config.fonts.normal,
        color: this.ui.config.colors.text
      });

      this.ui.drawText(`等级: ${skill.level}`, 50, y + 45, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.textSecondary
      });

      this.ui.drawText(skill.description, 200, y + 20, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.textSecondary
      });

      // 升级按钮
      const upgradeButton = this.ui.drawButton(
        '升级', this.ui.canvas.width - 120, y + 15, 80, 40,
        { backgroundColor: this.ui.config.colors.secondary, font: this.ui.config.fonts.small }
      );

      this.addClickableElement(upgradeButton, () => this.handleSkillUpgrade(index));
    });

    // 底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'skills');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }

  handleSkillUpgrade(skillIndex) {
    this.gameEngine.showToast('升级武功...');
    this.network.send({
      type: 'skill',
      action: 'upgrade',
      skillIndex: skillIndex
    });
  }
}

/**
 * 商店场景
 */
class ShopScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();
    this.ui.drawHeader('江湖市场');

    const contentY = 100;

    this.ui.drawText('商品列表', 30, contentY, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text
    });

    const items = [
      { name: '铁剑', price: 100, description: '普通的铁制长剑' },
      { name: '布衣', price: 50, description: '简单的布制衣服' },
      { name: '回血丹', price: 20, description: '恢复生命值的丹药' },
      { name: '内力丹', price: 30, description: '恢复内力的丹药' }
    ];

    items.forEach((item, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制商品卡片
      this.ui.drawCard(30, y, this.ui.canvas.width - 60, 70);

      this.ui.drawText(item.name, 50, y + 20, {
        font: this.ui.config.fonts.normal,
        color: this.ui.config.colors.text
      });

      this.ui.drawText(`价格: ${item.price}银两`, 50, y + 45, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.warning
      });

      this.ui.drawText(item.description, 200, y + 20, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.textSecondary
      });

      // 购买按钮
      const buyButton = this.ui.drawButton(
        '购买', this.ui.canvas.width - 120, y + 15, 80, 40,
        { backgroundColor: this.ui.config.colors.success, font: this.ui.config.fonts.small }
      );

      this.addClickableElement(buyButton, () => this.handleBuyItem(index));
    });

    // 底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'shop');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }

  handleBuyItem(itemIndex) {
    this.gameEngine.showToast('购买物品...');
    this.network.send({
      type: 'shop',
      action: 'buy',
      itemIndex: itemIndex
    });
  }
}

/**
 * 门派场景
 */
class GuildScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();
    this.ui.drawHeader('门派系统');

    const contentY = 100;
    const playerData = this.gameEngine.gameState.playerData;

    if (playerData?.guild) {
      // 已加入门派
      this.ui.drawText(`当前门派: ${playerData.guild.name}`, 30, contentY, {
        font: this.ui.config.fonts.large,
        color: this.ui.config.colors.text
      });

      this.ui.drawText(`门派等级: ${playerData.guild.level}`, 30, contentY + 40);
      this.ui.drawText(`贡献度: ${playerData.guild.contribution}`, 30, contentY + 70);

      // 门派功能按钮
      const donateButton = this.ui.drawButton(
        '捐献', 30, contentY + 120, 100, 40,
        { backgroundColor: this.ui.config.colors.warning }
      );
      this.addClickableElement(donateButton, () => this.handleGuildDonate());

      const leaveButton = this.ui.drawButton(
        '退出门派', 150, contentY + 120, 100, 40,
        { backgroundColor: this.ui.config.colors.danger }
      );
      this.addClickableElement(leaveButton, () => this.handleGuildLeave());
    } else {
      // 未加入门派
      this.ui.drawText('可加入的门派', 30, contentY, {
        font: this.ui.config.fonts.large,
        color: this.ui.config.colors.text
      });

      const guilds = [
        { name: '华山派', description: '以剑法闻名的门派' },
        { name: '武当派', description: '内功深厚的道家门派' },
        { name: '少林寺', description: '佛门圣地，武学渊源' }
      ];

      guilds.forEach((guild, index) => {
        const y = contentY + 50 + index * 80;

        // 绘制门派卡片
        this.ui.drawCard(30, y, this.ui.canvas.width - 60, 70);

        this.ui.drawText(guild.name, 50, y + 20, {
          font: this.ui.config.fonts.normal,
          color: this.ui.config.colors.text
        });

        this.ui.drawText(guild.description, 50, y + 45, {
          font: this.ui.config.fonts.small,
          color: this.ui.config.colors.textSecondary
        });

        // 加入按钮
        const joinButton = this.ui.drawButton(
          '加入', this.ui.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.config.colors.primary, font: this.ui.config.fonts.small }
        );

        this.addClickableElement(joinButton, () => this.handleGuildJoin(index));
      });
    }

    // 底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'guild');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }

  handleGuildDonate() {
    this.gameEngine.showToast('捐献门派...');
    this.network.send({
      type: 'guild',
      action: 'donate',
      amount: 100
    });
  }

  handleGuildLeave() {
    this.gameEngine.showConfirm('确认', '确定要退出门派吗？', (confirmed) => {
      if (confirmed) {
        this.network.send({
          type: 'guild',
          action: 'leave'
        });
      }
    });
  }

  handleGuildJoin(guildIndex) {
    this.gameEngine.showToast('申请加入门派...');
    this.network.send({
      type: 'guild',
      action: 'join',
      guildIndex: guildIndex
    });
  }
}

/**
 * 背包场景
 */
class BackpackScene extends BaseScene {
  constructor(gameEngine) {
    super(gameEngine);
    this.tabs = [
      { name: '角色', key: 'character' },
      { name: '武功', key: 'skills' },
      { name: '江湖', key: 'index' },
      { name: '市场', key: 'shop' },
      { name: '门派', key: 'guild' }
    ];
  }

  render() {
    this.clearElements();

    this.ui.clear();
    this.ui.drawHeader('背包');

    const contentY = 100;

    this.ui.drawText('物品列表', 30, contentY, {
      font: this.ui.config.fonts.large,
      color: this.ui.config.colors.text
    });

    const playerData = this.gameEngine.gameState.playerData;
    const items = playerData?.items || [
      { name: '新手剑', type: 'weapon', description: '初学者使用的剑' },
      { name: '布衣', type: 'armor', description: '简单的防护服' },
      { name: '回血丹', type: 'consumable', count: 5, description: '恢复生命值' }
    ];

    items.forEach((item, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制物品卡片
      this.ui.drawCard(30, y, this.ui.canvas.width - 60, 70);

      const itemName = item.count ? `${item.name} x${item.count}` : item.name;
      this.ui.drawText(itemName, 50, y + 20, {
        font: this.ui.config.fonts.normal,
        color: this.ui.config.colors.text
      });

      this.ui.drawText(item.description, 50, y + 45, {
        font: this.ui.config.fonts.small,
        color: this.ui.config.colors.textSecondary
      });

      // 操作按钮
      if (item.type === 'consumable') {
        const useButton = this.ui.drawButton(
          '使用', this.ui.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.config.colors.success, font: this.ui.config.fonts.small }
        );
        this.addClickableElement(useButton, () => this.handleUseItem(index));
      } else if (item.type === 'weapon' || item.type === 'armor') {
        const equipButton = this.ui.drawButton(
          '装备', this.ui.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.config.colors.primary, font: this.ui.config.fonts.small }
        );
        this.addClickableElement(equipButton, () => this.handleEquipItem(index));
      }
    });

    // 底部导航
    const tabButtons = this.ui.drawTabBar(this.tabs, 'backpack');
    tabButtons.forEach(button => {
      this.addClickableElement(button, () => this.gameEngine.switchScene(button.key));
    });
  }

  handleUseItem(itemIndex) {
    this.gameEngine.showToast('使用物品...');
    this.network.send({
      type: 'backpack',
      action: 'use',
      itemIndex: itemIndex
    });
  }

  handleEquipItem(itemIndex) {
    this.gameEngine.showToast('装备物品...');
    this.network.send({
      type: 'backpack',
      action: 'equip',
      itemIndex: itemIndex
    });
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SceneManager, BaseScene, LoginScene, IndexScene,
    CharacterScene, SkillsScene, ShopScene, GuildScene, BackpackScene
  };
} else {
  window.SceneManager = SceneManager;
  window.BaseScene = BaseScene;
  window.LoginScene = LoginScene;
  window.IndexScene = IndexScene;
  window.CharacterScene = CharacterScene;
  window.SkillsScene = SkillsScene;
  window.ShopScene = ShopScene;
  window.GuildScene = GuildScene;
  window.BackpackScene = BackpackScene;
}
