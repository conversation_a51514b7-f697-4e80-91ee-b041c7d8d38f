@echo off
echo ========================================
echo     Verify Mini-Game Configuration
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

echo Checking mini-game files...

if exist "%BUILD_DIR%\game.json" (
    echo OK: game.json exists
    findstr "deviceOrientation" "%BUILD_DIR%\game.json" >nul
    if %errorlevel% equ 0 (
        echo OK: game.json contains valid configuration
    ) else (
        echo WARNING: game.json may be invalid
    )
) else (
    echo ERROR: game.json not found
)

if exist "%BUILD_DIR%\game.js" (
    echo OK: game.js exists
    
    findstr "wx.canIUse" "%BUILD_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js contains wx.canIUse patch
    ) else (
        echo WARNING: game.js missing wx.canIUse patch
    )
    
    findstr "vendor.js" "%BUILD_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js loads vendor.js
    ) else (
        echo WARNING: game.js missing vendor.js reference
    )
    
    findstr "console.log" "%BUILD_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js contains debug logging
    ) else (
        echo WARNING: game.js missing debug logging
    )
) else (
    echo ERROR: game.js not found
)

echo.
echo Checking file sizes...
if exist "%BUILD_DIR%\game.js" (
    for %%A in ("%BUILD_DIR%\game.js") do (
        if %%~zA LSS 1000 (
            echo WARNING: game.js is very small ^(%%~zA bytes^) - may be incomplete
        ) else (
            echo OK: game.js size is %%~zA bytes
        )
    )
)

echo.
echo ========================================
echo Verification Complete
echo ========================================
echo.

if exist "%BUILD_DIR%\game.json" if exist "%BUILD_DIR%\game.js" (
    echo Status: READY FOR IMPORT
    echo.
    echo Next steps:
    echo 1. Open WeChat DevTools
    echo 2. Select "Mini Game" project type
    echo 3. Import directory: %cd%\%BUILD_DIR%
    echo 4. Check console for patch messages:
    echo    - "Starting mini-game with critical patches"
    echo    - "wx.canIUse patch applied successfully"
    echo    - "All mini-game patches applied successfully"
    echo.
) else (
    echo Status: NOT READY - Missing required files
    echo Please run create-game-files.bat first
    echo.
)

pause
