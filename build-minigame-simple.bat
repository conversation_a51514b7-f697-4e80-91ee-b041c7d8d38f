@echo off
echo ========================================
echo      Simple Mini-game Build Tool
echo ========================================
echo.

echo Step 1: Please compile in HBuilderX
echo.
echo In HBuilderX menu:
echo Run ^> Run to Mini Program Simulator ^> WeChat Developer Tools
echo.
echo Make sure to select "mp-weixin-game" platform if available
echo or use normal compilation and we will convert it
echo.
echo Press any key when HBuilderX compilation is complete...
pause >nul

echo.
echo Step 2: Converting to mini-game format...

set SOURCE_DIR=unpackage\dist\dev\mp-weixin
set TARGET_DIR=unpackage\dist\dev\mp-weixin-game

if not exist "%SOURCE_DIR%" (
    echo Error: Source directory not found: %SOURCE_DIR%
    echo Please make sure HBuilderX compilation succeeded
    pause
    exit /b 1
)

if exist "%TARGET_DIR%" rmdir /s /q "%TARGET_DIR%"
mkdir "%TARGET_DIR%"

echo Copying compiled files...
xcopy "%SOURCE_DIR%\*" "%TARGET_DIR%\" /E /I /Y >nul

echo Copying mini-game specific files...
copy game.json "%TARGET_DIR%\game.json" >nul 2>&1

echo Checking which game.js to use...
if exist "game-simple.js" (
    echo Using simplified game.js for better compatibility...
    copy game-simple.js "%TARGET_DIR%\game.js" >nul 2>&1
) else (
    echo Using standard game.js...
    copy game.js "%TARGET_DIR%\game.js" >nul 2>&1
)

echo Copying optional engine files...
if exist "gameEngine.js" copy gameEngine.js "%TARGET_DIR%\gameEngine.js" >nul 2>&1
if exist "uiManager.js" copy uiManager.js "%TARGET_DIR%\uiManager.js" >nul 2>&1
if exist "inputManager.js" copy inputManager.js "%TARGET_DIR%\inputManager.js" >nul 2>&1
if exist "networkManager.js" copy networkManager.js "%TARGET_DIR%\networkManager.js" >nul 2>&1
if exist "sceneManager.js" copy sceneManager.js "%TARGET_DIR%\sceneManager.js" >nul 2>&1

echo Checking file copy results...
if not exist "%TARGET_DIR%\game.js" echo WARNING: game.js not copied
if not exist "%TARGET_DIR%\gameEngine.js" echo WARNING: gameEngine.js not copied

echo Copying utils directory...
if exist "utils" (
    if not exist "%TARGET_DIR%\utils" mkdir "%TARGET_DIR%\utils"
    xcopy "utils\*" "%TARGET_DIR%\utils\" /E /I /Y >nul 2>&1
)

echo Creating mini-game app.js...
echo // Mini-game entry point > "%TARGET_DIR%\app.js"
echo // Load game logic >> "%TARGET_DIR%\app.js"
echo require('./game.js'); >> "%TARGET_DIR%\app.js"
echo console.log('Mini-game app.js loaded'); >> "%TARGET_DIR%\app.js"

echo.
echo ========================================
echo         Conversion Complete!
echo ========================================
echo.
echo Mini-game directory: %cd%\%TARGET_DIR%
echo.
echo To test in WeChat Developer Tools:
echo 1. Open WeChat Developer Tools
echo 2. Select "Mini-game" project type
echo 3. Import directory: %cd%\%TARGET_DIR%
echo 4. AppID: wxfb9c395829d83b91
echo.
pause
