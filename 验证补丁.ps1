# Verify Mini-Game Compatibility Patches

$sourceDir = "unpackage\dist\dev\mp-weixin"

Write-Host "========================================" -ForegroundColor Green
Write-Host "        Mini-Game Patch Verification" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check build directory
if (-not (Test-Path $sourceDir)) {
    Write-Host "ERROR: Build directory not found: $sourceDir" -ForegroundColor Red
    exit 1
}

Write-Host "OK: Build directory exists" -ForegroundColor Green

# Check game.json
if (Test-Path "$sourceDir\game.json") {
    Write-Host "OK: game.json file exists" -ForegroundColor Green
} else {
    Write-Host "ERROR: game.json file not found" -ForegroundColor Red
}

# Check game.js
if (Test-Path "$sourceDir\game.js") {
    Write-Host "OK: game.js file exists" -ForegroundColor Green

    # Check patch content
    $gameContent = Get-Content "$sourceDir\game.js" -Raw

    if ($gameContent -match "Mini-game compatibility patch|小游戏兼容性补丁") {
        Write-Host "OK: game.js contains compatibility patch" -ForegroundColor Green
    } else {
        Write-Host "ERROR: game.js does not contain compatibility patch" -ForegroundColor Red
    }

    if ($gameContent -match "wx\.canIUse") {
        Write-Host "OK: game.js contains wx.canIUse patch" -ForegroundColor Green
    } else {
        Write-Host "ERROR: game.js does not contain wx.canIUse patch" -ForegroundColor Red
    }

    if ($gameContent -match "global") {
        Write-Host "OK: game.js contains global object patch" -ForegroundColor Green
    } else {
        Write-Host "ERROR: game.js does not contain global object patch" -ForegroundColor Red
    }

} else {
    Write-Host "ERROR: game.js file not found" -ForegroundColor Red
}

# Check app.js
if (Test-Path "$sourceDir\app.js") {
    Write-Host "OK: app.js file exists" -ForegroundColor Green

    $appContent = Get-Content "$sourceDir\app.js" -Raw

    if ($appContent -match "Mini-game compatibility patch|小游戏兼容性补丁") {
        Write-Host "OK: app.js contains compatibility patch" -ForegroundColor Green
    } else {
        Write-Host "ERROR: app.js does not contain compatibility patch" -ForegroundColor Red
    }

} else {
    Write-Host "ERROR: app.js file not found" -ForegroundColor Red
}

# Check vendor.js for problematic code
if (Test-Path "$sourceDir\common\vendor.js") {
    Write-Host "OK: vendor.js file exists" -ForegroundColor Green

    $vendorContent = Get-Content "$sourceDir\common\vendor.js" -Raw

    if ($vendorContent -match "wx\$2\.canIUse") {
        Write-Host "WARNING: vendor.js contains wx`$2.canIUse calls - patches needed" -ForegroundColor Yellow
    } else {
        Write-Host "OK: vendor.js does not contain problematic code" -ForegroundColor Green
    }

} else {
    Write-Host "ERROR: vendor.js file not found" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Verification Complete" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nIf all checks pass, you can import in WeChat DevTools:" -ForegroundColor White
Write-Host "1. Select 'Mini Game' project type" -ForegroundColor White
Write-Host "2. Import directory: $(Get-Location)\$sourceDir" -ForegroundColor White
Write-Host "3. Check console for 'compatibility patch applied' logs" -ForegroundColor White

Read-Host "`nPress any key to exit"
