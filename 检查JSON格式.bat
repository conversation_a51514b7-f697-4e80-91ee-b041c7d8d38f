@echo off
echo ========================================
echo        JSON Format Check
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo ERROR: Build directory not found!
    pause
    exit /b 1
)

echo Checking JSON files...
echo.

REM Check if app.json exists and has content
if exist "%SOURCE_DIR%\app.json" (
    echo OK: app.json exists
    
    REM Check if file starts with {
    findstr /B "{" "%SOURCE_DIR%\app.json" >nul
    if %errorlevel% equ 0 (
        echo OK: app.json starts with opening brace
    ) else (
        echo ERROR: app.json does not start with opening brace
    )
    
    REM Check if file ends with }
    findstr /E "}" "%SOURCE_DIR%\app.json" >nul
    if %errorlevel% equ 0 (
        echo OK: app.json ends with closing brace
    ) else (
        echo ERROR: app.json does not end with closing brace
    )
    
    REM Check if file contains pages
    findstr "pages" "%SOURCE_DIR%\app.json" >nul
    if %errorlevel% equ 0 (
        echo OK: app.json contains pages configuration
    ) else (
        echo ERROR: app.json missing pages configuration
    )
    
) else (
    echo ERROR: app.json not found
)

REM Check game.json
if exist "%SOURCE_DIR%\game.json" (
    echo OK: game.json exists
    
    findstr /B "{" "%SOURCE_DIR%\game.json" >nul
    if %errorlevel% equ 0 (
        echo OK: game.json starts with opening brace
    ) else (
        echo ERROR: game.json does not start with opening brace
    )
    
) else (
    echo ERROR: game.json not found
)

echo.
echo ========================================
echo JSON Check Complete
echo ========================================
echo.
echo If all checks pass, the JSON files should work in WeChat DevTools
echo You can now refresh the project in WeChat DevTools
echo.
pause
