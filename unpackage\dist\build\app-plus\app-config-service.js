
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#f5f5f5","navigationBar":{"backgroundColor":"#2c3e50","titleText":"仗剑江湖行","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"仗剑江湖行","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.75","entryPagePath":"pages/login/login","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#ff6b6b","borderStyle":"black","blurEffect":"none","fontSize":"24rpx","iconWidth":"48rpx","spacing":"3px","height":"120rpx","list":[{"pagePath":"pages/character/character","text":"角色","iconPath":"/static/tabbar/backpack.png","selectedIconPath":"/static/tabbar/backpack-active.png"},{"pagePath":"pages/skills/skills","text":"武功","iconPath":"/static/tabbar/skills.png","selectedIconPath":"/static/tabbar/skills-active.png"},{"pagePath":"pages/index/index","text":"江湖","iconPath":"/static/tabbar/adventure.png","selectedIconPath":"/static/tabbar/adventure-active.png"},{"pagePath":"pages/shop/shop","text":"市场","iconPath":"/static/tabbar/shop.png","selectedIconPath":"/static/tabbar/shop-active.png"},{"pagePath":"pages/guild/guild","text":"门派","iconPath":"/static/tabbar/guild.png","selectedIconPath":"/static/tabbar/guild-active.png"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/login/login","meta":{"isQuit":true,"isEntry":true,"softinputMode":"adjustResize","softinputNavBar":"system","navigationBar":{"backgroundColor":"#667eea","titleText":"仗剑江湖行","style":"custom","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/index/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"backgroundColor":"#2c3e50","titleText":"仗剑江湖行","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/character/character","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"backgroundColor":"#2c3e50","titleText":"角色","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/skills/skills","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"backgroundColor":"#2c3e50","titleText":"武功","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/shop/shop","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"navigationBar":{"backgroundColor":"#2c3e50","titleText":"市场","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/guild/guild","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":4,"navigationBar":{"backgroundColor":"#2c3e50","titleText":"门派","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/character/backpack","meta":{"navigationBar":{"backgroundColor":"#2c3e50","titleText":"背包","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/crafting/crafting","meta":{"navigationBar":{"titleText":"打造","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  