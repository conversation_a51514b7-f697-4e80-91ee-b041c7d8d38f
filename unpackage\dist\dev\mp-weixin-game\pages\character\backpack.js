"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../utils/websocket.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const _sfc_main = {
  data() {
    return {
      inventoryData: [],
      inventoryCapacity: 50,
      showItemDetail: false,
      selectedItem: null,
      selectedIndex: -1,
      isLoading: false,
      // 新增：物品配置
      itemsConfig: {}
    };
  },
  onLoad() {
    console.log("[背包页面] onLoad 被调用");
    this.loadInventoryData();
    this.loadItemsConfig();
  },
  onShow() {
    console.log("[背包页面] onShow 被调用");
    this.loadInventoryData();
  },
  onUnload() {
    this.cleanupEventListeners();
  },
  methods: {
    async loadInventoryData() {
      try {
        console.log("[背包页面] 开始加载背包数据");
        this.isLoading = true;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_inventory_data",
          data: {}
        });
        console.log("[背包页面] 收到响应:", response);
        this.isLoading = false;
        if (response.type === "inventory_data") {
          console.log("收到背包数据:", response.data);
          this.inventoryData = response.data.inventory || [];
          this.inventoryCapacity = response.data.capacity || 50;
          console.log("[背包页面] 背包数据已更新:", this.inventoryData);
        } else if (response.type === "error") {
          console.error("WebSocket错误:", response.data);
          if (response.data && response.data.message && response.data.message.includes("背包已满")) {
            common_vendor.index.showToast({
              title: response.data.message,
              icon: "none",
              duration: 3e3
            });
          } else {
            common_vendor.index.showToast({
              title: "网络错误，请重试",
              icon: "none"
            });
          }
        }
      } catch (error) {
        console.error("加载背包数据失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "加载失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    handleItemClick(item, index) {
      console.log("[背包] 选中物品:", item, "索引:", index);
      const config = this.itemsConfig[item.id] || {};
      const type = config.type || item.type || "";
      const sellable = (typeof config.sellable !== "undefined" ? config.sellable : item.sellable) ? true : false;
      this.selectedItem = { ...item, ...config, type, sellable };
      this.selectedIndex = index;
      this.showItemDetail = true;
    },
    closeItemDetail() {
      this.showItemDetail = false;
      this.selectedItem = null;
      this.selectedIndex = -1;
    },
    getItemQualityClass(item) {
      if (!item || !item.品质)
        return "quality-normal";
      switch (item.品质) {
        case "common":
          return "quality-common";
        case "uncommon":
          return "quality-uncommon";
        case "rare":
          return "quality-rare";
        case "epic":
          return "quality-epic";
        case "legendary":
          return "quality-legendary";
        default:
          return "quality-normal";
      }
    },
    getQualityText(quality) {
      const qualityMap = {
        "common": "普通",
        "uncommon": "优秀",
        "rare": "稀有",
        "epic": "史诗",
        "legendary": "传说",
        "mythic": "神话"
      };
      return qualityMap[quality] || "普通";
    },
    getTypeText(type) {
      const typeMap = {
        "weapon": "武器",
        "helmet": "头盔",
        "necklace": "项链",
        "armor": "衣服",
        "cloak": "披风",
        "pants": "裤子",
        "shoes": "鞋子",
        "bracelet": "手镯",
        "ring": "戒指",
        "shield": "盾牌",
        "consumable": "消耗品",
        "medicine": "药品",
        "pill": "丹药",
        "material": "材料",
        "ore": "矿石",
        "wood": "木材",
        "herb": "草药",
        "fur": "兽皮",
        "tool": "工具",
        "pickaxe": "矿镐",
        "axe": "斧头",
        "sickle": "镰刀",
        "knife": "小刀",
        "special": "特殊",
        "quest": "任务",
        "currency": "货币"
      };
      return typeMap[type] || type;
    },
    canEquip(item) {
      const types = [
        "weapon",
        "helmet",
        "necklace",
        "armor",
        "cloak",
        "pants",
        "shoes",
        "bracelet",
        "ring",
        "shield",
        "medal",
        "accessory"
      ];
      return (item.equipable === 1 || item.equipable === true) && item.type !== "gather_tool" || types.includes(item.type);
    },
    canUse(item) {
      return item.usable === 1 || item.usable === true || ["consumable", "medicine", "pill"].includes(item.type);
    },
    getItemEffects(item) {
      const effects = [];
      const attributeMap = {
        "attack": "攻击",
        "defense": "防御",
        "hp": "气血",
        "mp": "内力",
        "energy": "精力",
        "energy_regen": "精力回复",
        "crit": "暴击",
        "dodge": "闪避",
        "hit": "命中",
        "speed": "速度"
      };
      for (const [englishName, chineseName] of Object.entries(attributeMap)) {
        const value = item[englishName] || item[chineseName];
        if (value && value > 0) {
          effects.push({
            name: chineseName,
            value
          });
        }
      }
      const effectsString = item.effects;
      if (effectsString && typeof effectsString === "string") {
        const effectPairs = effectsString.split(",");
        for (const pair of effectPairs) {
          const [attr, val] = pair.split(":");
          if (attr && val) {
            const attrName = attr.trim();
            const attrValue = parseInt(val.trim());
            const chineseName = attributeMap[attrName] || attrName;
            if (attrValue > 0) {
              const existingEffect = effects.find((e) => e.name === chineseName);
              if (existingEffect) {
                existingEffect.value += attrValue;
              } else {
                effects.push({
                  name: chineseName,
                  value: attrValue
                });
              }
            }
          }
        }
      }
      return effects;
    },
    async equipItem() {
      console.log("[装备] 当前selectedItem:", this.selectedItem, "selectedIndex:", this.selectedIndex);
      if (!this.selectedItem || !this.selectedItem.unique_id) {
        console.warn("[装备] 缺少unique_id，无法发送装备请求");
        return;
      }
      try {
        this.isLoading = true;
        const slotType = this.getDefaultSlot(this.selectedItem.type);
        console.log("[装备] 发送equip_item:", {
          unique_id: this.selectedItem.unique_id,
          slot_type: slotType
        });
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "equip_item",
          data: {
            unique_id: this.selectedItem.unique_id,
            type: this.selectedItem.type,
            sellable: this.selectedItem.sellable,
            slot_type: slotType
          }
        });
        this.isLoading = false;
        if (response.type === "equip_success") {
          common_vendor.index.showToast({
            title: response.data.message || "装备成功",
            icon: "success"
          });
          this.closeItemDetail();
          if (response.data.inventory) {
            this.inventoryData = response.data.inventory;
          } else {
            this.loadInventoryData();
          }
        } else if (response.type === "equip_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "装备失败",
            icon: "none"
          });
          this.closeItemDetail();
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "装备失败",
            icon: "none"
          });
          this.closeItemDetail();
        }
      } catch (error) {
        console.error("装备失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "装备失败: " + (error.message || "未知错误"),
          icon: "none"
        });
        this.closeItemDetail();
      }
    },
    getDefaultSlot(itemType) {
      const slotMapping = {
        "weapon": "main_hand",
        "helmet": "helmet",
        "necklace": "necklace",
        "armor": "armor",
        "cloak": "cloak",
        "pants": "pants",
        "shoes": "shoes",
        "bracelet": ["bracelet1", "bracelet2"],
        "ring": ["ring1", "ring2"],
        "shield": "off_hand",
        // 盾牌装备到副手
        "off_hand": "off_hand",
        // 副手装备装备到副手
        "medal": "medal",
        "accessory": ["bracelet1", "bracelet2"]
        // 饰品按手镯槽处理
      };
      const player = utils_gameState.gameState.playerData || {};
      if (itemType === "bracelet" || itemType === "accessory") {
        if (player.equipment && !player.equipment.bracelet1)
          return "bracelet1";
        if (player.equipment && !player.equipment.bracelet2)
          return "bracelet2";
        return "bracelet1";
      }
      if (itemType === "ring") {
        if (player.equipment && !player.equipment.ring1)
          return "ring1";
        if (player.equipment && !player.equipment.ring2)
          return "ring2";
        return "ring1";
      }
      const slot = slotMapping[itemType];
      if (Array.isArray(slot))
        return slot[0];
      return slot || "main_hand";
    },
    useItem() {
      if (!this.selectedItem || this.selectedIndex === -1)
        return;
      common_vendor.index.showModal({
        title: "使用物品",
        content: `确定要使用 ${this.selectedItem.name || this.selectedItem.名称} 吗？`,
        showCancel: true,
        cancelText: "取消",
        confirmText: "使用",
        success: (res) => {
          if (res.confirm) {
            this.sendUseItem();
          }
        }
      });
    },
    async sendUseItem() {
      try {
        this.isLoading = true;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "use_item",
          data: {
            unique_id: this.selectedItem.unique_id,
            type: this.selectedItem.type,
            sellable: this.selectedItem.sellable
          }
        });
        this.isLoading = false;
        if (response.type === "use_item_success") {
          common_vendor.index.showToast({
            title: response.data.message || "使用成功",
            icon: "success"
          });
          this.closeItemDetail();
          if (response.data.inventory) {
            this.inventoryData = response.data.inventory;
          } else {
            this.loadInventoryData();
          }
        } else if (response.type === "use_item_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "使用失败",
            icon: "none"
          });
          this.closeItemDetail();
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "使用失败",
            icon: "none"
          });
          this.closeItemDetail();
        }
      } catch (error) {
        console.error("使用物品失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "使用失败: " + (error.message || "未知错误"),
          icon: "none"
        });
        this.closeItemDetail();
      }
    },
    destroyItem() {
      if (!this.selectedItem || this.selectedIndex === -1)
        return;
      common_vendor.index.showModal({
        title: "销毁物品",
        content: `确定要销毁 ${this.selectedItem.name || this.selectedItem.名称} 吗？此操作不可撤销！`,
        showCancel: true,
        cancelText: "取消",
        confirmText: "销毁",
        success: (res) => {
          if (res.confirm) {
            this.sendDestroyItem();
          }
        }
      });
    },
    async sendDestroyItem() {
      try {
        this.isLoading = true;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "destroy_item",
          data: {
            unique_id: this.selectedItem.unique_id,
            type: this.selectedItem.type,
            sellable: this.selectedItem.sellable
          }
        });
        this.isLoading = false;
        if (response.type === "destroy_item_success") {
          common_vendor.index.showToast({
            title: response.data.message || "销毁成功",
            icon: "success"
          });
          this.showItemDetail = false;
          this.selectedItem = null;
          this.selectedIndex = -1;
          if (response.data.inventory) {
            this.inventoryData = response.data.inventory;
          } else {
            this.loadInventoryData();
          }
        } else if (response.type === "destroy_item_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "销毁失败",
            icon: "none"
          });
          this.closeItemDetail();
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "销毁失败",
            icon: "none"
          });
          this.closeItemDetail();
        }
      } catch (error) {
        console.error("销毁物品失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "销毁失败: " + (error.message || "未知错误"),
          icon: "none"
        });
        this.closeItemDetail();
      }
    },
    async expandInventory() {
      try {
        this.isLoading = true;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "expand_inventory",
          data: {}
        });
        this.isLoading = false;
        if (response.type === "expand_success") {
          common_vendor.index.showToast({
            title: response.data.message || "扩充背包成功",
            icon: "success"
          });
          if (response.data.inventory) {
            this.inventoryData = response.data.inventory;
          }
          if (response.data.capacity) {
            this.inventoryCapacity = response.data.capacity;
          }
          if (!response.data.inventory) {
            this.loadInventoryData();
          }
        } else if (response.type === "expand_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "扩充背包失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "扩充背包失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("扩充背包失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "扩充失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    sortInventory() {
      this.inventoryData.sort((a, b) => {
        const typeOrder = ["weapon", "shield", "helmet", "necklace", "armor", "cloak", "pants", "shoes", "bracelet", "ring", "consumable", "medicine", "pill", "material", "ore", "wood", "herb", "fur", "tool", "pickaxe", "axe", "sickle", "knife", "special", "quest", "currency"];
        const aTypeIndex = typeOrder.indexOf(a.type) || 999;
        const bTypeIndex = typeOrder.indexOf(b.type) || 999;
        if (aTypeIndex !== bTypeIndex) {
          return aTypeIndex - bTypeIndex;
        }
        const qualityOrder = ["mythic", "legendary", "epic", "rare", "uncommon", "common"];
        const aQualityIndex = qualityOrder.indexOf(a.品质 || a.quality) || 999;
        const bQualityIndex = qualityOrder.indexOf(b.品质 || b.quality) || 999;
        return aQualityIndex - bQualityIndex;
      });
      common_vendor.index.showToast({
        title: "背包已整理",
        icon: "success"
      });
    },
    clearInventory() {
      common_vendor.index.showModal({
        title: "确认清空背包",
        content: `确定要清空背包中的所有物品吗？
此操作不可撤销！
当前背包中有 ${this.inventoryData.length} 个物品。`,
        confirmText: "确认清空",
        confirmColor: "#F44336",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.performClearInventory();
          }
        }
      });
    },
    async performClearInventory() {
      try {
        this.isLoading = true;
        common_vendor.index.showToast({
          title: "开始清空背包...",
          icon: "none",
          duration: 1e3
        });
        const itemsToDestroy = [...this.inventoryData];
        let destroyedCount = 0;
        for (let i = 0; i < itemsToDestroy.length; i++) {
          const item = itemsToDestroy[i];
          try {
            await utils_gameData.gameUtils.sendMessage({
              type: "destroy_item",
              data: {
                unique_id: item.unique_id || item.id,
                slot: i
              }
            });
            destroyedCount++;
            if (destroyedCount % 5 === 0 || destroyedCount === itemsToDestroy.length) {
              common_vendor.index.showToast({
                title: `已清空 ${destroyedCount}/${itemsToDestroy.length} 个物品`,
                icon: "none",
                duration: 1e3
              });
            }
            await new Promise((resolve) => setTimeout(resolve, 100));
          } catch (error) {
            console.error("销毁物品失败:", error);
          }
        }
        setTimeout(() => {
          this.loadInventoryData();
          common_vendor.index.showToast({
            title: `背包清空完成！共清空 ${destroyedCount} 个物品`,
            icon: "success",
            duration: 2e3
          });
          this.isLoading = false;
        }, 1500);
      } catch (error) {
        console.error("清空背包失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "清空失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    async loadItemsConfig() {
      this.itemsConfig = await utils_gameState.gameState.getItemsConfig();
    },
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.inventoryData.length),
    b: common_vendor.t($data.inventoryCapacity),
    c: common_vendor.t($data.inventoryData.length),
    d: common_vendor.t($data.inventoryCapacity),
    e: common_vendor.f($data.inventoryData, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.icon || "📦"),
        b: common_vendor.t(item.name || item.名称),
        c: item.数量 > 1 || item.quantity > 1
      }, item.数量 > 1 || item.quantity > 1 ? {
        d: common_vendor.t(item.数量 || item.quantity)
      } : {}, {
        e: index,
        f: common_vendor.n($options.getItemQualityClass(item)),
        g: common_vendor.o(($event) => $options.handleItemClick(item, index), index)
      });
    }),
    f: common_vendor.f(Math.max(0, $data.inventoryCapacity - $data.inventoryData.length), (i, k0, i0) => {
      return {
        a: `empty-${i}`
      };
    }),
    g: common_vendor.o((...args) => $options.expandInventory && $options.expandInventory(...args)),
    h: common_vendor.o((...args) => $options.sortInventory && $options.sortInventory(...args)),
    i: common_vendor.t($data.isLoading ? "处理中..." : "🗑️ 清空背包"),
    j: common_vendor.o((...args) => $options.clearInventory && $options.clearInventory(...args)),
    k: $data.inventoryData.length === 0 || $data.isLoading,
    l: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    m: $data.showItemDetail
  }, $data.showItemDetail ? common_vendor.e({
    n: common_vendor.t($data.selectedItem.name || $data.selectedItem.名称),
    o: common_vendor.t($options.getQualityText($data.selectedItem.品质 || $data.selectedItem.quality)),
    p: common_vendor.t($options.getTypeText($data.selectedItem.类型 || $data.selectedItem.type)),
    q: common_vendor.t($options.getQualityText($data.selectedItem.品质 || $data.selectedItem.quality)),
    r: $data.selectedItem.数量 > 1 || $data.selectedItem.quantity > 1
  }, $data.selectedItem.数量 > 1 || $data.selectedItem.quantity > 1 ? {
    s: common_vendor.t($data.selectedItem.数量 || $data.selectedItem.quantity)
  } : {}, {
    t: $options.getItemEffects($data.selectedItem).length > 0
  }, $options.getItemEffects($data.selectedItem).length > 0 ? {
    v: common_vendor.f($options.getItemEffects($data.selectedItem), (effect, k0, i0) => {
      return {
        a: common_vendor.t(effect.name),
        b: common_vendor.t(effect.value),
        c: effect.name
      };
    })
  } : {}, {
    w: $data.selectedItem.description || $data.selectedItem.描述
  }, $data.selectedItem.description || $data.selectedItem.描述 ? {
    x: common_vendor.t($data.selectedItem.description || $data.selectedItem.描述)
  } : {}, {
    y: $options.canEquip($data.selectedItem)
  }, $options.canEquip($data.selectedItem) ? {
    z: common_vendor.o((...args) => $options.equipItem && $options.equipItem(...args))
  } : {}, {
    A: $options.canUse($data.selectedItem)
  }, $options.canUse($data.selectedItem) ? {
    B: common_vendor.o((...args) => $options.useItem && $options.useItem(...args))
  } : {}, {
    C: common_vendor.o((...args) => $options.destroyItem && $options.destroyItem(...args)),
    D: common_vendor.o((...args) => $options.closeItemDetail && $options.closeItemDetail(...args)),
    E: common_vendor.o(() => {
    }),
    F: common_vendor.o((...args) => $options.closeItemDetail && $options.closeItemDetail(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5c7554f9"]]);
wx.createPage(MiniProgramPage);
