<view class="container data-v-44e2720a"><view wx:if="{{a}}" class="guild-info data-v-44e2720a"><view class="guild-header data-v-44e2720a"><text class="guild-name data-v-44e2720a">{{b}}</text><text class="guild-level data-v-44e2720a">等级 {{c}}</text></view><view class="guild-stats data-v-44e2720a"><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">声望:</text><text class="stat-value data-v-44e2720a">{{d}}</text></view><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">贡献:</text><text class="stat-value data-v-44e2720a">{{e}}</text></view><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">职位:</text><text class="stat-value data-v-44e2720a">{{f}}</text></view></view><view wx:if="{{g}}" class="guild-actions data-v-44e2720a"><button class="daily-reward-btn data-v-44e2720a" bindtap="{{i}}" disabled="{{j}}">{{h}}</button></view></view><view wx:else class="no-guild data-v-44e2720a"><text class="no-guild-title data-v-44e2720a">尚未加入门派</text><text class="no-guild-desc data-v-44e2720a">加入门派可以获得专属武功和任务</text><button class="join-guild-btn data-v-44e2720a" bindtap="{{k}}">加入门派</button></view><view wx:if="{{l}}" class="guild-functions data-v-44e2720a"><view class="function-grid data-v-44e2720a"><view class="function-item data-v-44e2720a" bindtap="{{m}}"><text class="function-icon data-v-44e2720a">📋</text><text class="function-name data-v-44e2720a">门派任务</text></view><view class="function-item data-v-44e2720a" bindtap="{{n}}"><text class="function-icon data-v-44e2720a">⚔️</text><text class="function-name data-v-44e2720a">门派武功</text></view><view class="function-item data-v-44e2720a" bindtap="{{o}}"><text class="function-icon data-v-44e2720a">👥</text><text class="function-name data-v-44e2720a">门派成员</text></view><view class="function-item data-v-44e2720a" bindtap="{{p}}"><text class="function-icon data-v-44e2720a">🏪</text><text class="function-name data-v-44e2720a">门派商店</text></view><view class="function-item data-v-44e2720a" bindtap="{{q}}"><text class="function-icon data-v-44e2720a">🏆</text><text class="function-name data-v-44e2720a">门派排行</text></view><view class="function-item data-v-44e2720a" bindtap="{{r}}"><text class="function-icon data-v-44e2720a">⚔️</text><text class="function-name data-v-44e2720a">门派战争</text></view><view class="function-item data-v-44e2720a" bindtap="{{s}}"><text class="function-icon data-v-44e2720a">🏗️</text><text class="function-name data-v-44e2720a">门派建设</text></view></view></view><view wx:if="{{t}}" class="tasks-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派任务</text><text class="section-close data-v-44e2720a" bindtap="{{v}}">×</text></view><scroll-view class="tasks-list data-v-44e2720a" scroll-y="true"><view wx:for="{{w}}" wx:for-item="task" wx:key="h" class="task-item data-v-44e2720a" bindtap="{{task.i}}"><view class="task-info data-v-44e2720a"><text class="task-name data-v-44e2720a">{{task.a}}</text><text class="task-desc data-v-44e2720a">{{task.b}}</text><text class="task-reward data-v-44e2720a">奖励: {{task.c}}</text></view><view class="task-status data-v-44e2720a"><text class="{{['task-difficulty', 'data-v-44e2720a', task.e]}}">{{task.d}}</text><button class="accept-task-btn data-v-44e2720a" catchtap="{{task.f}}" disabled="{{task.g}}"> 接受 </button></view></view><view wx:if="{{x}}" class="empty-tasks data-v-44e2720a"><text class="data-v-44e2720a">暂无可接任务</text></view></scroll-view></view><view wx:if="{{y}}" class="skills-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派武功</text><text class="section-close data-v-44e2720a" bindtap="{{z}}">×</text></view><scroll-view class="skills-list data-v-44e2720a" scroll-y="true"><view wx:for="{{A}}" wx:for-item="skill" wx:key="i" class="skill-item data-v-44e2720a" bindtap="{{skill.j}}"><view class="skill-info data-v-44e2720a"><text class="skill-name data-v-44e2720a">{{skill.a}}</text><text class="skill-type data-v-44e2720a">{{skill.b}}</text><text class="skill-desc data-v-44e2720a">{{skill.c}}</text></view><view class="skill-status data-v-44e2720a"><text wx:if="{{skill.d}}" class="skill-level data-v-44e2720a">等级 {{skill.e}}</text><button class="learn-skill-btn data-v-44e2720a" catchtap="{{skill.g}}" disabled="{{skill.h}}">{{skill.f}}</button></view></view><view wx:if="{{B}}" class="empty-skills data-v-44e2720a"><text class="data-v-44e2720a">暂无门派武功</text></view></scroll-view></view><view wx:if="{{C}}" class="members-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派成员</text><text class="section-close data-v-44e2720a" bindtap="{{D}}">×</text></view><scroll-view class="members-list data-v-44e2720a" scroll-y="true"><view wx:for="{{E}}" wx:for-item="member" wx:key="e" class="member-item data-v-44e2720a"><view class="member-info data-v-44e2720a"><text class="member-name data-v-44e2720a">{{member.a}}</text><text class="member-position data-v-44e2720a">{{member.b}}</text><text class="member-level data-v-44e2720a">等级 {{member.c}}</text></view><view class="member-contribution data-v-44e2720a"><text class="contribution-label data-v-44e2720a">贡献:</text><text class="contribution-value data-v-44e2720a">{{member.d}}</text></view></view><view wx:if="{{F}}" class="empty-members data-v-44e2720a"><text class="data-v-44e2720a">暂无门派成员</text></view></scroll-view></view><view wx:if="{{G}}" class="shop-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派商店</text><text class="section-close data-v-44e2720a" bindtap="{{H}}">×</text></view><scroll-view class="shop-list data-v-44e2720a" scroll-y="true"><view wx:for="{{I}}" wx:for-item="item" wx:key="f" class="shop-item data-v-44e2720a" bindtap="{{item.g}}"><view class="item-info data-v-44e2720a"><text class="item-name data-v-44e2720a">{{item.a}}</text><text class="item-desc data-v-44e2720a">{{item.b}}</text></view><view class="item-price data-v-44e2720a"><text class="price-value data-v-44e2720a">{{item.c}}</text><text class="price-unit data-v-44e2720a">贡献</text></view><button class="buy-item-btn data-v-44e2720a" catchtap="{{item.d}}" disabled="{{item.e}}"> 购买 </button></view><view wx:if="{{J}}" class="empty-shop data-v-44e2720a"><text class="data-v-44e2720a">暂无商品</text></view></scroll-view></view><view wx:if="{{K}}" class="rankings-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派排行榜</text><text class="section-close data-v-44e2720a" bindtap="{{L}}">×</text></view><view class="ranking-tabs data-v-44e2720a"><view class="{{['ranking-tab', 'data-v-44e2720a', M && 'active']}}" bindtap="{{N}}"> 实力排行 </view><view class="{{['ranking-tab', 'data-v-44e2720a', O && 'active']}}" bindtap="{{P}}"> 武功排行 </view><view class="{{['ranking-tab', 'data-v-44e2720a', Q && 'active']}}" bindtap="{{R}}"> 贡献排行 </view></view><scroll-view class="rankings-list data-v-44e2720a" scroll-y="true"><view wx:for="{{S}}" wx:for-item="sect" wx:key="i" class="ranking-item data-v-44e2720a"><view class="ranking-rank data-v-44e2720a"><text class="rank-number data-v-44e2720a">{{sect.a}}</text></view><view class="ranking-info data-v-44e2720a"><text class="player-name data-v-44e2720a">{{sect.b}}</text><view class="player-stats data-v-44e2720a"><text wx:if="{{T}}" class="stat-text data-v-44e2720a"> 历练: {{sect.c}} | 贡献: {{sect.d}}</text><text wx:elif="{{U}}" class="stat-text data-v-44e2720a"> 武功总分: {{sect.e}} | 武功数量: {{sect.f}}</text><text wx:elif="{{V}}" class="stat-text data-v-44e2720a"> 贡献: {{sect.g}} | 门派等级: {{sect.h}}</text></view></view></view><view wx:if="{{W}}" class="no-rankings data-v-44e2720a"><text class="data-v-44e2720a">暂无排行数据</text></view></scroll-view></view><view wx:if="{{X}}" class="wars-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派战争</text><text class="section-close data-v-44e2720a" bindtap="{{Y}}">×</text></view><view class="war-actions data-v-44e2720a"><button class="declare-war-btn data-v-44e2720a" bindtap="{{Z}}">宣战</button></view><scroll-view class="wars-list data-v-44e2720a" scroll-y="true"><view wx:for="{{aa}}" wx:for-item="war" wx:key="m" class="war-item data-v-44e2720a"><view class="war-header data-v-44e2720a"><view class="war-sides data-v-44e2720a"><text class="{{['sect-name', 'attacker', 'data-v-44e2720a', war.b && 'own-sect']}}">{{war.a}}</text><text class="vs-text data-v-44e2720a">VS</text><text class="{{['sect-name', 'defender', 'data-v-44e2720a', war.d && 'own-sect']}}">{{war.c}}</text></view><view class="{{['war-status', 'data-v-44e2720a', war.f]}}">{{war.e}}</view></view><view class="war-info data-v-44e2720a"><text class="war-reason data-v-44e2720a">战争原因: {{war.g}}</text><text class="war-time data-v-44e2720a">宣战时间: {{war.h}}</text><view wx:if="{{war.i}}" class="war-result data-v-44e2720a"><text class="winner data-v-44e2720a">胜利者: {{war.j}}</text><text class="score data-v-44e2720a">比分: {{war.k}} : {{war.l}}</text></view></view></view><view wx:if="{{ab}}" class="no-wars data-v-44e2720a"><text class="data-v-44e2720a">暂无战争记录</text></view></scroll-view></view><view wx:if="{{ac}}" class="modal-overlay data-v-44e2720a" bindtap="{{ao}}"><view class="modal-content data-v-44e2720a" catchtap="{{an}}"><view class="modal-header data-v-44e2720a"><text class="modal-title data-v-44e2720a">宣战</text><text class="modal-close data-v-44e2720a" bindtap="{{ad}}">×</text></view><view class="modal-body data-v-44e2720a"><view class="form-group data-v-44e2720a"><text class="form-label data-v-44e2720a">选择目标门派:</text><picker class="data-v-44e2720a" value="{{af}}" range="{{ag}}" range-key="name" bindchange="{{ah}}"><view class="picker-display data-v-44e2720a">{{ae}}</view></picker></view><view class="form-group data-v-44e2720a"><text class="form-label data-v-44e2720a">战争原因:</text><block wx:if="{{r0}}"><textarea placeholder="请输入宣战理由..." maxlength="100" class="war-reason-input data-v-44e2720a" value="{{ai}}" bindinput="{{aj}}"/></block></view></view><view class="modal-footer data-v-44e2720a"><button class="cancel-btn data-v-44e2720a" bindtap="{{ak}}">取消</button><button class="confirm-btn data-v-44e2720a" bindtap="{{al}}" disabled="{{am}}"> 宣战 </button></view></view></view><view wx:if="{{ap}}" class="buildings-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派建设</text><text class="section-close data-v-44e2720a" bindtap="{{aq}}">×</text></view><view class="resources-display data-v-44e2720a"><text class="resources-title data-v-44e2720a">门派资源</text><view class="resources-grid data-v-44e2720a"><view class="resource-item data-v-44e2720a"><text class="resource-icon data-v-44e2720a">🪵</text><text class="resource-name data-v-44e2720a">木材</text><text class="resource-value data-v-44e2720a">{{ar}}</text></view><view class="resource-item data-v-44e2720a"><text class="resource-icon data-v-44e2720a">🪨</text><text class="resource-name data-v-44e2720a">石料</text><text class="resource-value data-v-44e2720a">{{as}}</text></view><view class="resource-item data-v-44e2720a"><text class="resource-icon data-v-44e2720a">⚒️</text><text class="resource-name data-v-44e2720a">铁矿</text><text class="resource-value data-v-44e2720a">{{at}}</text></view><view class="resource-item data-v-44e2720a"><text class="resource-icon data-v-44e2720a">💰</text><text class="resource-name data-v-44e2720a">金币</text><text class="resource-value data-v-44e2720a">{{av}}</text></view></view></view><scroll-view class="buildings-list data-v-44e2720a" scroll-y="true"><view wx:for="{{aw}}" wx:for-item="building" wx:key="g" class="building-item data-v-44e2720a"><view class="building-header data-v-44e2720a"><view class="building-info data-v-44e2720a"><text class="building-name data-v-44e2720a">{{building.a}}</text><text class="building-level data-v-44e2720a">等级 {{building.b}}</text></view><button class="upgrade-btn data-v-44e2720a" bindtap="{{building.c}}" disabled="{{building.d}}"> 升级 </button></view><view wx:if="{{building.e}}" class="building-cost data-v-44e2720a"><text class="cost-title data-v-44e2720a">升级消耗:</text><view class="cost-items data-v-44e2720a"><text wx:for="{{building.f}}" wx:for-item="cost" wx:key="c" class="{{['cost-item', 'data-v-44e2720a', cost.d && 'insufficient']}}">{{cost.a}}: {{cost.b}}</text></view></view></view><view wx:if="{{ax}}" class="no-buildings data-v-44e2720a"><text class="data-v-44e2720a">暂无建筑数据</text></view></scroll-view></view><view wx:if="{{ay}}" class="modal-overlay data-v-44e2720a" bindtap="{{aC}}"><view class="modal-content join-sect-guide data-v-44e2720a" catchtap="{{aB}}"><view class="modal-header data-v-44e2720a"><text class="modal-title data-v-44e2720a">如何加入门派</text><text class="modal-close data-v-44e2720a" bindtap="{{az}}">×</text></view><view class="modal-body data-v-44e2720a"><view class="guide-section data-v-44e2720a"><text class="guide-title data-v-44e2720a">📜 门派令牌</text><text class="guide-text data-v-44e2720a">每个门派都需要对应的令牌才能加入，令牌可通过以下方式获得：</text></view><view class="guide-section data-v-44e2720a"><text class="guide-subtitle data-v-44e2720a">🎯 获取方式</text><view class="guide-list data-v-44e2720a"><text class="guide-item data-v-44e2720a">• 完成特定任务获得门派令牌</text><text class="guide-item data-v-44e2720a">• 击败特定NPC有几率掉落令牌</text><text class="guide-item data-v-44e2720a">• 在商店购买门派令牌</text><text class="guide-item data-v-44e2720a">• 参与活动获得令牌奖励</text></view></view><view class="guide-section data-v-44e2720a"><text class="guide-subtitle data-v-44e2720a">⚔️ 主要门派</text><view class="sect-list data-v-44e2720a"><view class="sect-item data-v-44e2720a"><text class="sect-name data-v-44e2720a">少林派</text><text class="sect-desc data-v-44e2720a">以内功和拳法闻名，需要少林令牌</text></view><view class="sect-item data-v-44e2720a"><text class="sect-name data-v-44e2720a">武当派</text><text class="sect-desc data-v-44e2720a">太极剑法和内功修为，需要武当令牌</text></view><view class="sect-item data-v-44e2720a"><text class="sect-name data-v-44e2720a">峨眉派</text><text class="sect-desc data-v-44e2720a">剑法精妙，医术高超，需要峨眉令牌</text></view><view class="sect-item data-v-44e2720a"><text class="sect-name data-v-44e2720a">华山派</text><text class="sect-desc data-v-44e2720a">剑法独步天下，需要华山令牌</text></view></view></view><view class="guide-section data-v-44e2720a"><text class="guide-subtitle data-v-44e2720a">💡 使用方法</text><text class="guide-text data-v-44e2720a">获得门派令牌后，在背包中使用即可自动加入对应门派。加入成功后将有全服公告。</text></view></view><view class="modal-footer data-v-44e2720a"><button class="modal-btn confirm-btn data-v-44e2720a" bindtap="{{aA}}">我知道了</button></view></view></view><view wx:if="{{aD}}" class="modal-overlay data-v-44e2720a" bindtap="{{aO}}"><view class="modal-content data-v-44e2720a" catchtap="{{aN}}"><view class="modal-header data-v-44e2720a"><text class="modal-title data-v-44e2720a">任务详情</text><text class="modal-close data-v-44e2720a" bindtap="{{aE}}">×</text></view><view wx:if="{{aF}}" class="modal-body data-v-44e2720a"><text class="detail-name data-v-44e2720a">{{aG}}</text><text class="detail-desc data-v-44e2720a">{{aH}}</text><text class="detail-requirement data-v-44e2720a">要求: {{aI}}</text><text class="detail-reward data-v-44e2720a">奖励: {{aJ}}</text></view><view class="modal-footer data-v-44e2720a"><button class="modal-btn cancel-btn data-v-44e2720a" bindtap="{{aK}}">关闭</button><button class="modal-btn confirm-btn data-v-44e2720a" bindtap="{{aL}}" disabled="{{aM}}"> 接受任务 </button></view></view></view></view>