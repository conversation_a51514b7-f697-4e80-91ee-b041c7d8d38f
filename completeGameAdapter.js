/**
 * 完整的小游戏适配器
 * 将所有原有小程序功能完整移植到小游戏Canvas环境
 */

class CompleteGameAdapter {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.systemInfo = {};
    this.currentPage = 'login';
    this.gameData = {};
    this.wsManager = null;
    this.inputMode = false;
    this.currentInput = null;
    this.formData = {
      login: { username: '', password: '' },
      register: { username: '', password: '', confirmPassword: '', characterName: '', gender: 'male' }
    };
    this.playerData = null;
    this.isRegistering = false;
    
    // UI配置
    this.ui = {
      colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        background: '#f5f5f5',
        white: '#ffffff',
        text: '#333333',
        textSecondary: '#666666',
        border: '#e0e0e0'
      },
      fonts: {
        title: '32px Arial',
        large: '24px Arial',
        normal: '18px Arial',
        small: '14px Arial'
      }
    };
    
    this.init();
  }
  
  // 初始化
  async init() {
    console.log('初始化完整游戏适配器...');
    
    // 获取系统信息
    this.systemInfo = wx.getSystemInfoSync();
    
    // 创建Canvas
    this.canvas = wx.createCanvas();
    this.ctx = this.canvas.getContext('2d');
    
    // 设置Canvas尺寸
    this.canvas.width = this.systemInfo.screenWidth;
    this.canvas.height = this.systemInfo.screenHeight;
    
    console.log('Canvas创建成功:', this.canvas.width, 'x', this.canvas.height);
    
    // 初始化WebSocket管理器
    this.initWebSocket();
    
    // 注册触摸事件
    this.registerTouchEvents();
    
    // 渲染初始页面
    this.renderCurrentPage();
    
    console.log('完整游戏适配器初始化完成');
  }
  
  // 初始化WebSocket
  initWebSocket() {
    try {
      // 导入WebSocket管理器
      const wsManager = require('./utils/websocket.js');
      this.wsManager = wsManager.default || wsManager;
      
      // 设置消息处理器
      if (this.wsManager && typeof this.wsManager.setMessageHandler === 'function') {
        this.wsManager.setMessageHandler((data) => {
          this.handleWebSocketMessage(data);
        });
      }
      
      console.log('WebSocket管理器初始化成功');
    } catch (error) {
      console.error('WebSocket管理器初始化失败:', error);
    }
  }
  
  // 处理WebSocket消息
  handleWebSocketMessage(data) {
    console.log('收到WebSocket消息:', data);
    
    switch (data.type) {
      case 'login_success':
        this.playerData = data.data;
        this.currentPage = 'index';
        this.renderCurrentPage();
        this.showToast('登录成功');
        break;
        
      case 'login_failed':
        this.showToast('登录失败：' + data.message);
        break;
        
      case 'register_success':
        this.showToast('注册成功，请登录');
        this.isRegistering = false;
        this.renderCurrentPage();
        break;
        
      case 'register_failed':
        this.showToast('注册失败：' + data.message);
        break;
        
      case 'player_data':
        this.playerData = data.data;
        this.renderCurrentPage();
        break;
        
      default:
        console.log('未处理的消息类型:', data.type);
    }
  }
  
  // 注册触摸事件
  registerTouchEvents() {
    wx.onTouchEnd((e) => {
      if (!e.touches || e.touches.length === 0) return;
      
      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;
      
      this.handleTouch(x, y);
    });
  }
  
  // 处理触摸事件
  handleTouch(x, y) {
    console.log('触摸事件:', x, y, '当前页面:', this.currentPage);
    
    // 如果在输入模式，处理输入相关事件
    if (this.inputMode) {
      this.handleInputTouch(x, y);
      return;
    }
    
    // 根据当前页面处理触摸事件
    switch (this.currentPage) {
      case 'login':
        this.handleLoginTouch(x, y);
        break;
      case 'index':
        this.handleIndexTouch(x, y);
        break;
      case 'character':
        this.handleCharacterTouch(x, y);
        break;
      case 'skills':
        this.handleSkillsTouch(x, y);
        break;
      case 'shop':
        this.handleShopTouch(x, y);
        break;
      case 'guild':
        this.handleGuildTouch(x, y);
        break;
      case 'backpack':
        this.handleBackpackTouch(x, y);
        break;
      case 'crafting':
        this.handleCraftingTouch(x, y);
        break;
    }
  }
  
  // 渲染当前页面
  renderCurrentPage() {
    switch (this.currentPage) {
      case 'login':
        this.renderLoginPage();
        break;
      case 'index':
        this.renderIndexPage();
        break;
      case 'character':
        this.renderCharacterPage();
        break;
      case 'skills':
        this.renderSkillsPage();
        break;
      case 'shop':
        this.renderShopPage();
        break;
      case 'guild':
        this.renderGuildPage();
        break;
      case 'backpack':
        this.renderBackpackPage();
        break;
      case 'crafting':
        this.renderCraftingPage();
        break;
    }
  }
  
  // 清空画布
  clearCanvas() {
    this.ctx.fillStyle = this.ui.colors.background;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }
  
  // 绘制文本
  drawText(text, x, y, options = {}) {
    const {
      font = this.ui.fonts.normal,
      color = this.ui.colors.text,
      align = 'left',
      baseline = 'top'
    } = options;
    
    this.ctx.font = font;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    this.ctx.textBaseline = baseline;
    this.ctx.fillText(text, x, y);
  }
  
  // 绘制按钮
  drawButton(text, x, y, width, height, options = {}) {
    const {
      backgroundColor = this.ui.colors.primary,
      textColor = this.ui.colors.white,
      font = this.ui.fonts.normal,
      borderRadius = 5
    } = options;
    
    // 绘制按钮背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制按钮文本
    this.drawText(text, x + width / 2, y + height / 2, {
      font,
      color: textColor,
      align: 'center',
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  }
  
  // 绘制输入框
  drawInputBox(x, y, width, height, value, placeholder, options = {}) {
    const {
      backgroundColor = this.ui.colors.white,
      borderColor = this.ui.colors.border,
      textColor = this.ui.colors.text,
      placeholderColor = this.ui.colors.textSecondary,
      font = this.ui.fonts.normal
    } = options;
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    this.ctx.strokeStyle = borderColor;
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);
    
    // 绘制文本或占位符
    const displayText = value || placeholder;
    const textColor = value ? textColor : placeholderColor;
    
    this.drawText(displayText, x + 10, y + height / 2, {
      font,
      color: textColor,
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  }
  
  // 显示输入对话框
  showInputDialog(title, placeholder, callback) {
    wx.showModal({
      title: title,
      editable: true,
      placeholderText: placeholder,
      success: (res) => {
        if (res.confirm && res.content) {
          callback(res.content);
        }
      }
    });
  }
  
  // 显示提示
  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  // 页面导航
  navigateTo(page) {
    console.log('导航到页面:', page);
    this.currentPage = page;
    this.renderCurrentPage();
  }
  
  // 发送WebSocket消息
  sendMessage(data) {
    if (this.wsManager && typeof this.wsManager.send === 'function') {
      this.wsManager.send(data);
    } else {
      console.error('WebSocket管理器未初始化');
    }
  }
}

  // 渲染登录页面
  renderLoginPage() {
    this.clearCanvas();

    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // 绘制标题
    this.drawText('仗剑江湖行', this.canvas.width / 2, 80, {
      font: 'bold 36px Arial',
      color: this.ui.colors.white,
      align: 'center'
    });

    this.drawText('小游戏版本', this.canvas.width / 2, 130, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.white,
      align: 'center'
    });

    // 绘制表单背景
    const formX = 30;
    const formY = 180;
    const formWidth = this.canvas.width - 60;
    const formHeight = this.isRegistering ? 450 : 320;

    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
    this.ctx.fillRect(formX, formY, formWidth, formHeight);

    if (this.isRegistering) {
      this.renderRegisterForm(formX, formY, formWidth);
    } else {
      this.renderLoginForm(formX, formY, formWidth);
    }
  }

  // 渲染登录表单
  renderLoginForm(formX, formY, formWidth) {
    // 表单标题
    this.drawText('登录江湖', formX + formWidth / 2, formY + 30, {
      font: 'bold 28px Arial',
      color: this.ui.colors.text,
      align: 'center'
    });

    // 账号输入框
    this.drawText('账号:', formX + 20, formY + 80, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.usernameInput = this.drawInputBox(
      formX + 20, formY + 105, formWidth - 40, 40,
      this.formData.login.username, '请输入账号'
    );

    // 密码输入框
    this.drawText('密码:', formX + 20, formY + 160, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.passwordInput = this.drawInputBox(
      formX + 20, formY + 185, formWidth - 40, 40,
      this.formData.login.password ? '••••••••' : '', '请输入密码'
    );

    // 登录按钮
    this.loginButton = this.drawButton(
      '登录', formX + 20, formY + 245, formWidth - 40, 45,
      { backgroundColor: this.ui.colors.primary, font: 'bold 20px Arial' }
    );

    // 注册链接
    this.drawText('还没有账号？点击注册', formX + formWidth / 2, formY + 310, {
      font: this.ui.fonts.small,
      color: this.ui.colors.secondary,
      align: 'center'
    });

    this.registerLink = {
      x: formX + 20,
      y: formY + 300,
      width: formWidth - 40,
      height: 20
    };
  }

  // 渲染注册表单
  renderRegisterForm(formX, formY, formWidth) {
    // 表单标题
    this.drawText('创建角色', formX + formWidth / 2, formY + 30, {
      font: 'bold 28px Arial',
      color: this.ui.colors.text,
      align: 'center'
    });

    let currentY = formY + 70;
    const fieldHeight = 65;

    // 账号输入框
    this.drawText('账号:', formX + 20, currentY, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.regUsernameInput = this.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.register.username, '请输入账号'
    );
    currentY += fieldHeight;

    // 密码输入框
    this.drawText('密码:', formX + 20, currentY, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.regPasswordInput = this.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.register.password ? '••••••••' : '', '请输入密码'
    );
    currentY += fieldHeight;

    // 确认密码输入框
    this.drawText('确认密码:', formX + 20, currentY, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.regConfirmPasswordInput = this.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.register.confirmPassword ? '••••••••' : '', '请再次输入密码'
    );
    currentY += fieldHeight;

    // 角色名输入框
    this.drawText('角色名:', formX + 20, currentY, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    this.regCharacterNameInput = this.drawInputBox(
      formX + 20, currentY + 20, formWidth - 40, 35,
      this.formData.register.characterName, '请输入角色名'
    );
    currentY += fieldHeight;

    // 性别选择
    this.drawText('性别:', formX + 20, currentY, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.text
    });

    const genderButtonWidth = 80;
    this.maleButton = this.drawButton(
      '男', formX + 20, currentY + 20, genderButtonWidth, 35,
      {
        backgroundColor: this.formData.register.gender === 'male' ? this.ui.colors.primary : this.ui.colors.background,
        textColor: this.formData.register.gender === 'male' ? this.ui.colors.white : this.ui.colors.text
      }
    );

    this.femaleButton = this.drawButton(
      '女', formX + 120, currentY + 20, genderButtonWidth, 35,
      {
        backgroundColor: this.formData.register.gender === 'female' ? this.ui.colors.primary : this.ui.colors.background,
        textColor: this.formData.register.gender === 'female' ? this.ui.colors.white : this.ui.colors.text
      }
    );
    currentY += 70;

    // 注册按钮
    this.registerButton = this.drawButton(
      '创建角色', formX + 20, currentY, formWidth - 40, 45,
      { backgroundColor: this.ui.colors.success, font: 'bold 20px Arial' }
    );

    // 返回登录链接
    this.drawText('已有账号？返回登录', formX + formWidth / 2, currentY + 65, {
      font: this.ui.fonts.small,
      color: this.ui.colors.secondary,
      align: 'center'
    });

    this.backToLoginLink = {
      x: formX + 20,
      y: currentY + 55,
      width: formWidth - 40,
      height: 20
    };
  }

  // 处理登录页面触摸
  handleLoginTouch(x, y) {
    if (this.isRegistering) {
      // 注册界面触摸处理
      if (this.isPointInRect(x, y, this.regUsernameInput)) {
        this.showInputDialog('输入账号', '请输入账号', (value) => {
          this.formData.register.username = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.regPasswordInput)) {
        this.showInputDialog('输入密码', '请输入密码', (value) => {
          this.formData.register.password = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.regConfirmPasswordInput)) {
        this.showInputDialog('确认密码', '请再次输入密码', (value) => {
          this.formData.register.confirmPassword = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.regCharacterNameInput)) {
        this.showInputDialog('输入角色名', '请输入角色名', (value) => {
          this.formData.register.characterName = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.maleButton)) {
        this.formData.register.gender = 'male';
        this.renderCurrentPage();
      } else if (this.isPointInRect(x, y, this.femaleButton)) {
        this.formData.register.gender = 'female';
        this.renderCurrentPage();
      } else if (this.isPointInRect(x, y, this.registerButton)) {
        this.handleRegister();
      } else if (this.isPointInRect(x, y, this.backToLoginLink)) {
        this.isRegistering = false;
        this.renderCurrentPage();
      }
    } else {
      // 登录界面触摸处理
      if (this.isPointInRect(x, y, this.usernameInput)) {
        this.showInputDialog('输入账号', '请输入账号', (value) => {
          this.formData.login.username = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.passwordInput)) {
        this.showInputDialog('输入密码', '请输入密码', (value) => {
          this.formData.login.password = value;
          this.renderCurrentPage();
        });
      } else if (this.isPointInRect(x, y, this.loginButton)) {
        this.handleLogin();
      } else if (this.isPointInRect(x, y, this.registerLink)) {
        this.isRegistering = true;
        this.renderCurrentPage();
      }
    }
  }

  // 处理登录
  handleLogin() {
    const { username, password } = this.formData.login;

    if (!username || !password) {
      this.showToast('请输入账号和密码');
      return;
    }

    console.log('执行登录:', username);

    // 发送登录请求
    this.sendMessage({
      type: 'login',
      data: { username, password }
    });

    this.showToast('正在登录...');
  }

  // 处理注册
  handleRegister() {
    const { username, password, confirmPassword, characterName, gender } = this.formData.register;

    if (!username || !password || !confirmPassword || !characterName) {
      this.showToast('请填写完整信息');
      return;
    }

    if (password !== confirmPassword) {
      this.showToast('两次密码输入不一致');
      return;
    }

    console.log('执行注册:', { username, characterName, gender });

    // 发送注册请求
    this.sendMessage({
      type: 'register',
      data: { username, password, characterName, gender }
    });

    this.showToast('正在注册...');
  }

  // 检查点是否在矩形内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  }

  // 渲染主页面
  renderIndexPage() {
    this.clearCanvas();

    // 绘制头部
    this.drawHeader('仗剑江湖行');

    const contentY = 80;

    // 绘制玩家信息
    if (this.playerData) {
      this.drawPlayerInfo(contentY);
    }

    // 绘制闯江湖按钮
    this.adventureButton = this.drawButton(
      '闯江湖', 30, contentY + 200, this.canvas.width - 60, 50,
      { backgroundColor: this.ui.colors.success, font: 'bold 20px Arial' }
    );

    // 绘制最近事件
    this.drawRecentEvents(contentY + 280);

    // 绘制底部导航
    this.drawTabBar();
  }

  // 绘制头部
  drawHeader(title) {
    this.ctx.fillStyle = this.ui.colors.primary;
    this.ctx.fillRect(0, 0, this.canvas.width, 70);

    this.drawText(title, this.canvas.width / 2, 35, {
      font: 'bold 24px Arial',
      color: this.ui.colors.white,
      align: 'center',
      baseline: 'middle'
    });
  }

  // 绘制玩家信息
  drawPlayerInfo(y) {
    const infoY = y + 20;

    this.drawText('角色信息', 30, infoY, {
      font: 'bold 20px Arial',
      color: this.ui.colors.text
    });

    if (this.playerData) {
      this.drawText(`角色名: ${this.playerData.characterName || '无名侠客'}`, 30, infoY + 35, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.text
      });

      this.drawText(`等级: ${this.playerData.level || 1}`, 30, infoY + 65, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.text
      });

      this.drawText(`银两: ${this.playerData.money || 0}`, 30, infoY + 95, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.text
      });

      this.drawText(`经验: ${this.playerData.exp || 0}`, 30, infoY + 125, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.text
      });
    }
  }

  // 绘制最近事件
  drawRecentEvents(y) {
    this.drawText('最近事件', 30, y, {
      font: 'bold 18px Arial',
      color: this.ui.colors.text
    });

    // 模拟一些事件
    const events = [
      '你在江湖中闯荡，获得了一些经验',
      '遇到了一位神秘商人',
      '在客栈中听到了江湖传说'
    ];

    events.forEach((event, index) => {
      this.drawText(`• ${event}`, 30, y + 30 + index * 25, {
        font: this.ui.fonts.small,
        color: this.ui.colors.textSecondary
      });
    });
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarY = this.canvas.height - 80;
    const tabWidth = this.canvas.width / 5;

    // 绘制背景
    this.ctx.fillStyle = this.ui.colors.white;
    this.ctx.fillRect(0, tabBarY, this.canvas.width, 80);

    // 绘制分割线
    this.ctx.strokeStyle = this.ui.colors.border;
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(0, tabBarY);
    this.ctx.lineTo(this.canvas.width, tabBarY);
    this.ctx.stroke();

    // 导航项
    const tabs = [
      { name: '角色', page: 'character' },
      { name: '武功', page: 'skills' },
      { name: '江湖', page: 'index' },
      { name: '市场', page: 'shop' },
      { name: '门派', page: 'guild' }
    ];

    this.tabButtons = [];

    tabs.forEach((tab, index) => {
      const x = index * tabWidth;
      const isActive = this.currentPage === tab.page;

      // 绘制标签文本
      this.drawText(tab.name, x + tabWidth / 2, tabBarY + 40, {
        font: this.ui.fonts.normal,
        color: isActive ? this.ui.colors.primary : this.ui.colors.textSecondary,
        align: 'center',
        baseline: 'middle'
      });

      this.tabButtons.push({
        x: x,
        y: tabBarY,
        width: tabWidth,
        height: 80,
        page: tab.page
      });
    });
  }

  // 处理主页面触摸
  handleIndexTouch(x, y) {
    // 检查底部导航栏
    if (this.tabButtons) {
      for (const button of this.tabButtons) {
        if (this.isPointInRect(x, y, button)) {
          this.navigateTo(button.page);
          return;
        }
      }
    }

    // 检查闯江湖按钮
    if (this.adventureButton && this.isPointInRect(x, y, this.adventureButton)) {
      this.handleAdventure();
    }
  }

  // 处理冒险
  handleAdventure() {
    this.showToast('开始冒险...');

    // 发送冒险请求
    this.sendMessage({
      type: 'adventure',
      action: 'start'
    });
  }

  // 渲染角色页面
  renderCharacterPage() {
    this.clearCanvas();
    this.drawHeader('角色信息');

    const contentY = 100;

    if (this.playerData) {
      // 绘制角色详细信息
      this.drawText('基本信息', 30, contentY, {
        font: 'bold 20px Arial',
        color: this.ui.colors.text
      });

      const fields = [
        { label: '角色名', value: this.playerData.characterName || '无名侠客' },
        { label: '等级', value: this.playerData.level || 1 },
        { label: '经验', value: `${this.playerData.exp || 0}/${this.playerData.maxExp || 100}` },
        { label: '银两', value: this.playerData.money || 0 },
        { label: '生命值', value: `${this.playerData.hp || 100}/${this.playerData.maxHp || 100}` },
        { label: '内力', value: `${this.playerData.mp || 50}/${this.playerData.maxMp || 50}` }
      ];

      fields.forEach((field, index) => {
        const y = contentY + 40 + index * 35;
        this.drawText(`${field.label}:`, 30, y, {
          font: this.ui.fonts.normal,
          color: this.ui.colors.text
        });
        this.drawText(field.value.toString(), 150, y, {
          font: this.ui.fonts.normal,
          color: this.ui.colors.textSecondary
        });
      });

      // 绘制属性信息
      this.drawText('属性', 30, contentY + 280, {
        font: 'bold 20px Arial',
        color: this.ui.colors.text
      });

      const attributes = [
        { label: '攻击力', value: this.playerData.attack || 10 },
        { label: '防御力', value: this.playerData.defense || 5 },
        { label: '敏捷', value: this.playerData.agility || 8 },
        { label: '幸运', value: this.playerData.luck || 5 }
      ];

      attributes.forEach((attr, index) => {
        const y = contentY + 320 + index * 35;
        this.drawText(`${attr.label}:`, 30, y, {
          font: this.ui.fonts.normal,
          color: this.ui.colors.text
        });
        this.drawText(attr.value.toString(), 150, y, {
          font: this.ui.fonts.normal,
          color: this.ui.colors.textSecondary
        });
      });
    }

    this.drawTabBar();
  }

  // 处理角色页面触摸
  handleCharacterTouch(x, y) {
    this.handleTabBarTouch(x, y);
  }

  // 处理底部导航栏触摸
  handleTabBarTouch(x, y) {
    if (this.tabButtons) {
      for (const button of this.tabButtons) {
        if (this.isPointInRect(x, y, button)) {
          this.navigateTo(button.page);
          return;
        }
      }
    }
  }

  // 渲染武功页面
  renderSkillsPage() {
    this.clearCanvas();
    this.drawHeader('武功秘籍');

    const contentY = 100;

    // 绘制武功列表
    this.drawText('已学武功', 30, contentY, {
      font: 'bold 20px Arial',
      color: this.ui.colors.text
    });

    const skills = this.playerData?.skills || [
      { name: '基础剑法', level: 1, description: '最基础的剑法' },
      { name: '轻功', level: 1, description: '提升移动速度' }
    ];

    this.skillButtons = [];

    skills.forEach((skill, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制技能背景
      this.ctx.fillStyle = this.ui.colors.white;
      this.ctx.fillRect(30, y, this.canvas.width - 60, 70);

      this.ctx.strokeStyle = this.ui.colors.border;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(30, y, this.canvas.width - 60, 70);

      // 绘制技能信息
      this.drawText(skill.name, 50, y + 20, {
        font: 'bold 18px Arial',
        color: this.ui.colors.text
      });

      this.drawText(`等级: ${skill.level}`, 50, y + 45, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.textSecondary
      });

      this.drawText(skill.description, 200, y + 20, {
        font: this.ui.fonts.small,
        color: this.ui.colors.textSecondary
      });

      // 升级按钮
      const upgradeButton = this.drawButton(
        '升级', this.canvas.width - 120, y + 15, 80, 40,
        { backgroundColor: this.ui.colors.secondary, font: this.ui.fonts.small }
      );

      this.skillButtons.push({
        ...upgradeButton,
        skillIndex: index,
        action: 'upgrade'
      });
    });

    this.drawTabBar();
  }

  // 处理武功页面触摸
  handleSkillsTouch(x, y) {
    // 检查技能按钮
    if (this.skillButtons) {
      for (const button of this.skillButtons) {
        if (this.isPointInRect(x, y, button)) {
          this.handleSkillAction(button.skillIndex, button.action);
          return;
        }
      }
    }

    this.handleTabBarTouch(x, y);
  }

  // 处理技能操作
  handleSkillAction(skillIndex, action) {
    if (action === 'upgrade') {
      this.showToast('升级武功...');

      this.sendMessage({
        type: 'skill',
        action: 'upgrade',
        skillIndex: skillIndex
      });
    }
  }

  // 渲染商店页面
  renderShopPage() {
    this.clearCanvas();
    this.drawHeader('江湖市场');

    const contentY = 100;

    // 绘制商品列表
    this.drawText('商品列表', 30, contentY, {
      font: 'bold 20px Arial',
      color: this.ui.colors.text
    });

    const items = [
      { name: '铁剑', price: 100, description: '普通的铁制长剑' },
      { name: '布衣', price: 50, description: '简单的布制衣服' },
      { name: '回血丹', price: 20, description: '恢复生命值的丹药' },
      { name: '内力丹', price: 30, description: '恢复内力的丹药' }
    ];

    this.shopButtons = [];

    items.forEach((item, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制商品背景
      this.ctx.fillStyle = this.ui.colors.white;
      this.ctx.fillRect(30, y, this.canvas.width - 60, 70);

      this.ctx.strokeStyle = this.ui.colors.border;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(30, y, this.canvas.width - 60, 70);

      // 绘制商品信息
      this.drawText(item.name, 50, y + 20, {
        font: 'bold 18px Arial',
        color: this.ui.colors.text
      });

      this.drawText(`价格: ${item.price}银两`, 50, y + 45, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.warning
      });

      this.drawText(item.description, 200, y + 20, {
        font: this.ui.fonts.small,
        color: this.ui.colors.textSecondary
      });

      // 购买按钮
      const buyButton = this.drawButton(
        '购买', this.canvas.width - 120, y + 15, 80, 40,
        { backgroundColor: this.ui.colors.success, font: this.ui.fonts.small }
      );

      this.shopButtons.push({
        ...buyButton,
        itemIndex: index,
        action: 'buy'
      });
    });

    this.drawTabBar();
  }

  // 处理商店页面触摸
  handleShopTouch(x, y) {
    // 检查商品按钮
    if (this.shopButtons) {
      for (const button of this.shopButtons) {
        if (this.isPointInRect(x, y, button)) {
          this.handleShopAction(button.itemIndex, button.action);
          return;
        }
      }
    }

    this.handleTabBarTouch(x, y);
  }

  // 处理商店操作
  handleShopAction(itemIndex, action) {
    if (action === 'buy') {
      this.showToast('购买物品...');

      this.sendMessage({
        type: 'shop',
        action: 'buy',
        itemIndex: itemIndex
      });
    }
  }

  // 渲染门派页面
  renderGuildPage() {
    this.clearCanvas();
    this.drawHeader('门派系统');

    const contentY = 100;

    if (this.playerData?.guild) {
      // 已加入门派
      this.drawText(`当前门派: ${this.playerData.guild.name}`, 30, contentY, {
        font: 'bold 20px Arial',
        color: this.ui.colors.text
      });

      this.drawText(`门派等级: ${this.playerData.guild.level}`, 30, contentY + 40, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.textSecondary
      });

      this.drawText(`贡献度: ${this.playerData.guild.contribution}`, 30, contentY + 70, {
        font: this.ui.fonts.normal,
        color: this.ui.colors.textSecondary
      });

      // 门派功能按钮
      this.guildDonateButton = this.drawButton(
        '捐献', 30, contentY + 120, 100, 40,
        { backgroundColor: this.ui.colors.warning }
      );

      this.guildLeaveButton = this.drawButton(
        '退出门派', 150, contentY + 120, 100, 40,
        { backgroundColor: this.ui.colors.danger }
      );
    } else {
      // 未加入门派
      this.drawText('可加入的门派', 30, contentY, {
        font: 'bold 20px Arial',
        color: this.ui.colors.text
      });

      const guilds = [
        { name: '华山派', description: '以剑法闻名的门派' },
        { name: '武当派', description: '内功深厚的道家门派' },
        { name: '少林寺', description: '佛门圣地，武学渊源' }
      ];

      this.guildJoinButtons = [];

      guilds.forEach((guild, index) => {
        const y = contentY + 50 + index * 80;

        // 绘制门派背景
        this.ctx.fillStyle = this.ui.colors.white;
        this.ctx.fillRect(30, y, this.canvas.width - 60, 70);

        this.ctx.strokeStyle = this.ui.colors.border;
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(30, y, this.canvas.width - 60, 70);

        // 绘制门派信息
        this.drawText(guild.name, 50, y + 20, {
          font: 'bold 18px Arial',
          color: this.ui.colors.text
        });

        this.drawText(guild.description, 50, y + 45, {
          font: this.ui.fonts.small,
          color: this.ui.colors.textSecondary
        });

        // 加入按钮
        const joinButton = this.drawButton(
          '加入', this.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.colors.primary, font: this.ui.fonts.small }
        );

        this.guildJoinButtons.push({
          ...joinButton,
          guildIndex: index,
          action: 'join'
        });
      });
    }

    this.drawTabBar();
  }

  // 处理门派页面触摸
  handleGuildTouch(x, y) {
    if (this.playerData?.guild) {
      // 已加入门派的操作
      if (this.guildDonateButton && this.isPointInRect(x, y, this.guildDonateButton)) {
        this.handleGuildDonate();
      } else if (this.guildLeaveButton && this.isPointInRect(x, y, this.guildLeaveButton)) {
        this.handleGuildLeave();
      }
    } else {
      // 加入门派的操作
      if (this.guildJoinButtons) {
        for (const button of this.guildJoinButtons) {
          if (this.isPointInRect(x, y, button)) {
            this.handleGuildJoin(button.guildIndex);
            return;
          }
        }
      }
    }

    this.handleTabBarTouch(x, y);
  }

  // 处理门派捐献
  handleGuildDonate() {
    this.showToast('捐献门派...');

    this.sendMessage({
      type: 'guild',
      action: 'donate',
      amount: 100
    });
  }

  // 处理退出门派
  handleGuildLeave() {
    wx.showModal({
      title: '确认',
      content: '确定要退出门派吗？',
      success: (res) => {
        if (res.confirm) {
          this.sendMessage({
            type: 'guild',
            action: 'leave'
          });
        }
      }
    });
  }

  // 处理加入门派
  handleGuildJoin(guildIndex) {
    this.showToast('申请加入门派...');

    this.sendMessage({
      type: 'guild',
      action: 'join',
      guildIndex: guildIndex
    });
  }

  // 渲染背包页面
  renderBackpackPage() {
    this.clearCanvas();
    this.drawHeader('背包');

    const contentY = 100;

    this.drawText('物品列表', 30, contentY, {
      font: 'bold 20px Arial',
      color: this.ui.colors.text
    });

    const items = this.playerData?.items || [
      { name: '新手剑', type: 'weapon', description: '初学者使用的剑' },
      { name: '布衣', type: 'armor', description: '简单的防护服' },
      { name: '回血丹', type: 'consumable', count: 5, description: '恢复生命值' }
    ];

    this.backpackButtons = [];

    items.forEach((item, index) => {
      const y = contentY + 50 + index * 80;

      // 绘制物品背景
      this.ctx.fillStyle = this.ui.colors.white;
      this.ctx.fillRect(30, y, this.canvas.width - 60, 70);

      this.ctx.strokeStyle = this.ui.colors.border;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(30, y, this.canvas.width - 60, 70);

      // 绘制物品信息
      const itemName = item.count ? `${item.name} x${item.count}` : item.name;
      this.drawText(itemName, 50, y + 20, {
        font: 'bold 18px Arial',
        color: this.ui.colors.text
      });

      this.drawText(item.description, 50, y + 45, {
        font: this.ui.fonts.small,
        color: this.ui.colors.textSecondary
      });

      // 使用按钮
      if (item.type === 'consumable') {
        const useButton = this.drawButton(
          '使用', this.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.colors.success, font: this.ui.fonts.small }
        );

        this.backpackButtons.push({
          ...useButton,
          itemIndex: index,
          action: 'use'
        });
      } else if (item.type === 'weapon' || item.type === 'armor') {
        const equipButton = this.drawButton(
          '装备', this.canvas.width - 120, y + 15, 80, 40,
          { backgroundColor: this.ui.colors.primary, font: this.ui.fonts.small }
        );

        this.backpackButtons.push({
          ...equipButton,
          itemIndex: index,
          action: 'equip'
        });
      }
    });

    this.drawTabBar();
  }

  // 处理背包页面触摸
  handleBackpackTouch(x, y) {
    // 检查物品按钮
    if (this.backpackButtons) {
      for (const button of this.backpackButtons) {
        if (this.isPointInRect(x, y, button)) {
          this.handleBackpackAction(button.itemIndex, button.action);
          return;
        }
      }
    }

    this.handleTabBarTouch(x, y);
  }

  // 处理背包操作
  handleBackpackAction(itemIndex, action) {
    this.showToast(`${action === 'use' ? '使用' : '装备'}物品...`);

    this.sendMessage({
      type: 'backpack',
      action: action,
      itemIndex: itemIndex
    });
  }

  // 渲染锻造页面
  renderCraftingPage() {
    this.clearCanvas();
    this.drawHeader('锻造系统');

    const contentY = 100;

    this.drawText('可锻造物品', 30, contentY, {
      font: 'bold 20px Arial',
      color: this.ui.colors.text
    });

    // 这里可以添加锻造相关的界面
    this.drawText('锻造功能开发中...', 30, contentY + 50, {
      font: this.ui.fonts.normal,
      color: this.ui.colors.textSecondary
    });

    this.drawTabBar();
  }

  // 处理锻造页面触摸
  handleCraftingTouch(x, y) {
    this.handleTabBarTouch(x, y);
  }
}

// 导出适配器
module.exports = CompleteGameAdapter;
module.exports.default = CompleteGameAdapter;
