{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "bigPackageSizeSupport": true, "gameSubpackage": true, "minifyWXSS": true, "lazyCodeLoading": "requiredComponents"}, "compileType": "miniprogram", "libVersion": "", "appid": "touristappid", "projectname": "仗剑江湖行", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}