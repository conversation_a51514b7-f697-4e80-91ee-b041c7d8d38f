// 仗剑江湖行 - 小游戏构建脚本
// 直接从源码构建小游戏，不依赖 uni-app 的页面编译

const fs = require('fs');
const path = require('path');

console.log('开始构建小游戏...');

const buildDir = 'unpackage/dist/dev/mp-weixin';

// 清理构建目录
if (fs.existsSync(buildDir)) {
  fs.rmSync(buildDir, { recursive: true, force: true });
}

// 创建构建目录
fs.mkdirSync(buildDir, { recursive: true });
fs.mkdirSync(path.join(buildDir, 'utils'), { recursive: true });

console.log('构建目录已创建');

// 复制核心文件
const filesToCopy = [
  { src: 'game.json', dest: 'game.json' },
  { src: 'game.js', dest: 'game.js' },
  { src: 'mini-game-patch.js', dest: 'mini-game-patch.js' },
  { src: 'miniGameAdapter.js', dest: 'miniGameAdapter.js' },
  { src: 'miniGameWebSocket.js', dest: 'miniGameWebSocket.js' },
  { src: 'utils/gameData.js', dest: 'utils/gameData.js' },
  { src: 'utils/gameState.js', dest: 'utils/gameState.js' },
  { src: 'utils/websocket.js', dest: 'utils/websocket.js' }
];

filesToCopy.forEach(file => {
  const srcPath = file.src;
  const destPath = path.join(buildDir, file.dest);
  
  if (fs.existsSync(srcPath)) {
    fs.copyFileSync(srcPath, destPath);
    console.log(`✓ 已复制: ${file.src}`);
  } else {
    console.log(`⚠️  文件不存在: ${file.src}`);
  }
});

// 创建 app.js
const appJsContent = `// 仗剑江湖行 - 小游戏主入口
// 引入兼容性补丁
require('./mini-game-patch.js');

// 引入游戏主逻辑
require('./game.js');

console.log('小游戏 app.js 加载完成');
`;

fs.writeFileSync(path.join(buildDir, 'app.js'), appJsContent);
console.log('✓ 已创建: app.js');

// 创建 project.config.json
const projectConfig = {
  "description": "仗剑江湖行小游戏 - 完整功能版本",
  "packOptions": {
    "ignore": []
  },
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "useIsolateContext": true,
    "userConfirmedBundleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true,
    "disableUseStrict": false,
    "minifyWXML": true,
    "showES6CompileOption": false,
    "useCompilerPlugins": false
  },
  "compileType": "miniprogram",
  "libVersion": "2.19.4",
  "appid": "wxfb9c395829d83b91",
  "projectname": "仗剑江湖行",
  "debugOptions": {
    "hidedInDevtools": []
  },
  "scripts": {},
  "staticServerOptions": {
    "baseURL": "",
    "servePath": ""
  },
  "isGameTourist": false,
  "condition": {
    "search": {
      "list": []
    },
    "conversation": {
      "list": []
    },
    "game": {
      "list": []
    },
    "plugin": {
      "list": []
    },
    "gamePlugin": {
      "list": []
    },
    "miniprogram": {
      "list": []
    }
  }
};

fs.writeFileSync(path.join(buildDir, 'project.config.json'), JSON.stringify(projectConfig, null, 2));
console.log('✓ 已创建: project.config.json');

console.log('\n========================================');
console.log('         小游戏构建完成！');
console.log('========================================');
console.log(`\n📁 小游戏目录：${path.resolve(buildDir)}`);
console.log('\n🎮 在微信开发者工具中导入：');
console.log('1. 打开微信开发者工具');
console.log('2. 选择"小游戏"项目类型');
console.log(`3. 项目目录：${path.resolve(buildDir)}`);
console.log('4. AppID：wxfb9c395829d83b91');
console.log('5. 点击"导入"');
console.log('\n✨ 所有原有功能都已保留并适配到小游戏环境！');
