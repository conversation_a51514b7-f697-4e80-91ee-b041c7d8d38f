@echo off
chcp 65001 >nul
echo ========================================
echo        Mini-Game Patch Verification
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo ERROR: Build directory not found!
    echo Please build the project first using HBuilderX
    pause
    exit /b 1
)

echo OK: Build directory exists

REM Check game.json
if exist "%SOURCE_DIR%\game.json" (
    echo OK: game.json file exists
) else (
    echo ERROR: game.json file not found
)

REM Check game.js
if exist "%SOURCE_DIR%\game.js" (
    echo OK: game.js file exists
    
    findstr "Mini-game" "%SOURCE_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js contains compatibility patch
    ) else (
        echo ERROR: game.js does not contain compatibility patch
    )

    findstr "canIUse" "%SOURCE_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js contains wx.canIUse patch
    ) else (
        echo ERROR: game.js does not contain wx.canIUse patch
    )

    findstr "globalObj.global" "%SOURCE_DIR%\game.js" >nul
    if %errorlevel% equ 0 (
        echo OK: game.js contains global object patch
    ) else (
        echo ERROR: game.js does not contain global object patch
    )
) else (
    echo ERROR: game.js file not found
)

REM Check app.js
if exist "%SOURCE_DIR%\app.js" (
    echo OK: app.js file exists
    
    findstr "Mini-game" "%SOURCE_DIR%\app.js" >nul
    if %errorlevel% equ 0 (
        echo OK: app.js contains compatibility patch
    ) else (
        echo ERROR: app.js does not contain compatibility patch
    )
) else (
    echo ERROR: app.js file not found
)

REM Check vendor.js
if exist "%SOURCE_DIR%\common\vendor.js" (
    echo OK: vendor.js file exists
    
    findstr /C:"wx$2.canIUse" "%SOURCE_DIR%\common\vendor.js" >nul
    if !errorlevel! equ 0 (
        echo WARNING: vendor.js contains wx$2.canIUse calls - patches needed
    ) else (
        echo OK: vendor.js does not contain problematic code
    )
) else (
    echo ERROR: vendor.js file not found
)

echo.
echo ========================================
echo Verification Complete
echo ========================================
echo.
echo If all checks pass, you can import in WeChat DevTools:
echo 1. Select 'Mini Game' project type
echo 2. Import directory: %cd%\%SOURCE_DIR%
echo 3. Check console for 'compatibility patch applied' logs
echo.
pause
