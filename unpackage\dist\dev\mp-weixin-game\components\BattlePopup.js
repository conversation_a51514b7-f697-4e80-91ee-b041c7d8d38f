"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "BattlePopup",
  props: {
    visible: <PERSON><PERSON><PERSON>,
    battleLog: Array,
    // [{round, desc, effect_desc, ...}]
    player: Object,
    monster: Object,
    attackMode: {
      type: String,
      // 'active' or 'passive'
      default: "active"
    },
    battleStage: {
      type: String,
      // 'encounter' | 'battle' | 'end'
      default: "battle"
    }
  },
  data() {
    return {
      defaultPlayerAvatar: "/static/npc/default.png",
      defaultMonsterAvatar: "/static/npc/default.png",
      debug: false,
      // 关闭调试模式
      scrollTop: 0,
      // 滚动位置
      scrollIntoView: ""
      // 滚动到指定元素
    };
  },
  computed: {
    playerHpPercent() {
      if (!this.player)
        return 100;
      return this.player.hp && this.player.max_hp ? this.player.hp / this.player.max_hp * 100 : 100;
    },
    playerMpPercent() {
      if (!this.player)
        return 100;
      return this.player.mp && this.player.max_mp ? this.player.mp / this.player.max_mp * 100 : 100;
    },
    monsterHpPercent() {
      if (!this.monster)
        return 100;
      return this.monster.hp && this.monster.max_hp ? this.monster.hp / this.monster.max_hp * 100 : 100;
    },
    processedBattleLog() {
      if (!this.battleLog || !Array.isArray(this.battleLog)) {
        return [];
      }
      return this.battleLog.map((round, idx) => {
        const defaultTime = this.getCurrentTime();
        const formattedDesc = this.formatBattleDesc(round.desc);
        const formattedEffectDesc = round.effect_desc && round.effect_desc !== "" ? this.formatBattleDesc(round.effect_desc) : null;
        return {
          ...round,
          defaultTime,
          formattedDesc,
          formattedEffectDesc
        };
      });
    }
  },
  watch: {
    battleLog: {
      handler(newVal) {
        this.scrollToBottom();
      },
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    onClose() {
      this.$emit("close");
    },
    onNext() {
      this.$emit("next");
    },
    onAttack() {
      this.$emit("attack");
    },
    onEscape() {
      this.$emit("escape");
    },
    formatBattleDesc(desc) {
      if (!desc || desc === "") {
        return "";
      }
      let formatted = desc.replace(/【([^】]+)】/g, '<span style="color: #e74c3c; font-weight: bold; text-shadow: 0 0 2px rgba(231, 76, 60, 0.3);">【$1】</span>');
      formatted = formatted.replace(/(\d+)(?=点伤害)/g, '<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>');
      formatted = formatted.replace(/(\d+)(?=点)/g, '<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>');
      formatted = formatted.replace(/(眩晕|中毒|流血)/g, '<span style="color: #9b59b6; font-weight: bold; text-shadow: 0 0 2px rgba(155, 89, 182, 0.3);">$1</span>');
      return formatted;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollIntoView = "battle-log-bottom";
        setTimeout(() => {
          this.scrollIntoView = "";
        }, 100);
      });
    },
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      const seconds = now.getSeconds().toString().padStart(2, "0");
      return `${hours}:${minutes}:${seconds}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? common_vendor.e({
    b: $props.player.avatar || $data.defaultPlayerAvatar,
    c: common_vendor.t($props.player.name || "玩家"),
    d: $options.playerHpPercent,
    e: common_vendor.t(Math.floor($props.player.hp)),
    f: common_vendor.t(Math.floor($props.player.max_hp)),
    g: $options.playerMpPercent,
    h: common_vendor.t($props.player.mp),
    i: common_vendor.t($props.player.max_mp),
    j: $props.monster.avatar || $data.defaultMonsterAvatar,
    k: common_vendor.t($props.monster.name || "怪物"),
    l: $options.monsterHpPercent,
    m: common_vendor.t(Math.floor($props.monster.hp)),
    n: common_vendor.t(Math.floor($props.monster.max_hp)),
    o: $props.battleLog && $props.battleLog.length > 0
  }, $props.battleLog && $props.battleLog.length > 0 ? {
    p: common_vendor.f($options.processedBattleLog, (round, idx, i0) => {
      return common_vendor.e({
        a: common_vendor.t(round.round || idx + 1),
        b: common_vendor.t(round.timestamp || round.defaultTime),
        c: round.formattedDesc,
        d: round.formattedEffectDesc
      }, round.formattedEffectDesc ? {
        e: round.formattedEffectDesc
      } : {}, {
        f: `round-${idx}`
      });
    })
  } : {}, {
    q: $data.scrollTop,
    r: $data.scrollIntoView,
    s: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    t: $props.battleStage === "encounter" && $props.attackMode === "passive"
  }, $props.battleStage === "encounter" && $props.attackMode === "passive" ? {
    v: common_vendor.o(($event) => _ctx.$emit("attack")),
    w: common_vendor.o(($event) => _ctx.$emit("escape"))
  } : $props.battleStage === "encounter" && $props.attackMode === "active" ? {
    y: common_vendor.o(($event) => _ctx.$emit("escape"))
  } : $props.battleStage === "battle" ? {
    A: common_vendor.o(($event) => _ctx.$emit("escape"))
  } : $props.battleStage === "end" ? {
    C: common_vendor.o(($event) => _ctx.$emit("close"))
  } : {}, {
    x: $props.battleStage === "encounter" && $props.attackMode === "active",
    z: $props.battleStage === "battle",
    B: $props.battleStage === "end"
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-66bb1315"]]);
wx.createComponent(Component);
