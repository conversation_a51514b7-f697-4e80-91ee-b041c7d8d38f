/**
 * 游戏渲染器 - 负责所有场景的渲染
 */

// UI配置
const uiConfig = {
  colors: {
    primary: '#2c3e50',
    secondary: '#3498db',
    success: '#27ae60',
    warning: '#f39c12',
    danger: '#e74c3c',
    white: '#ffffff',
    text: '#2c3e50',
    textSecondary: '#7f8c8d',
    background: '#ecf0f1',
    border: '#bdc3c7'
  },
  fonts: {
    title: 'bold 32px Arial',
    large: 'bold 24px Arial',
    normal: '18px Arial',
    small: '14px Arial',
    tiny: '12px Arial'
  }
};

// 渲染工具方法
const RenderUtils = {
  // 绘制文本
  drawText: function(ctx, text, x, y, options = {}) {
    const {
      font = uiConfig.fonts.normal,
      color = uiConfig.colors.text,
      align = 'left',
      baseline = 'top',
      maxWidth = null
    } = options;
    
    ctx.font = font;
    ctx.fillStyle = color;
    ctx.textAlign = align;
    ctx.textBaseline = baseline;
    
    if (maxWidth) {
      ctx.fillText(text, x, y, maxWidth);
    } else {
      ctx.fillText(text, x, y);
    }
  },
  
  // 绘制按钮
  drawButton: function(ctx, text, x, y, width, height, options = {}) {
    const {
      backgroundColor = uiConfig.colors.primary,
      textColor = uiConfig.colors.white,
      font = uiConfig.fonts.normal,
      border = false,
      borderColor = uiConfig.colors.border,
      disabled = false
    } = options;
    
    const bgColor = disabled ? uiConfig.colors.textSecondary : backgroundColor;
    const txtColor = disabled ? uiConfig.colors.white : textColor;
    
    // 绘制按钮背景
    ctx.fillStyle = bgColor;
    ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    if (border) {
      ctx.strokeStyle = borderColor;
      ctx.lineWidth = 1;
      ctx.strokeRect(x, y, width, height);
    }
    
    // 绘制按钮文本
    this.drawText(ctx, text, x + width / 2, y + height / 2, {
      font,
      color: txtColor,
      align: 'center',
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  },
  
  // 绘制输入框
  drawInputBox: function(ctx, x, y, width, height, value, placeholder, options = {}) {
    const {
      backgroundColor = uiConfig.colors.white,
      borderColor = uiConfig.colors.border,
      textColor = uiConfig.colors.text,
      placeholderColor = uiConfig.colors.textSecondary,
      font = uiConfig.fonts.normal,
      focused = false
    } = options;
    
    // 绘制背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    ctx.strokeStyle = focused ? uiConfig.colors.primary : borderColor;
    ctx.lineWidth = focused ? 2 : 1;
    ctx.strokeRect(x, y, width, height);
    
    // 绘制文本或占位符
    const displayText = value || placeholder;
    const color = value ? textColor : placeholderColor;
    
    this.drawText(ctx, displayText, x + 10, y + height / 2, {
      font,
      color,
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  },
  
  // 绘制卡片
  drawCard: function(ctx, x, y, width, height, options = {}) {
    const {
      backgroundColor = uiConfig.colors.white,
      borderColor = uiConfig.colors.border,
      shadow = true
    } = options;
    
    // 绘制阴影
    if (shadow) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.fillRect(x + 2, y + 2, width, height);
    }
    
    // 绘制卡片背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    ctx.strokeStyle = borderColor;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);
    
    return { x, y, width, height };
  },
  
  // 绘制进度条
  drawProgressBar: function(ctx, x, y, width, height, progress, options = {}) {
    const {
      backgroundColor = uiConfig.colors.border,
      progressColor = uiConfig.colors.success,
      showText = true,
      text = null
    } = options;
    
    // 绘制背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(x, y, width, height);
    
    // 绘制进度
    const progressWidth = width * Math.min(Math.max(progress, 0), 1);
    ctx.fillStyle = progressColor;
    ctx.fillRect(x, y, progressWidth, height);
    
    // 绘制文本
    if (showText) {
      const displayText = text || Math.round(progress * 100) + '%';
      this.drawText(ctx, displayText, x + width / 2, y + height / 2, {
        font: uiConfig.fonts.small,
        color: uiConfig.colors.text,
        align: 'center',
        baseline: 'middle'
      });
    }
  },
  
  // 绘制头部导航
  drawHeader: function(ctx, canvas, title, options = {}) {
    const {
      height = 60,
      backgroundColor = uiConfig.colors.primary,
      textColor = uiConfig.colors.white,
      showBack = false
    } = options;
    
    // 绘制背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvas.width, height);
    
    // 绘制标题
    this.drawText(ctx, title, canvas.width / 2, height / 2, {
      font: uiConfig.fonts.large,
      color: textColor,
      align: 'center',
      baseline: 'middle'
    });
    
    // 绘制返回按钮
    let backButton = null;
    if (showBack) {
      backButton = this.drawButton(ctx, '←', 10, 10, 40, 40, {
        backgroundColor: 'transparent',
        textColor: textColor,
        font: uiConfig.fonts.large
      });
    }
    
    return { backButton };
  },
  
  // 绘制底部导航栏
  drawTabBar: function(ctx, canvas, tabs, currentTab) {
    const tabBarHeight = 80;
    const tabBarY = canvas.height - tabBarHeight;
    const tabWidth = canvas.width / tabs.length;
    
    // 绘制背景
    ctx.fillStyle = uiConfig.colors.white;
    ctx.fillRect(0, tabBarY, canvas.width, tabBarHeight);
    
    // 绘制顶部分割线
    ctx.strokeStyle = uiConfig.colors.border;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, tabBarY);
    ctx.lineTo(canvas.width, tabBarY);
    ctx.stroke();
    
    const tabButtons = [];
    
    tabs.forEach((tab, index) => {
      const x = index * tabWidth;
      const isActive = currentTab === tab.key;
      
      // 绘制标签文本
      this.drawText(ctx, tab.name, x + tabWidth / 2, tabBarY + 40, {
        font: uiConfig.fonts.normal,
        color: isActive ? uiConfig.colors.primary : uiConfig.colors.textSecondary,
        align: 'center',
        baseline: 'middle'
      });
      
      // 如果是活跃标签，绘制指示器
      if (isActive) {
        ctx.fillStyle = uiConfig.colors.primary;
        ctx.fillRect(x + tabWidth / 2 - 20, tabBarY + 55, 40, 3);
      }
      
      tabButtons.push({
        x: x,
        y: tabBarY,
        width: tabWidth,
        height: tabBarHeight,
        key: tab.key
      });
    });
    
    return tabButtons;
  },
  
  // 检查点是否在矩形内
  isPointInRect: function(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  },
  
  // 清空画布
  clear: function(ctx, canvas, color = uiConfig.colors.background) {
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  },
  
  // 绘制渐变背景
  drawGradientBackground: function(ctx, canvas, startColor = '#667eea', endColor = '#764ba2') {
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, startColor);
    gradient.addColorStop(1, endColor);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }
};

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { RenderUtils, uiConfig };
} else {
  window.RenderUtils = RenderUtils;
  window.uiConfig = uiConfig;
}
