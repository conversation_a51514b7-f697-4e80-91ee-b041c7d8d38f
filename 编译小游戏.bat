@echo off
chcp 65001 >nul
echo ========================================
echo      仗剑江湖行 - 小游戏编译工具
echo ========================================
echo.

echo [1/5] 准备编译环境...
set "SOURCE_DIR=%cd%"
set "BUILD_DIR=unpackage\dist\dev\mp-weixin-game"

echo 源码目录: %SOURCE_DIR%
echo 输出目录: %BUILD_DIR%
echo.

echo [2/5] 清理输出目录...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
    echo ✓ 已清理旧的编译文件
)

mkdir "%BUILD_DIR%" 2>nul
mkdir "%BUILD_DIR%\utils" 2>nul
echo ✓ 输出目录已创建
echo.

echo [3/5] 使用 HBuilderX 编译基础文件...
echo 请在 HBuilderX 中执行：运行 ^> 运行到小程序模拟器 ^> 微信开发者工具
echo 等待编译完成后按任意键继续...
pause >nul

echo.
echo [4/5] 复制小游戏专用文件...

REM 复制小游戏配置文件
if exist "game.json" (
    copy "game.json" "%BUILD_DIR%\game.json" >nul
    echo ✓ 已复制: game.json
) else (
    echo ❌ 缺少: game.json
)

REM 复制小游戏入口文件
if exist "game.js" (
    copy "game.js" "%BUILD_DIR%\game.js" >nul
    echo ✓ 已复制: game.js
) else (
    echo ❌ 缺少: game.js
)

REM 复制兼容性补丁
if exist "mini-game-patch.js" (
    copy "mini-game-patch.js" "%BUILD_DIR%\mini-game-patch.js" >nul
    echo ✓ 已复制: mini-game-patch.js
) else (
    echo ❌ 缺少: mini-game-patch.js
)

REM 复制游戏适配器
if exist "miniGameAdapter.js" (
    copy "miniGameAdapter.js" "%BUILD_DIR%\miniGameAdapter.js" >nul
    echo ✓ 已复制: miniGameAdapter.js
) else (
    echo ❌ 缺少: miniGameAdapter.js
)

REM 复制页面适配器
if exist "gamePageAdapter.js" (
    copy "gamePageAdapter.js" "%BUILD_DIR%\gamePageAdapter.js" >nul
    echo ✓ 已复制: gamePageAdapter.js
) else (
    echo ❌ 缺少: gamePageAdapter.js
)

REM 复制小游戏WebSocket管理器
if exist "miniGameWebSocket.js" (
    copy "miniGameWebSocket.js" "%BUILD_DIR%\miniGameWebSocket.js" >nul
    echo ✓ 已复制: miniGameWebSocket.js
) else (
    echo ❌ 缺少: miniGameWebSocket.js
)

echo.
echo [5/5] 创建小游戏专用的 app.js...

REM 创建小游戏版本的 app.js
echo // 仗剑江湖行 - 小游戏主入口 > "%BUILD_DIR%\app.js"
echo // 引入兼容性补丁 >> "%BUILD_DIR%\app.js"
echo require('./mini-game-patch.js'); >> "%BUILD_DIR%\app.js"
echo. >> "%BUILD_DIR%\app.js"
echo // 引入游戏主逻辑 >> "%BUILD_DIR%\app.js"
echo require('./game.js'); >> "%BUILD_DIR%\app.js"
echo. >> "%BUILD_DIR%\app.js"
echo console.log('小游戏 app.js 加载完成'); >> "%BUILD_DIR%\app.js"

echo ✓ 已创建: app.js

echo.
echo [6/6] 验证编译结果...

set "MISSING_FILES="

if not exist "%BUILD_DIR%\game.json" set "MISSING_FILES=%MISSING_FILES% game.json"
if not exist "%BUILD_DIR%\game.js" set "MISSING_FILES=%MISSING_FILES% game.js"
if not exist "%BUILD_DIR%\app.js" set "MISSING_FILES=%MISSING_FILES% app.js"

if not "%MISSING_FILES%"=="" (
    echo ❌ 编译失败，缺少文件：%MISSING_FILES%
    echo.
    echo 请检查：
    echo 1. 确保所有源文件存在
    echo 2. HBuilderX 编译是否成功
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo         小游戏编译完成！
echo ========================================
echo.
echo 📁 小游戏目录：%cd%\%BUILD_DIR%
echo.
echo 🎮 在微信开发者工具中导入：
echo 1. 打开微信开发者工具
echo 2. 选择"小游戏"项目类型
echo 3. 项目目录：%cd%\%BUILD_DIR%
echo 4. AppID：wxfb9c395829d83b91
echo 5. 点击"导入"
echo.
echo ✨ 所有功能都已适配到小游戏环境！
echo.

pause
