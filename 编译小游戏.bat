@echo off
echo ========================================
echo      Game Compilation Tool
echo ========================================
echo.

echo [1/5] Preparing build environment...
set "SOURCE_DIR=%cd%"
set "BUILD_DIR=unpackage\dist\dev\mp-weixin-game"

echo Source directory: %SOURCE_DIR%
echo Output directory: %BUILD_DIR%
echo.

echo [2/5] Cleaning output directory...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
    echo Success: Cleaned old build files
)

mkdir "%BUILD_DIR%" 2>nul
mkdir "%BUILD_DIR%\utils" 2>nul
echo Success: Output directory created
echo.

echo [3/5] Compile with HBuilderX...
echo Please run in HBuilderX: Run ^> Run to Mini Program Simulator ^> WeChat Developer Tools
echo Press any key to continue after compilation...
pause >nul

echo.
echo [4/5] Copying mini-game specific files...

REM Copy game configuration file
if exist "game.json" (
    copy "game.json" "%BUILD_DIR%\game.json" >nul
    echo Success: Copied game.json
) else (
    echo Error: Missing game.json
)

REM Copy game entry file
if exist "game.js" (
    copy "game.js" "%BUILD_DIR%\game.js" >nul
    echo Success: Copied game.js
) else (
    echo Error: Missing game.js
)

REM Copy compatibility patch
if exist "mini-game-patch.js" (
    copy "mini-game-patch.js" "%BUILD_DIR%\mini-game-patch.js" >nul
    echo Success: Copied mini-game-patch.js
) else (
    echo Error: Missing mini-game-patch.js
)

REM Copy game adapter
if exist "miniGameAdapter.js" (
    copy "miniGameAdapter.js" "%BUILD_DIR%\miniGameAdapter.js" >nul
    echo Success: Copied miniGameAdapter.js
) else (
    echo Error: Missing miniGameAdapter.js
)

REM Copy page adapter
if exist "gamePageAdapter.js" (
    copy "gamePageAdapter.js" "%BUILD_DIR%\gamePageAdapter.js" >nul
    echo Success: Copied gamePageAdapter.js
) else (
    echo Error: Missing gamePageAdapter.js
)

REM Copy mini-game WebSocket manager
if exist "miniGameWebSocket.js" (
    copy "miniGameWebSocket.js" "%BUILD_DIR%\miniGameWebSocket.js" >nul
    echo Success: Copied miniGameWebSocket.js
) else (
    echo Error: Missing miniGameWebSocket.js
)

echo.
echo [5/5] Creating mini-game app.js...

REM Create mini-game version of app.js
echo // Mini-game main entry > "%BUILD_DIR%\app.js"
echo // Import compatibility patch >> "%BUILD_DIR%\app.js"
echo require('./mini-game-patch.js'); >> "%BUILD_DIR%\app.js"
echo. >> "%BUILD_DIR%\app.js"
echo // Import game main logic >> "%BUILD_DIR%\app.js"
echo require('./game.js'); >> "%BUILD_DIR%\app.js"
echo. >> "%BUILD_DIR%\app.js"
echo console.log('Mini-game app.js loaded'); >> "%BUILD_DIR%\app.js"

echo Success: Created app.js

echo.
echo [6/6] Verifying build results...

set "MISSING_FILES="

if not exist "%BUILD_DIR%\game.json" set "MISSING_FILES=%MISSING_FILES% game.json"
if not exist "%BUILD_DIR%\game.js" set "MISSING_FILES=%MISSING_FILES% game.js"
if not exist "%BUILD_DIR%\app.js" set "MISSING_FILES=%MISSING_FILES% app.js"

if not "%MISSING_FILES%"=="" (
    echo Error: Build failed, missing files: %MISSING_FILES%
    echo.
    echo Please check:
    echo 1. Ensure all source files exist
    echo 2. HBuilderX compilation succeeded
    echo 3. Re-run this script
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo         Mini-game Build Complete!
echo ========================================
echo.
echo Mini-game directory: %cd%\%BUILD_DIR%
echo.
echo Import in WeChat Developer Tools:
echo 1. Open WeChat Developer Tools
echo 2. Select "Mini-game" project type
echo 3. Project directory: %cd%\%BUILD_DIR%
echo 4. AppID: wxfb9c395829d83b91
echo 5. Click "Import"
echo.
echo All functions have been adapted to mini-game environment!
echo.

pause
