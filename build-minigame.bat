@echo off
echo ========================================
echo        Mini-Game Build Script
echo ========================================
echo.

echo Step 1: Clean build cache...
if exist "unpackage" (
    rmdir /s /q "unpackage"
    echo Build cache cleaned
) else (
    echo Build cache already clean
)

echo.
echo Step 2: Check source files...

if exist "manifest.json" (
    echo OK: manifest.json exists
) else (
    echo ERROR: manifest.json not found
    pause
    exit /b 1
)

if exist "pages.json" (
    echo OK: pages.json exists
) else (
    echo ERROR: pages.json not found
    pause
    exit /b 1
)

if exist "main.js" (
    echo OK: main.js exists
) else (
    echo ERROR: main.js not found
    pause
    exit /b 1
)

if exist "mini-game-patch.js" (
    echo OK: mini-game-patch.js exists
) else (
    echo ERROR: mini-game-patch.js not found
    pause
    exit /b 1
)

echo.
echo Step 3: Verify source configuration...

findstr "gamePlugin" "manifest.json" >nul
if %errorlevel% equ 0 (
    echo OK: manifest.json contains mini-game config
) else (
    echo WARNING: manifest.json missing mini-game config
)

findstr "pages/index/index" "pages.json" >nul
if %errorlevel% equ 0 (
    echo OK: pages.json contains index page config
) else (
    echo WARNING: pages.json missing index page config
)

findstr "mini-game-patch" "main.js" >nul
if %errorlevel% equ 0 (
    echo OK: main.js contains patch import
) else (
    echo WARNING: main.js missing patch import
)

echo.
echo Step 4: Please compile the project manually...
echo.
echo Follow these steps:
echo 1. Open HBuilderX
echo 2. Open project: %cd%
echo 3. Click: Run -^> Run to Mini Program Simulator -^> WeChat DevTools
echo 4. Wait for compilation to complete
echo 5. After compilation, press any key to continue...
echo.
pause

echo.
echo Step 5: Check build results...

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%BUILD_DIR%" (
    echo ERROR: Build directory not found, please compile first
    pause
    exit /b 1
)

echo OK: Build directory exists

if exist "%BUILD_DIR%\app.js" (
    echo OK: app.js generated
) else (
    echo ERROR: app.js not generated
)

if exist "%BUILD_DIR%\app.json" (
    echo OK: app.json generated
) else (
    echo ERROR: app.json not generated
)

if exist "%BUILD_DIR%\common\vendor.js" (
    echo OK: vendor.js generated
) else (
    echo ERROR: vendor.js not generated
)

echo.
echo Step 6: Apply mini-game patches...

echo Creating game.json...
echo {> "%BUILD_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%BUILD_DIR%\game.json"
echo   "showStatusBar": false,>> "%BUILD_DIR%\game.json"
echo   "networkTimeout": {>> "%BUILD_DIR%\game.json"
echo     "request": 60000,>> "%BUILD_DIR%\game.json"
echo     "connectSocket": 60000,>> "%BUILD_DIR%\game.json"
echo     "uploadFile": 60000,>> "%BUILD_DIR%\game.json"
echo     "downloadFile": 60000>> "%BUILD_DIR%\game.json"
echo   },>> "%BUILD_DIR%\game.json"
echo   "subpackages": [],>> "%BUILD_DIR%\game.json"
echo   "plugins": {},>> "%BUILD_DIR%\game.json"
echo   "preloadRule": {},>> "%BUILD_DIR%\game.json"
echo   "resizable": false>> "%BUILD_DIR%\game.json"
echo }>> "%BUILD_DIR%\game.json"

if exist "%BUILD_DIR%\app.js" (
    copy "%BUILD_DIR%\app.js" "%BUILD_DIR%\game.js" >nul
    echo OK: game.js created
)

echo.
echo Step 7: Verify final results...

if exist "%BUILD_DIR%\game.json" (
    echo OK: game.json exists
) else (
    echo ERROR: game.json not found
)

if exist "%BUILD_DIR%\game.js" (
    echo OK: game.js exists
) else (
    echo ERROR: game.js not found
)

findstr "mini-game" "%BUILD_DIR%\app.js" >nul
if %errorlevel% equ 0 (
    echo OK: Build contains mini-game patches
) else (
    echo WARNING: Build missing mini-game patches
)

echo.
echo ========================================
echo Mini-Game Build Complete!
echo ========================================
echo.
echo Now you can import in WeChat DevTools:
echo 1. Select "Mini Game" project type
echo 2. Import directory: %cd%\%BUILD_DIR%
echo 3. Enter your Mini Game AppID
echo 4. Check console logs to verify patches
echo.
pause
