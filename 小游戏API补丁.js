// 小游戏 API 兼容性补丁
// 解决 wx.canIUse is not a function 错误

(function() {
  'use strict';
  
  // 检查是否在小游戏环境中
  if (typeof wx !== 'undefined' && !wx.canIUse) {
    console.log('检测到小游戏环境，应用 API 兼容性补丁');
    
    // 为小游戏环境添加 canIUse 方法
    wx.canIUse = function(apiName) {
      // 小游戏中常用的 API 列表
      const gameAPIs = [
        'getSystemInfoSync',
        'getSystemInfo',
        'getAppBaseInfo',
        'getWindowInfo', 
        'getDeviceInfo',
        'getSystemSetting',
        'getAppAuthorizeSetting',
        'request',
        'connectSocket',
        'onSocketOpen',
        'onSocketClose',
        'onSocketMessage',
        'onSocketError',
        'sendSocketMessage',
        'closeSocket',
        'showToast',
        'showModal',
        'showLoading',
        'hideLoading',
        'setStorage',
        'getStorage',
        'removeStorage',
        'clearStorage',
        'setStorageSync',
        'getStorageSync',
        'removeStorageSync',
        'clearStorageSync',
        'onShow',
        'onHide',
        'offShow',
        'offHide',
        'exitMiniProgram',
        'navigateToMiniProgram',
        'getUpdateManager',
        'createInnerAudioContext',
        'getBackgroundAudioManager',
        'createVideoContext',
        'createCameraContext',
        'createLivePlayerContext',
        'createMapContext',
        'createCanvasContext',
        'canvasToTempFilePath',
        'canvasPutImageData',
        'canvasGetImageData',
        'downloadFile',
        'uploadFile',
        'createDownloadTask',
        'createUploadTask'
      ];
      
      // 检查 API 是否存在
      if (gameAPIs.includes(apiName)) {
        return typeof wx[apiName] === 'function';
      }
      
      // 对于不在列表中的 API，直接检查是否存在
      return typeof wx[apiName] !== 'undefined';
    };
    
    // 确保基础 API 存在
    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
    }
    
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
    }
    
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
    }
    
    console.log('小游戏 API 兼容性补丁应用完成');
  }
})();
