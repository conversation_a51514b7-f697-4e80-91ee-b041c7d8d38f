@echo off
echo ========================================
echo      Clean Mini-game Build Tool
echo ========================================
echo.

echo Building pure mini-game (no wxss/wxml files)...

set TARGET_DIR=unpackage\dist\dev\mp-weixin-game

echo Cleaning target directory...
if exist "%TARGET_DIR%" rmdir /s /q "%TARGET_DIR%"
mkdir "%TARGET_DIR%"

echo Creating mini-game structure...

REM 1. Copy game.json configuration
echo Copying game.json...
if exist "game.json" (
    copy game.json "%TARGET_DIR%\game.json" >nul 2>&1
    echo   ✓ game.json copied
) else (
    echo   ✗ game.json not found, creating default...
    echo {> "%TARGET_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%TARGET_DIR%\game.json"
    echo   "showStatusBar": false,>> "%TARGET_DIR%\game.json"
    echo   "networkTimeout": {>> "%TARGET_DIR%\game.json"
    echo     "request": 10000,>> "%TARGET_DIR%\game.json"
    echo     "connectSocket": 10000,>> "%TARGET_DIR%\game.json"
    echo     "uploadFile": 10000,>> "%TARGET_DIR%\game.json"
    echo     "downloadFile": 10000>> "%TARGET_DIR%\game.json"
    echo   }>> "%TARGET_DIR%\game.json"
    echo }>> "%TARGET_DIR%\game.json"
)

REM 2. Copy main game file
echo Copying main game file...
if exist "game-debug.js" (
    echo   Using debug version for troubleshooting...
    copy game-debug.js "%TARGET_DIR%\game.js" >nul 2>&1
    echo   ✓ game-debug.js → game.js
) else if exist "game-simple.js" (
    echo   Using simplified version...
    copy game-simple.js "%TARGET_DIR%\game.js" >nul 2>&1
    echo   ✓ game-simple.js → game.js
) else if exist "game.js" (
    echo   Using standard version...
    copy game.js "%TARGET_DIR%\game.js" >nul 2>&1
    echo   ✓ game.js copied
) else (
    echo   ✗ No game.js file found!
    echo   Please ensure you have game.js, game-simple.js, or game-debug.js
    pause
    exit /b 1
)

REM 3. Copy optional engine files (only .js files)
echo Copying engine files...
if exist "gameEngine.js" (
    copy gameEngine.js "%TARGET_DIR%\gameEngine.js" >nul 2>&1
    echo   ✓ gameEngine.js
)
if exist "uiManager.js" (
    copy uiManager.js "%TARGET_DIR%\uiManager.js" >nul 2>&1
    echo   ✓ uiManager.js
)
if exist "inputManager.js" (
    copy inputManager.js "%TARGET_DIR%\inputManager.js" >nul 2>&1
    echo   ✓ inputManager.js
)
if exist "networkManager.js" (
    copy networkManager.js "%TARGET_DIR%\networkManager.js" >nul 2>&1
    echo   ✓ networkManager.js
)
if exist "sceneManager.js" (
    copy sceneManager.js "%TARGET_DIR%\sceneManager.js" >nul 2>&1
    echo   ✓ sceneManager.js
)
if exist "gameRenderer.js" (
    copy gameRenderer.js "%TARGET_DIR%\gameRenderer.js" >nul 2>&1
    echo   ✓ gameRenderer.js
)
if exist "gameScenes.js" (
    copy gameScenes.js "%TARGET_DIR%\gameScenes.js" >nul 2>&1
    echo   ✓ gameScenes.js
)

REM 4. Copy utils directory (only .js files)
echo Copying utils directory...
if exist "utils" (
    if not exist "%TARGET_DIR%\utils" mkdir "%TARGET_DIR%\utils"
    
    REM Copy only .js files from utils
    for %%f in (utils\*.js) do (
        copy "%%f" "%TARGET_DIR%\utils\" >nul 2>&1
        echo   ✓ %%f
    )
)

REM 5. Copy components directory (only .js files)
echo Copying components directory...
if exist "components" (
    if not exist "%TARGET_DIR%\components" mkdir "%TARGET_DIR%\components"
    
    REM Copy only .js files from components
    for %%f in (components\*.js) do (
        copy "%%f" "%TARGET_DIR%\components\" >nul 2>&1
        echo   ✓ %%f
    )
)

REM 6. Copy pages directory (only .js files)
echo Copying pages directory...
if exist "pages" (
    if not exist "%TARGET_DIR%\pages" mkdir "%TARGET_DIR%\pages"
    
    REM Create subdirectories and copy only .js files
    for /d %%d in (pages\*) do (
        if not exist "%TARGET_DIR%\%%d" mkdir "%TARGET_DIR%\%%d"
        for %%f in (%%d\*.js) do (
            copy "%%f" "%TARGET_DIR%\%%d\" >nul 2>&1
            echo   ✓ %%f
        )
    )
)

REM 7. Copy static resources (images, audio, etc.)
echo Copying static resources...
if exist "static" (
    if not exist "%TARGET_DIR%\static" mkdir "%TARGET_DIR%\static"
    xcopy "static\*" "%TARGET_DIR%\static\" /E /I /Y >nul 2>&1
    echo   ✓ static directory
)

if exist "images" (
    if not exist "%TARGET_DIR%\images" mkdir "%TARGET_DIR%\images"
    xcopy "images\*" "%TARGET_DIR%\images\" /E /I /Y >nul 2>&1
    echo   ✓ images directory
)

if exist "audio" (
    if not exist "%TARGET_DIR%\audio" mkdir "%TARGET_DIR%\audio"
    xcopy "audio\*" "%TARGET_DIR%\audio\" /E /I /Y >nul 2>&1
    echo   ✓ audio directory
)

REM 8. Create app.js entry point
echo Creating app.js entry point...
echo // Mini-game entry point > "%TARGET_DIR%\app.js"
echo // This file is required by WeChat Mini-game >> "%TARGET_DIR%\app.js"
echo console.log('Mini-game starting...'); >> "%TARGET_DIR%\app.js"
echo require('./game.js'); >> "%TARGET_DIR%\app.js"
echo console.log('Mini-game loaded successfully'); >> "%TARGET_DIR%\app.js"

REM 9. Verify critical files
echo.
echo Verifying mini-game structure...
if exist "%TARGET_DIR%\game.js" (
    echo   ✓ game.js exists
) else (
    echo   ✗ game.js missing!
)

if exist "%TARGET_DIR%\game.json" (
    echo   ✓ game.json exists
) else (
    echo   ✗ game.json missing!
)

if exist "%TARGET_DIR%\app.js" (
    echo   ✓ app.js exists
) else (
    echo   ✗ app.js missing!
)

REM 10. Check for unwanted files
echo.
echo Checking for unwanted files...
set UNWANTED_FOUND=0

for /r "%TARGET_DIR%" %%f in (*.wxss) do (
    echo   ✗ Found unwanted file: %%f
    set UNWANTED_FOUND=1
)

for /r "%TARGET_DIR%" %%f in (*.wxml) do (
    echo   ✗ Found unwanted file: %%f
    set UNWANTED_FOUND=1
)

for /r "%TARGET_DIR%" %%f in (*.vue) do (
    echo   ✗ Found unwanted file: %%f
    set UNWANTED_FOUND=1
)

if %UNWANTED_FOUND%==0 (
    echo   ✓ No unwanted files found
)

echo.
echo ========================================
echo         Clean Build Complete!
echo ========================================
echo.
echo Mini-game directory: %cd%\%TARGET_DIR%
echo.
echo Files included:
echo   - game.js (main logic)
echo   - game.json (configuration)
echo   - app.js (entry point)
echo   - *.js files (game modules)
echo   - static resources (images, audio)
echo.
echo Files excluded:
echo   - *.wxss (小程序样式文件)
echo   - *.wxml (小程序模板文件)
echo   - *.vue (Vue组件文件)
echo   - Other non-game files
echo.
echo To test in WeChat Developer Tools:
echo 1. Open WeChat Developer Tools
echo 2. Select "Mini-game" project type (NOT Mini-program)
echo 3. Import directory: %cd%\%TARGET_DIR%
echo 4. AppID: wxfb9c395829d83b91
echo.
pause
