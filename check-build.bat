@echo off
echo ========================================
echo        Check Build Status
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

echo Checking build directory...
if not exist "%BUILD_DIR%" (
    echo ERROR: Build directory not found
    echo Please compile the project in HBuilderX first
    echo.
    echo Steps:
    echo 1. Open HBuilderX
    echo 2. Open project: %cd%
    echo 3. Run -^> Run to Mini Program Simulator -^> WeChat DevTools
    echo.
    pause
    exit /b 1
)

echo OK: Build directory exists
echo.

echo Checking required files...
if exist "%BUILD_DIR%\app.js" (
    echo OK: app.js exists
) else (
    echo ERROR: app.js not found
)

if exist "%BUILD_DIR%\app.json" (
    echo OK: app.json exists
) else (
    echo ERROR: app.json not found
)

if exist "%BUILD_DIR%\common\vendor.js" (
    echo OK: vendor.js exists
) else (
    echo ERROR: vendor.js not found
)

if exist "%BUILD_DIR%\pages\index\index.js" (
    echo OK: index page exists
) else (
    echo ERROR: index page not found
)

if exist "%BUILD_DIR%\pages\login\login.js" (
    echo OK: login page exists
) else (
    echo ERROR: login page not found
)

echo.
echo Checking mini-game files...
if exist "%BUILD_DIR%\game.json" (
    echo OK: game.json exists
) else (
    echo WARNING: game.json not found - will create
    echo Creating game.json...
    echo {> "%BUILD_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%BUILD_DIR%\game.json"
    echo   "showStatusBar": false,>> "%BUILD_DIR%\game.json"
    echo   "networkTimeout": {>> "%BUILD_DIR%\game.json"
    echo     "request": 60000,>> "%BUILD_DIR%\game.json"
    echo     "connectSocket": 60000,>> "%BUILD_DIR%\game.json"
    echo     "uploadFile": 60000,>> "%BUILD_DIR%\game.json"
    echo     "downloadFile": 60000>> "%BUILD_DIR%\game.json"
    echo   },>> "%BUILD_DIR%\game.json"
    echo   "subpackages": [],>> "%BUILD_DIR%\game.json"
    echo   "plugins": {},>> "%BUILD_DIR%\game.json"
    echo   "preloadRule": {},>> "%BUILD_DIR%\game.json"
    echo   "resizable": false>> "%BUILD_DIR%\game.json"
    echo }>> "%BUILD_DIR%\game.json"
    echo OK: game.json created
)

if exist "%BUILD_DIR%\game.js" (
    echo OK: game.js exists
) else (
    echo WARNING: game.js not found - will create
    if exist "%BUILD_DIR%\app.js" (
        copy "%BUILD_DIR%\app.js" "%BUILD_DIR%\game.js" >nul
        echo OK: game.js created
    ) else (
        echo ERROR: Cannot create game.js - app.js not found
    )
)

echo.
echo Checking patches...
findstr "mini-game" "%BUILD_DIR%\app.js" >nul
if %errorlevel% equ 0 (
    echo OK: Mini-game patches found in app.js
) else (
    echo WARNING: Mini-game patches not found in app.js
    echo This might cause compatibility issues
)

echo.
echo ========================================
echo Build Check Complete
echo ========================================
echo.
echo Ready to import in WeChat DevTools:
echo 1. Select "Mini Game" project type
echo 2. Import directory: %cd%\%BUILD_DIR%
echo 3. Enter your Mini Game AppID
echo.
pause
