@echo off
chcp 65001 >nul
echo ========================================
echo      小游戏编译检查工具
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin-game"

echo [1/4] 检查编译输出目录...
if exist "%BUILD_DIR%" (
    echo ✓ 编译输出目录存在: %BUILD_DIR%
) else (
    echo ❌ 编译输出目录不存在: %BUILD_DIR%
    echo 请先在 HBuilderX 中编译项目为小游戏
    pause
    exit /b 1
)

echo.
echo [2/4] 检查必需的小游戏文件...

set "MISSING_FILES="

if not exist "%BUILD_DIR%\game.json" (
    set "MISSING_FILES=%MISSING_FILES% game.json"
)
if not exist "%BUILD_DIR%\game.js" (
    set "MISSING_FILES=%MISSING_FILES% game.js"
)
if not exist "%BUILD_DIR%\app.js" (
    set "MISSING_FILES=%MISSING_FILES% app.js"
)

if not "%MISSING_FILES%"=="" (
    echo ❌ 缺少以下关键文件：%MISSING_FILES%
    echo.
    echo 解决方案：
    echo 1. 确保 manifest.json 中配置了 mp-weixin-game
    echo 2. 确保 pages.json 中 pages 数组为空
    echo 3. 重新在 HBuilderX 中编译项目
    echo.
    pause
    exit /b 1
) else (
    echo ✓ 所有必需文件都存在
)

echo.
echo [3/4] 检查文件内容...

if exist "%BUILD_DIR%\game.json" (
    findstr /C:"deviceOrientation" "%BUILD_DIR%\game.json" >nul
    if %errorlevel% equ 0 (
        echo ✓ game.json 配置正确
    ) else (
        echo ⚠️  game.json 配置可能有问题
    )
)

if exist "%BUILD_DIR%\app.js" (
    findstr /C:"require" "%BUILD_DIR%\app.js" >nul
    if %errorlevel% equ 0 (
        echo ✓ app.js 包含必要的引用
    ) else (
        echo ⚠️  app.js 可能缺少必要的引用
    )
)

echo.
echo [4/4] 检查项目配置...

if exist "manifest.json" (
    findstr /C:"mp-weixin-game" "manifest.json" >nul
    if %errorlevel% equ 0 (
        echo ✓ manifest.json 配置为小游戏模式
    ) else (
        echo ❌ manifest.json 未配置为小游戏模式
        echo 请检查 manifest.json 中的 mp-weixin-game 配置
    )
)

if exist "pages.json" (
    findstr /C:"\"pages\": \[\]" "pages.json" >nul
    if %errorlevel% equ 0 (
        echo ✓ pages.json 配置正确（空页面数组）
    ) else (
        echo ⚠️  pages.json 可能包含页面配置，建议清空
    )
)

echo.
echo ========================================
echo           检查完成！
echo ========================================
echo.

if "%MISSING_FILES%"=="" (
    echo 🎮 小游戏编译检查通过！
    echo.
    echo 📁 小游戏目录：%cd%\%BUILD_DIR%
    echo.
    echo 🚀 在微信开发者工具中导入：
    echo 1. 打开微信开发者工具
    echo 2. 选择"小游戏"项目类型
    echo 3. 项目目录：%cd%\%BUILD_DIR%
    echo 4. AppID：wxfb9c395829d83b91
    echo 5. 点击"导入"
    echo.
) else (
    echo ❌ 小游戏编译检查失败！
    echo 请按照上述提示修复问题后重新编译。
    echo.
)

pause
