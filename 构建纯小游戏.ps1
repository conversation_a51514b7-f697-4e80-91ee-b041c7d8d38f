# 仗剑江湖行 - 纯小游戏构建脚本
# 直接从源码构建小游戏，不依赖 uni-app 的页面编译

Write-Host "========================================" -ForegroundColor Green
Write-Host "    仗剑江湖行 - 纯小游戏构建工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$buildDir = "unpackage\dist\dev\mp-weixin"

Write-Host "[1/5] 清理构建目录..." -ForegroundColor Yellow
if (Test-Path $buildDir) {
    Remove-Item -Path $buildDir -Recurse -Force
    Write-Host "✓ 已清理旧的构建文件" -ForegroundColor Green
}

Write-Host ""
Write-Host "[2/5] 创建构建目录..." -ForegroundColor Yellow
New-Item -Path $buildDir -ItemType Directory -Force | Out-Null
New-Item -Path "$buildDir\utils" -ItemType Directory -Force | Out-Null
Write-Host "✓ 构建目录已创建" -ForegroundColor Green

Write-Host ""
Write-Host "[3/5] 复制核心文件..." -ForegroundColor Yellow

# 复制核心文件
$filesToCopy = @(
    @{ src = "game.json"; dest = "game.json" },
    @{ src = "game.js"; dest = "game.js" },
    @{ src = "mini-game-patch.js"; dest = "mini-game-patch.js" },
    @{ src = "miniGameAdapter.js"; dest = "miniGameAdapter.js" },
    @{ src = "miniGameWebSocket.js"; dest = "miniGameWebSocket.js" },
    @{ src = "utils\gameData.js"; dest = "utils\gameData.js" },
    @{ src = "utils\gameState.js"; dest = "utils\gameState.js" },
    @{ src = "utils\websocket.js"; dest = "utils\websocket.js" }
)

foreach ($file in $filesToCopy) {
    $srcPath = $file.src
    $destPath = Join-Path $buildDir $file.dest
    
    if (Test-Path $srcPath) {
        Copy-Item $srcPath $destPath
        Write-Host "✓ 已复制: $($file.src)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  文件不存在: $($file.src)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "[4/5] 创建小游戏入口文件..." -ForegroundColor Yellow

# 创建 app.js
$appJsContent = @"
// 仗剑江湖行 - 小游戏主入口
// 引入兼容性补丁
require('./mini-game-patch.js');

// 引入游戏主逻辑
require('./game.js');

console.log('小游戏 app.js 加载完成');
"@

$appJsContent | Out-File -FilePath "$buildDir\app.js" -Encoding UTF8
Write-Host "✓ 已创建: app.js" -ForegroundColor Green

Write-Host ""
Write-Host "[5/5] 创建项目配置文件..." -ForegroundColor Yellow

# 创建 project.config.json
$projectConfig = @"
{
  "description": "仗剑江湖行小游戏 - 完整功能版本",
  "packOptions": {
    "ignore": []
  },
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "useIsolateContext": true,
    "userConfirmedBundleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true,
    "disableUseStrict": false,
    "minifyWXML": true,
    "showES6CompileOption": false,
    "useCompilerPlugins": false
  },
  "compileType": "miniprogram",
  "libVersion": "2.19.4",
  "appid": "wxfb9c395829d83b91",
  "projectname": "仗剑江湖行",
  "debugOptions": {
    "hidedInDevtools": []
  },
  "scripts": {},
  "staticServerOptions": {
    "baseURL": "",
    "servePath": ""
  },
  "isGameTourist": false,
  "condition": {
    "search": {
      "list": []
    },
    "conversation": {
      "list": []
    },
    "game": {
      "list": []
    },
    "plugin": {
      "list": []
    },
    "gamePlugin": {
      "list": []
    },
    "miniprogram": {
      "list": []
    }
  }
}
"@

$projectConfig | Out-File -FilePath "$buildDir\project.config.json" -Encoding UTF8
Write-Host "✓ 已创建: project.config.json" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "         纯小游戏构建完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 小游戏目录：$(Resolve-Path $buildDir)" -ForegroundColor White
Write-Host ""
Write-Host "🎮 在微信开发者工具中导入：" -ForegroundColor White
Write-Host "1. 打开微信开发者工具" -ForegroundColor White
Write-Host "2. 选择'小游戏'项目类型" -ForegroundColor White
Write-Host "3. 项目目录：$(Resolve-Path $buildDir)" -ForegroundColor White
Write-Host "4. AppID：wxfb9c395829d83b91" -ForegroundColor White
Write-Host "5. 点击'导入'" -ForegroundColor White
Write-Host ""
Write-Host "✨ 所有原有功能都已保留并适配到小游戏环境！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 特性：" -ForegroundColor Yellow
Write-Host "• 完整的登录和注册功能" -ForegroundColor White
Write-Host "• Canvas 渲染的游戏界面" -ForegroundColor White
Write-Host "• WebSocket 实时通信" -ForegroundColor White
Write-Host "• 触摸交互支持" -ForegroundColor White
Write-Host "• 小游戏 API 兼容性" -ForegroundColor White
Write-Host "• 内存管理和性能优化" -ForegroundColor White

Read-Host "`n按任意键退出"
