@echo off
chcp 65001 >nul
echo ========================================
echo      编译模式切换工具
echo ========================================
echo.

echo 请选择编译模式：
echo 1. 小程序模式（开发调试）
echo 2. 小游戏模式（发布版本）
echo.

set /p choice=请输入选择 (1 或 2): 

if "%choice%"=="1" goto miniprogram
if "%choice%"=="2" goto minigame

echo 无效选择，退出...
pause
exit /b 1

:miniprogram
echo.
echo [切换到小程序模式]
echo.

REM 备份当前 manifest.json
copy manifest.json manifest.json.bak >nul

REM 确保使用小程序配置
powershell -Command "(Get-Content manifest.json) -replace '\"mp-weixin-game\"', '\"mp-weixin\"' | Set-Content manifest.json"

REM 确保 pages.json 包含所有页面
echo ✓ 已切换到小程序模式
echo.
echo 现在可以在 HBuilderX 中编译：
echo 运行 ^> 运行到小程序模拟器 ^> 微信开发者工具
echo.
goto end

:minigame
echo.
echo [切换到小游戏模式]
echo.

REM 备份当前 manifest.json
copy manifest.json manifest.json.bak >nul

REM 临时修改 manifest.json，强制使用小游戏配置
powershell -Command "$content = Get-Content manifest.json -Raw; $content = $content -replace '\"mp-weixin\"(\s*):(\s*){', '\"mp-weixin-game\"$1:$2{'; Set-Content manifest.json $content"

echo ✓ 已切换到小游戏模式
echo.
echo 现在可以在 HBuilderX 中编译：
echo 运行 ^> 运行到小程序模拟器 ^> 微信开发者工具
echo.
echo 编译完成后，输出目录将包含小游戏文件
echo.

:end
echo 提示：编译完成后可以运行此脚本切换回其他模式
echo.
pause
