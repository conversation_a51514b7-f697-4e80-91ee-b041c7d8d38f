"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "GatheringPopup",
  props: {
    visible: <PERSON><PERSON><PERSON>,
    event: Object,
    times: Number,
    result: String,
    inventory: Array
  },
  data() {
    return {
      toolNameMap: {
        sickle: "镰刀",
        axe: "斧头",
        pickaxe: "矿镐",
        knife: "小刀",
        hoe: "锄头",
        fishing_rod: "鱼竿",
        net: "捕网"
      }
    };
  },
  computed: {
    hasRequiredTool() {
      var _a, _b, _c;
      console.log("检查工具:", {
        event: this.event,
        requiredTool: (_a = this.event) == null ? void 0 : _a.requiredTool,
        resourceLevel: (_b = this.event) == null ? void 0 : _b.resourceLevel,
        inventory: this.inventory,
        inventoryLength: (_c = this.inventory) == null ? void 0 : _c.length
      });
      if (!this.event || !this.event.requiredTool || !this.inventory) {
        console.log("工具检查失败: 缺少必要数据");
        return false;
      }
      const requiredTool = this.event.requiredTool;
      const requiredLevel = this.event.resourceLevel || 1;
      const toolTypeMap = {
        "镰刀": "sickle",
        "斧头": "axe",
        "矿镐": "pickaxe",
        "小刀": "knife",
        "sickle": "sickle",
        "axe": "axe",
        "pickaxe": "pickaxe",
        "knife": "knife"
      };
      console.log("=== GatheringPopup 工具检查详细调试 ===");
      console.log("需要的工具:", requiredTool);
      console.log("需要的等级:", requiredLevel);
      console.log("工具类型映射:", toolTypeMap);
      console.log("需要的类型:", toolTypeMap[requiredTool]);
      console.log("背包物品数量:", this.inventory.length);
      this.inventory.forEach((item, index) => {
        console.log(`背包物品[${index}]:`, {
          id: item.id,
          name: item.name,
          type: item.type,
          level: item.level,
          quantity: item.quantity
        });
      });
      const matchingTools = this.inventory.filter((item) => {
        if (item.name === requiredTool || item.id === requiredTool) {
          return true;
        }
        const requiredType = toolTypeMap[requiredTool];
        if (requiredType && item.type === requiredType) {
          return true;
        }
        if (item.name && item.name.includes(requiredTool)) {
          return true;
        }
        return false;
      });
      console.log("找到的匹配工具:", matchingTools);
      const validTools = matchingTools.filter((tool) => {
        const toolLevel = parseInt(tool.level || 1);
        const isValid = toolLevel >= requiredLevel;
        console.log(`工具 ${tool.name} 等级${toolLevel} ${isValid ? "✅" : "❌"} 需要等级${requiredLevel}`);
        return isValid;
      });
      console.log("等级足够的工具:", validTools);
      const hasItem = validTools.length > 0;
      console.log("工具检查结果:", hasItem);
      return hasItem;
    }
  },
  methods: {
    onClose() {
      console.log("🚪 GatheringPopup.onClose 被调用");
      console.trace("调用栈:");
      this.$emit("close");
    },
    onGather() {
      console.log("🎯 GatheringPopup.onGather 被调用");
      this.$emit("do-gather");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.event.content),
    b: common_vendor.t($props.event.requiredToolDesc || $props.event.toolName || $data.toolNameMap[$props.event.requiredTool] || $props.event.requiredTool),
    c: common_vendor.t($props.times),
    d: $props.result
  }, $props.result ? {
    e: common_vendor.t($props.result)
  } : {}, {
    f: common_vendor.o((...args) => $options.onGather && $options.onGather(...args)),
    g: !$options.hasRequiredTool || $props.times <= 0,
    h: common_vendor.o((...args) => $options.onClose && $options.onClose(...args)),
    i: common_vendor.o((...args) => $options.onClose && $options.onClose(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a12f92ec"]]);
wx.createComponent(Component);
