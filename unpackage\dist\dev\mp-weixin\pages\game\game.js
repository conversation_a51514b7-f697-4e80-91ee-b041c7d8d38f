"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../mini-game-patch.js");
const GamePageAdapter = require("../../gamePageAdapter.js");
require("../../utils/gameData.js");
require("../../utils/websocket.js");
const _sfc_main = {
  data() {
    return {
      gameAdapter: null,
      canvas: null,
      ctx: null
    };
  },
  onLoad() {
    console.log("游戏页面加载");
    this.initGame();
  },
  onShow() {
    console.log("游戏页面显示");
    if (this.gameAdapter && typeof this.gameAdapter.onShow === "function") {
      this.gameAdapter.onShow();
    }
  },
  onHide() {
    console.log("游戏页面隐藏");
    if (this.gameAdapter && typeof this.gameAdapter.onHide === "function") {
      this.gameAdapter.onHide();
    }
  },
  onUnload() {
    console.log("游戏页面卸载");
    if (this.gameAdapter && typeof this.gameAdapter.destroy === "function") {
      this.gameAdapter.destroy();
    }
  },
  methods: {
    // 初始化游戏
    async initGame() {
      try {
        await this.initCanvas();
        this.gameAdapter = new GamePageAdapter();
        await this.gameAdapter.init();
        console.log("游戏初始化完成");
      } catch (error) {
        console.error("游戏初始化失败:", error);
        common_vendor.index.showToast({
          title: "游戏初始化失败",
          icon: "none"
        });
      }
    },
    // 初始化Canvas
    initCanvas() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getSystemInfo({
          success: (res) => {
            this.ctx = common_vendor.index.createCanvasContext("gameCanvas", this);
            const query = common_vendor.index.createSelectorQuery().in(this);
            query.select("#gameCanvas").boundingClientRect((rect) => {
              if (rect) {
                this.canvas = {
                  width: res.screenWidth,
                  height: res.screenHeight,
                  pixelRatio: res.pixelRatio
                };
                console.log("Canvas初始化完成:", this.canvas);
                resolve();
              } else {
                reject(new Error("Canvas元素未找到"));
              }
            }).exec();
          },
          fail: (error) => {
            reject(error);
          }
        });
      });
    },
    // 触摸开始
    onTouchStart(e) {
      if (this.gameAdapter && typeof this.gameAdapter.handleTouch === "function") {
        this.gameAdapter.handleTouch(e, "start");
      }
    },
    // 触摸移动
    onTouchMove(e) {
      if (this.gameAdapter && typeof this.gameAdapter.handleTouch === "function") {
        this.gameAdapter.handleTouch(e, "move");
      }
    },
    // 触摸结束
    onTouchEnd(e) {
      if (this.gameAdapter && typeof this.gameAdapter.handleTouch === "function") {
        this.gameAdapter.handleTouch(e, "end");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-84820fe1"]]);
wx.createPage(MiniProgramPage);
