"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_websocket = require("../../utils/websocket.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      isLoading: true,
      loadingText: "正在加载角色数据...",
      playerData: {
        character_name: "",
        name: "",
        gender: "男",
        level: 1,
        hp: 0,
        max_hp: 0,
        mp: 0,
        max_mp: 0,
        energy: 0,
        max_energy: 0,
        spirit: 0,
        max_spirit: 0,
        experience: 0,
        money: 0,
        talent: {},
        dodge: 1,
        crit: 1,
        fortune: 1,
        equipment: {},
        inventory_capacity: 50,
        realm_info: {
          current_realm: "初出茅庐",
          next_realm: "不堪一击",
          current_min: 0,
          current_max: 5,
          progress: 0,
          experience: 0
        },
        skill_points: 0,
        current_map: null
        // 新增：当前地图
      },
      // 门派信息
      sectInfo: {
        has_sect: false,
        sect_name: "无门派",
        rank_name: "",
        contribution: 0
      },
      inventoryData: [],
      activeTab: "attributes",
      isExpanding: false,
      bonusSummary: {},
      // 新增：增益摘要
      // 新增：物品配置
      itemsConfig: {},
      mapsConfig: {},
      // 新增：地图配置
      // 新增：天赋详情弹窗
      showTalentModal: false,
      selectedTalent: {},
      // 新增：装备详情弹窗
      showEquipmentModal: false,
      selectedEquipment: {},
      selectedSlot: ""
    };
  },
  computed: {
    mainEquipments() {
      const eq = this.playerData.equipment || {};
      try {
        return JSON.parse(JSON.stringify({
          main_hand: eq.main_hand || null,
          off_hand: eq.off_hand || null,
          armor: eq.armor || null,
          helmet: eq.helmet || null,
          necklace: eq.necklace || null,
          ring1: eq.ring1 || null,
          ring2: eq.ring2 || null,
          medal: eq.medal || null
        }));
      } catch (error) {
        console.error("装备数据解析失败:", error);
        return {
          main_hand: null,
          off_hand: null,
          armor: null,
          helmet: null,
          necklace: null,
          ring1: null,
          ring2: null,
          medal: null
        };
      }
    },
    talentArr() {
      var _a, _b, _c, _d;
      return [
        { label: "力量", value: ((_a = this.playerData.talent) == null ? void 0 : _a.力量) ?? 0 },
        { label: "悟性", value: ((_b = this.playerData.talent) == null ? void 0 : _b.悟性) ?? 0 },
        { label: "身法", value: ((_c = this.playerData.talent) == null ? void 0 : _c.身法) ?? 0 },
        { label: "根骨", value: ((_d = this.playerData.talent) == null ? void 0 : _d.根骨) ?? 0 },
        { label: "富源", value: this.playerData.fortune ?? 1 }
      ];
    },
    talentArrNoFortune() {
      var _a, _b, _c, _d;
      return [
        { label: "力量", value: ((_a = this.playerData.talent) == null ? void 0 : _a.力量) ?? 0 },
        { label: "悟性", value: ((_b = this.playerData.talent) == null ? void 0 : _b.悟性) ?? 0 },
        { label: "身法", value: ((_c = this.playerData.talent) == null ? void 0 : _c.身法) ?? 0 },
        { label: "根骨", value: ((_d = this.playerData.talent) == null ? void 0 : _d.根骨) ?? 0 }
      ];
    },
    currentMapName() {
      if (!this.mapsConfig || !utils_gameState.gameState.player.current_map)
        return "未知";
      const map = this.mapsConfig[utils_gameState.gameState.player.current_map];
      return map ? map.name : "未知";
    },
    energyRegenDetails() {
      return this.playerData.energy_regen_details || null;
    }
  },
  onLoad() {
    this.setupEventListeners();
    setTimeout(async () => {
      await this.loadMapsConfigSafe();
      this.initGameState();
    }, 100);
    this.loadItemsConfig();
  },
  onShow() {
    if (!utils_websocket.wsManager.isConnected) {
      utils_websocket.wsManager.connect().then(() => {
        if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
          utils_websocket.wsManager.autoAuthenticate();
        }
        setTimeout(() => {
          if (utils_gameState.gameState.requestAllData)
            utils_gameState.gameState.requestAllData();
        }, 500);
      });
    } else if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
      utils_websocket.wsManager.autoAuthenticate();
      setTimeout(() => {
        if (utils_gameState.gameState.requestAllData)
          utils_gameState.gameState.requestAllData();
      }, 500);
    }
    this.updatePlayerData();
    this.fetchBonusSummary();
    if (!utils_gameState.gameState.getPlayer()) {
      if (utils_websocket.wsManager.isConnected && utils_gameState.gameState.isAuthed) {
        utils_gameState.gameState.requestAllData();
      } else {
        this.initGameState();
      }
    }
  },
  onUnload() {
    this.cleanupEventListeners();
    utils_gameState.gameState.offUpdate(this.handleStateUpdate);
  },
  methods: {
    async initGameState() {
      try {
        this.isLoading = true;
        this.loadingText = "正在初始化游戏状态...";
        utils_gameState.gameState.onUpdate(this.handleStateUpdate);
        await utils_gameState.gameState.init();
        if (utils_gameState.gameState.isAuthed) {
          utils_gameState.gameState.requestAllData();
        }
        setTimeout(() => {
          if (this.isLoading) {
            this.isLoading = false;
            common_vendor.index.showToast({
              title: "数据加载超时，请重试",
              icon: "none"
            });
          }
        }, 1e4);
      } catch (error) {
        this.isLoading = false;
        console.error("初始化游戏状态失败:", error);
        common_vendor.index.showToast({
          title: "初始化失败: " + error.message,
          icon: "none"
        });
      }
    },
    updatePlayerData() {
      const player = utils_gameState.gameState.getPlayer();
      if (player) {
        this.playerData = { ...player };
      } else {
        if (utils_gameState.gameState.isAuthed) {
          utils_gameState.gameState.requestAllData();
        }
      }
      const inventory = utils_gameState.gameState.getInventory();
      if (inventory && Array.isArray(inventory)) {
        this.inventoryData = [...inventory];
      } else {
        this.inventoryData = [];
      }
      this.loadSectInfo();
    },
    // 获取门派信息
    async loadSectInfo() {
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_info" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_info") {
          if (response.data.has_sect) {
            this.sectInfo = {
              has_sect: true,
              sect_name: response.data.sect_name,
              rank_name: response.data.rank_name,
              contribution: response.data.contribution
            };
          } else {
            this.sectInfo = {
              has_sect: false,
              sect_name: "无门派",
              rank_name: "",
              contribution: 0
            };
          }
        } else {
          this.sectInfo = {
            has_sect: false,
            sect_name: "无门派",
            rank_name: "",
            contribution: 0
          };
        }
      } catch (error) {
        console.error("获取门派信息失败:", error);
        this.sectInfo = {
          has_sect: false,
          sect_name: "无门派",
          rank_name: "",
          contribution: 0
        };
      }
    },
    handleStateUpdate(type, gameStateInstance) {
      switch (type) {
        case "player":
          if (gameStateInstance.player) {
            this.playerData = { ...gameStateInstance.player };
            this.isLoading = false;
            if (!this.playerData.max_mp && this.playerData.max_mp !== 0) {
              console.warn("max_mp 为空或undefined");
            }
            if (!this.playerData.max_energy && this.playerData.max_energy !== 0) {
              console.warn("max_energy 为空或undefined");
            }
            if (!this.playerData.attack && this.playerData.attack !== 0) {
              console.warn("attack 为空或undefined");
            }
            if (!this.playerData.defense && this.playerData.defense !== 0) {
              console.warn("defense 为空或undefined");
            }
            this.checkDataCompleteness();
          }
          break;
        case "inventory":
          if (gameStateInstance.inventory && Array.isArray(gameStateInstance.inventory)) {
            this.inventoryData = [...gameStateInstance.inventory];
          } else {
            this.inventoryData = [];
          }
          break;
      }
    },
    checkDataCompleteness() {
      const requiredFields = ["max_hp", "max_mp", "max_energy", "max_spirit", "attack", "defense", "dodge", "crit"];
      const missingFields = requiredFields.filter((field) => {
        const value = this.playerData[field];
        return value === void 0 || value === null || value === "";
      });
      if (missingFields.length > 0) {
        setTimeout(() => {
          utils_gameState.gameState.requestAllData();
        }, 1e3);
      }
    },
    formatMoney(money) {
      if (money >= 1e12) {
        return (money / 1e12).toFixed(1) + "万亿";
      } else if (money >= 1e8) {
        return (money / 1e8).toFixed(1) + "亿";
      } else if (money >= 1e4) {
        return (money / 1e4).toFixed(1) + "万";
      } else if (money >= 1e3) {
        return money.toLocaleString();
      } else {
        return money.toString();
      }
    },
    formatExperience(exp) {
      if (exp >= 1e12) {
        return (exp / 1e12).toFixed(1) + "万亿";
      } else if (exp >= 1e8) {
        return (exp / 1e8).toFixed(1) + "亿";
      } else if (exp >= 1e4) {
        return (exp / 1e4).toFixed(1) + "万";
      } else if (exp >= 1e3) {
        return exp.toLocaleString();
      } else {
        return exp.toString();
      }
    },
    setupEventListeners() {
      this.cleanupEventListeners();
      this._errorHandler = (err) => this.handleError(err);
      this._authSuccessHandler = (data) => this.handleAuthSuccess(data);
      this._unequipSuccessHandler = (data) => this.handleUnequipSuccess(data);
      this._unequipFailedHandler = (data) => this.handleUnequipFailed(data);
      this._equipSuccessHandler = (data) => this.handleEquipSuccess(data);
      this._equipFailedHandler = (data) => this.handleEquipFailed(data);
      this._healingSuccessHandler = (data) => this.handleHealingSuccess(data);
      this._healingFailedHandler = (data) => this.handleHealingFailed(data);
      this._expandSuccessHandler = (data) => this.handleExpandSuccess(data);
      this._expandFailedHandler = (data) => this.handleExpandFailed(data);
      this._breakthroughSuccessHandler = (data) => this.handleBreakthroughSuccess(data);
      this._breakthroughFailedHandler = (data) => this.handleBreakthroughFailed(data);
    },
    cleanupEventListeners() {
    },
    async fetchBonusSummary() {
      try {
        const res = await utils_gameData.gameUtils.sendMessage({
          type: "get_bonus_summary",
          data: {}
        });
        if (res.type === "bonus_summary") {
          this.bonusSummary = res.data;
        }
      } catch (e) {
        console.warn("获取增益摘要失败", e);
      }
    },
    handleAuthSuccess(data) {
      if (utils_gameState.gameState.isAuthed) {
        utils_gameState.gameState.requestAllData();
      }
    },
    handleError(error) {
      this.isLoading = false;
      console.error("角色页面收到错误:", error);
      const errorMessage = error.message || "网络错误，请重试";
      common_vendor.index.showToast({
        title: errorMessage,
        icon: "none"
      });
    },
    // 处理装备槽点击
    handleEquipmentClick(slot) {
      const equipment = this.mainEquipments[slot];
      if (equipment && equipment.id) {
        this.selectedEquipment = { ...equipment };
        this.selectedSlot = slot;
        this.showEquipmentModal = true;
      } else {
        common_vendor.index.showToast({
          title: `${this.getSlotLabel(slot)}为空`,
          icon: "none"
        });
      }
    },
    // 关闭装备详情弹窗
    closeEquipmentModal() {
      this.showEquipmentModal = false;
      this.selectedEquipment = {};
      this.selectedSlot = "";
    },
    // 卸下选中的装备
    unequipSelectedEquipment() {
      if (this.selectedSlot) {
        const slot = this.selectedSlot;
        this.closeEquipmentModal();
        this.unequipItem(slot);
      }
    },
    // 获取装备类型文本
    getTypeText(type) {
      const typeMap = {
        "weapon": "武器",
        "helmet": "头盔",
        "necklace": "项链",
        "armor": "衣服",
        "cloak": "披风",
        "pants": "裤子",
        "shoes": "鞋子",
        "bracelet": "手镯",
        "ring": "戒指",
        "shield": "盾牌",
        "medal": "勋章",
        "accessory": "饰品"
      };
      return typeMap[type] || type;
    },
    // 获取装备品质文本
    getQualityText(quality) {
      const qualityMap = {
        "common": "普通",
        "uncommon": "优秀",
        "rare": "稀有",
        "epic": "史诗",
        "legendary": "传说",
        "mythic": "神话"
      };
      return qualityMap[quality] || "普通";
    },
    // 获取装备属性效果（与背包页面保持一致）
    getEquipmentEffects(equipment) {
      if (!equipment)
        return [];
      const effects = [];
      const attributeMap = {
        "attack": "攻击",
        "defense": "防御",
        "hp": "气血",
        "mp": "内力",
        "energy": "精力",
        "energy_regen": "精力回复",
        "crit": "暴击",
        "dodge": "闪避",
        "hit": "命中",
        "speed": "速度"
      };
      for (const [englishName, chineseName] of Object.entries(attributeMap)) {
        const value = equipment[englishName] || equipment[chineseName];
        if (value && value > 0) {
          effects.push({
            name: chineseName,
            value
          });
        }
      }
      const effectsString = equipment.effects;
      if (effectsString && typeof effectsString === "string") {
        const effectPairs = effectsString.split(",");
        for (const pair of effectPairs) {
          const [attr, val] = pair.split(":");
          if (attr && val) {
            const attrName = attr.trim();
            const attrValue = parseInt(val.trim());
            const chineseName = attributeMap[attrName] || attrName;
            if (attrValue > 0) {
              const existingEffect = effects.find((e) => e.name === chineseName);
              if (existingEffect) {
                existingEffect.value += attrValue;
              } else {
                effects.push({
                  name: chineseName,
                  value: attrValue
                });
              }
            }
          }
        }
      }
      return effects;
    },
    selectEquipment(slot) {
      const item = this.equipment[slot];
      if (item) {
        common_vendor.index.showModal({
          title: item.name,
          content: `类型：${item.type}
品质：${item.quality}
攻击：${item.attack || 0}
防御：${item.defense || 0}`,
          showCancel: true,
          cancelText: "取消",
          confirmText: "卸下",
          success: (res) => {
            if (res.confirm) {
              this.unequipItem(slot);
            }
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "该槽位为空",
          icon: "none"
        });
      }
    },
    selectInventoryItem(index) {
      const item = this.inventoryData[index];
      if (item) {
        const isEquipment = ["weapon", "helmet", "necklace", "armor", "cloak", "pants", "shoes", "bracelet", "ring"].includes(item.type);
        if (isEquipment) {
          common_vendor.index.showActionSheet({
            itemList: ["装备", "使用", "取消"],
            success: (res) => {
              if (res.tapIndex === 0) {
                this.equipItem(index);
              } else if (res.tapIndex === 1) {
                this.useItem(index);
              }
            }
          });
        } else {
          common_vendor.index.showModal({
            title: item.name,
            content: `类型：${item.type}
品质：${item.quality}
数量：${item.quantity}`,
            showCancel: true,
            cancelText: "取消",
            confirmText: "使用",
            success: (res) => {
              if (res.confirm) {
                this.useItem(index);
              }
            }
          });
        }
      }
    },
    async unequipItem(slot) {
      try {
        this.isLoading = true;
        this.loadingText = "正在卸下装备...";
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "unequip",
          data: { slot }
        });
        this.isLoading = false;
        if (response.type === "unequip_success") {
          common_vendor.index.showToast({
            title: response.data.message || "卸下装备成功",
            icon: "success"
          });
          this.updatePlayerData();
        } else if (response.type === "unequip_failed") {
          console.error("卸下装备失败:", response.data);
          common_vendor.index.showToast({
            title: response.data.message || "卸下装备失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          this.handleError(response.data);
        }
      } catch (error) {
        console.error("卸下装备失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "卸下装备失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    async equipItem(index) {
      const item = this.inventoryData[index];
      if (!item)
        return;
      const slotMapping = {
        "weapon": ["main_hand", "off_hand"],
        "helmet": ["helmet"],
        "necklace": ["necklace"],
        "armor": ["armor"],
        "cloak": ["cloak"],
        "pants": ["pants"],
        "shoes": ["shoes"],
        "ring": ["ring1", "ring2"],
        "off_hand": ["off_hand"],
        // 副手装备
        "shield": ["off_hand"],
        // 盾牌
        "medal": ["medal"]
        // 勋章
      };
      const possibleSlots = slotMapping[item.type] || [];
      if (possibleSlots.length === 0) {
        common_vendor.index.showToast({
          title: "无法装备此物品",
          icon: "none"
        });
        return;
      }
      if (possibleSlots.length > 1) {
        const slotNames = {
          "main_hand": "主手",
          "off_hand": "副手",
          "ring1": "戒指1",
          "ring2": "戒指2"
        };
        const slotOptions = possibleSlots.map((slot) => slotNames[slot] || slot);
        common_vendor.index.showActionSheet({
          itemList: slotOptions,
          success: (res) => {
            const selectedSlot = possibleSlots[res.tapIndex];
            this.doEquipItem(index, selectedSlot);
          }
        });
      } else {
        this.doEquipItem(index, possibleSlots[0]);
      }
    },
    async doEquipItem(index, slot) {
      try {
        this.isLoading = true;
        this.loadingText = "正在装备...";
        utils_websocket.wsManager.sendMessage("equip_item", {
          item_index: index,
          slot_type: slot
        });
      } catch (error) {
        console.error("装备失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "装备失败: " + error.message,
          icon: "none"
        });
      }
    },
    useItem(index) {
    },
    async handleHealing() {
      try {
        this.isLoading = true;
        this.loadingText = "正在疗伤...";
        utils_websocket.wsManager.sendMessage("healing", {});
      } catch (error) {
        console.error("疗伤失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "疗伤失败: " + error.message,
          icon: "none"
        });
      }
    },
    handleHealingSuccess(data) {
      this.isLoading = false;
      common_vendor.index.showToast({
        title: "疗伤成功",
        icon: "success"
      });
      this.updatePlayerData();
    },
    handleHealingFailed(data) {
      this.isLoading = false;
      console.error("疗伤失败:", data);
      common_vendor.index.showToast({
        title: data.message || "疗伤失败",
        icon: "none"
      });
    },
    handleUnequipSuccess(data) {
      this.isLoading = false;
      common_vendor.index.showToast({
        title: data.message || "卸下装备成功",
        icon: "success"
      });
      this.updatePlayerData();
    },
    handleUnequipFailed(data) {
      this.isLoading = false;
      console.error("卸下装备失败:", data);
      common_vendor.index.showToast({
        title: data.message || "卸下装备失败",
        icon: "none"
      });
    },
    handleEquipSuccess(data) {
      this.isLoading = false;
      common_vendor.index.showToast({
        title: data.message || "装备成功",
        icon: "success"
      });
      this.updatePlayerData();
    },
    handleEquipFailed(data) {
      this.isLoading = false;
      console.error("装备失败:", data);
      common_vendor.index.showToast({
        title: data.message || "装备失败",
        icon: "none"
      });
    },
    async expandInventory() {
      try {
        this.isLoading = true;
        this.loadingText = "正在扩充背包...";
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "expand_inventory",
          data: {}
        });
        this.isLoading = false;
        if (response.type === "expand_success") {
          common_vendor.index.showToast({
            title: response.data.message || "扩充背包成功",
            icon: "success"
          });
          if (response.data.capacity) {
            this.inventoryCapacity = response.data.capacity;
          }
          this.updatePlayerData();
        } else if (response.type === "expand_failed") {
          console.error("扩充背包失败:", response.data);
          common_vendor.index.showToast({
            title: response.data.message || "扩充背包失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          this.handleError(response.data);
        }
      } catch (error) {
        console.error("扩充背包失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "扩充背包失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    handleExpandSuccess(data) {
      this.isLoading = false;
      common_vendor.index.showToast({
        title: data.message || "扩充背包成功",
        icon: "success"
      });
      if (data.capacity) {
        this.inventoryCapacity = data.capacity;
      }
      this.updatePlayerData();
    },
    handleExpandFailed(data) {
      this.isLoading = false;
      console.error("扩充背包失败:", data);
      common_vendor.index.showToast({
        title: data.message || "扩充背包失败",
        icon: "none"
      });
    },
    handleBreakthroughSuccess(data) {
      this.isLoading = false;
      common_vendor.index.showToast({
        title: data.message || "境界突破成功！",
        icon: "success"
      });
      this.updatePlayerData();
    },
    handleBreakthroughFailed(data) {
      this.isLoading = false;
      console.error("境界突破失败:", data);
      common_vendor.index.showToast({
        title: data.message || "境界突破失败",
        icon: "none"
      });
    },
    handleCrafting() {
      common_vendor.index.navigateTo({
        url: "/pages/crafting/crafting"
      });
    },
    handleBreakthrough() {
      common_vendor.index.showModal({
        title: "境界突破",
        content: "是否尝试突破当前境界？需要消耗大量历练值。",
        showCancel: true,
        cancelText: "取消",
        confirmText: "突破",
        success: (res) => {
          if (res.confirm) {
            this.attemptBreakthrough();
          }
        }
      });
    },
    async attemptBreakthrough() {
      try {
        this.isLoading = true;
        this.loadingText = "正在突破境界...";
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "realm_breakthrough",
          data: {}
        });
        this.isLoading = false;
        if (response.type === "breakthrough_success") {
          common_vendor.index.showToast({
            title: response.data.message || "境界突破成功！",
            icon: "success"
          });
          this.updatePlayerData();
        } else if (response.type === "breakthrough_failed") {
          console.error("境界突破失败:", response.data);
          common_vendor.index.showToast({
            title: response.data.message || "境界突破失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          this.handleError(response.data);
        }
      } catch (error) {
        console.error("境界突破失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "境界突破失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    handleBackpack() {
      common_vendor.index.navigateTo({
        url: "/pages/character/backpack"
      });
    },
    getItemQualityClass(item) {
      if (!item || !item.quality)
        return "quality-normal";
      switch (item.quality) {
        case "common":
          return "quality-common";
        case "uncommon":
          return "quality-uncommon";
        case "rare":
          return "quality-rare";
        case "epic":
          return "quality-epic";
        case "legendary":
          return "quality-legendary";
        default:
          return "quality-normal";
      }
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    handleItemClick(item, index) {
    },
    getTalentLabel(key) {
      const labels = {
        "力量": "力量",
        "悟性": "悟性",
        "身法": "身法",
        "根骨": "根骨"
      };
      return labels[key] || key;
    },
    getSlotLabel(slot) {
      const slotMap = {
        main_hand: "主手",
        off_hand: "副手",
        armor: "衣服",
        helmet: "头盔",
        necklace: "项链",
        ring1: "戒指1",
        ring2: "戒指2",
        medal: "勋章"
      };
      return slotMap[slot] || slot;
    },
    getRealmProgressPercent() {
      const realmInfo = this.playerData.realm_info;
      if (!realmInfo || !realmInfo.next_min)
        return 0;
      const progress = realmInfo.progress || 0;
      const nextMin = realmInfo.next_min || 0;
      const currentMax = realmInfo.current_max || 0;
      const currentRange = nextMin - currentMax;
      if (currentRange <= 0)
        return 100;
      const currentProgress = progress - currentMax;
      const percent = Math.min(100, Math.max(0, currentProgress / currentRange * 100));
      return Math.round(percent);
    },
    async loadItemsConfig() {
      this.itemsConfig = await utils_gameState.gameState.getItemsConfig();
    },
    // 可在需要时通过 this.itemsConfig[itemId] 获取物品详情
    async loadMapsConfigSafe() {
      try {
        this.mapsConfig = await utils_gameState.gameState.getMapsConfig();
      } catch (e) {
        console.error("地图信息加载失败", e);
        common_vendor.index.showToast({ title: "地图信息错误", icon: "none" });
      }
    },
    // 天赋详情相关方法
    showTalentDetail(talent) {
      this.selectedTalent = talent;
      this.showTalentModal = true;
    },
    closeTalentModal() {
      this.showTalentModal = false;
      this.selectedTalent = {};
    },
    getConstitutionBonus() {
      return 0;
    },
    getConstitutionHpBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.constitution) == null ? void 0 : _c.hp_bonus_percentage) || 0;
    },
    getConstitutionHpBonusValue() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.constitution) == null ? void 0 : _c.hp_bonus) || 0;
    },
    getCurrentEnergyRegen() {
      var _a;
      if (this.energyRegenDetails) {
        return this.energyRegenDetails.final_energy_regen.toFixed(2);
      }
      const baseRegen = this.playerData.energy_regen_rate || 0.1;
      const constitution = ((_a = this.playerData.talent) == null ? void 0 : _a.根骨) || 15;
      const bonus = Math.min(1, (constitution - 15) * 0.02);
      const multiplier = 1 + bonus * Math.log(constitution / 15 + 1) / Math.log(2);
      return (baseRegen * multiplier).toFixed(2);
    },
    // 力量增益计算方法
    getStrengthBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getStrengthAttackBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.attack_bonus) || 0;
    },
    // 悟性增益计算方法
    getIntelligenceBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.intelligence) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getIntelligenceMultiplier() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.intelligence) == null ? void 0 : _c.exp_multiplier) || 1;
    },
    // 身法增益计算方法
    getAgilityDefenseBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.defense_bonus_percentage) || 0;
    },
    getAgilityDodgeBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.dodge_bonus_percentage) || 0;
    },
    getAgilityDefenseBonusValue() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.defense_bonus) || 0;
    },
    getAgilityDodgeBonusValue() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.playerData) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.dodge_bonus) || 0;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f;
  return common_vendor.e({
    a: $data.isLoading
  }, $data.isLoading ? {
    b: common_vendor.t($data.loadingText)
  } : {}, {
    c: common_assets._imports_0,
    d: common_vendor.t($data.playerData.character_name || $data.playerData.name || "未知"),
    e: common_vendor.t($data.playerData.gender || "男"),
    f: common_vendor.t($options.formatExperience($data.playerData.experience || 0)),
    g: common_vendor.t($data.playerData.fortune || 1),
    h: common_vendor.t($data.sectInfo.sect_name),
    i: common_vendor.t($options.formatMoney($data.playerData.money || 0)),
    j: common_vendor.t($data.playerData.skill_points || 0),
    k: common_vendor.t(((_a = $data.playerData.realm_info) == null ? void 0 : _a.current_realm) || "初出茅庐"),
    l: common_vendor.t(Math.floor($data.playerData.max_hp || 0)),
    m: common_vendor.t(Math.floor($data.playerData.max_mp || 0)),
    n: common_vendor.t(Math.floor($data.playerData.max_energy || 0)),
    o: common_vendor.t(Math.floor($data.playerData.max_spirit || $data.playerData.max_energy || 0)),
    p: common_vendor.t(Math.floor($data.playerData.attack || 0)),
    q: common_vendor.t(Math.floor($data.playerData.defense || 0)),
    r: common_vendor.t(($data.playerData.dodge || 0).toFixed(1)),
    s: common_vendor.t(($data.playerData.crit || 0).toFixed(1)),
    t: $data.bonusSummary.total
  }, $data.bonusSummary.total ? {
    v: common_vendor.t(Math.floor($data.bonusSummary.equipment.attack || 0)),
    w: common_vendor.t(Math.floor($data.bonusSummary.equipment.defense || 0)),
    x: common_vendor.t(Math.floor($data.bonusSummary.equipment.hp || 0)),
    y: common_vendor.t(Math.floor($data.bonusSummary.martial.attack || 0)),
    z: common_vendor.t(Math.floor($data.bonusSummary.martial.defense || 0)),
    A: common_vendor.t(Math.floor($data.bonusSummary.martial.hp || 0)),
    B: common_vendor.t(Math.floor($data.bonusSummary.total.attack || 0)),
    C: common_vendor.t(Math.floor($data.bonusSummary.total.defense || 0)),
    D: common_vendor.t(Math.floor($data.bonusSummary.total.hp || 0))
  } : {}, {
    E: common_vendor.f($options.talentArrNoFortune, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: common_vendor.t(Math.floor(item.value || 0)),
        c: item.label,
        d: common_vendor.o(($event) => $options.showTalentDetail(item), item.label)
      };
    }),
    F: $data.showTalentModal
  }, $data.showTalentModal ? common_vendor.e({
    G: common_vendor.t($data.selectedTalent.label),
    H: common_vendor.o((...args) => $options.closeTalentModal && $options.closeTalentModal(...args)),
    I: common_vendor.t($data.selectedTalent.label),
    J: common_vendor.t(Math.floor($data.selectedTalent.value || 0)),
    K: $data.selectedTalent.label === "根骨"
  }, $data.selectedTalent.label === "根骨" ? {
    L: common_vendor.t(($options.getConstitutionHpBonus() || 0).toFixed(1)),
    M: common_vendor.t(($options.getConstitutionHpBonusValue() || 0).toFixed(1)),
    N: common_vendor.t(Math.floor($options.getConstitutionBonus() || 0)),
    O: common_vendor.t(Number($options.getCurrentEnergyRegen() || 0).toFixed(1))
  } : $data.selectedTalent.label === "力量" ? {
    Q: common_vendor.t(($options.getStrengthBonus() || 0).toFixed(1)),
    R: common_vendor.t(($options.getStrengthAttackBonus() || 0).toFixed(1))
  } : $data.selectedTalent.label === "悟性" ? {
    T: common_vendor.t(($options.getIntelligenceBonus() || 0).toFixed(1)),
    U: common_vendor.t(($options.getIntelligenceMultiplier() || 1).toFixed(2))
  } : $data.selectedTalent.label === "身法" ? {
    W: common_vendor.t(($options.getAgilityDefenseBonus() || 0).toFixed(1)),
    X: common_vendor.t(($options.getAgilityDefenseBonusValue() || 0).toFixed(1)),
    Y: common_vendor.t(($options.getAgilityDodgeBonus() || 0).toFixed(1))
  } : {}, {
    P: $data.selectedTalent.label === "力量",
    S: $data.selectedTalent.label === "悟性",
    V: $data.selectedTalent.label === "身法",
    Z: common_vendor.o((...args) => $options.closeTalentModal && $options.closeTalentModal(...args)),
    aa: common_vendor.o(() => {
    }),
    ab: common_vendor.o((...args) => $options.closeTalentModal && $options.closeTalentModal(...args))
  }) : {}, {
    ac: common_vendor.t(((_b = $data.playerData.realm_info) == null ? void 0 : _b.next_realm) || "不堪一击"),
    ad: common_vendor.t($options.formatExperience($data.playerData.experience || 0)),
    ae: common_vendor.t($options.formatExperience(((_c = $data.playerData.realm_info) == null ? void 0 : _c.current_max) || 0)),
    af: $options.getRealmProgressPercent() + "%",
    ag: common_vendor.t(((_e = (_d = $data.playerData.realm_info) == null ? void 0 : _d.next_bonus) == null ? void 0 : _e.description) || "无增益"),
    ah: common_vendor.f(["main_hand", "off_hand", "helmet", "armor", "necklace", "ring1", "ring2", "medal"], (slot, k0, i0) => {
      return common_vendor.e({
        a: $options.mainEquipments[slot] && $options.mainEquipments[slot].id
      }, $options.mainEquipments[slot] && $options.mainEquipments[slot].id ? {
        b: common_vendor.t($options.mainEquipments[slot].icon || "📦"),
        c: common_vendor.t($options.mainEquipments[slot].name)
      } : {
        d: common_vendor.t($options.getSlotLabel(slot))
      }, {
        e: slot,
        f: common_vendor.o(($event) => $options.handleEquipmentClick(slot), slot)
      });
    }),
    ai: $data.showEquipmentModal
  }, $data.showEquipmentModal ? common_vendor.e({
    aj: common_vendor.t($data.selectedEquipment.name),
    ak: common_vendor.o((...args) => $options.closeEquipmentModal && $options.closeEquipmentModal(...args)),
    al: common_vendor.t($data.selectedEquipment.icon || "📦"),
    am: common_vendor.t($options.getTypeText($data.selectedEquipment.type)),
    an: common_vendor.t($options.getQualityText($data.selectedEquipment.quality || $data.selectedEquipment.品质)),
    ao: common_vendor.t($options.getSlotLabel($data.selectedSlot)),
    ap: $options.getEquipmentEffects($data.selectedEquipment).length > 0
  }, $options.getEquipmentEffects($data.selectedEquipment).length > 0 ? {
    aq: common_vendor.f($options.getEquipmentEffects($data.selectedEquipment), (effect, k0, i0) => {
      return {
        a: common_vendor.t(effect.name),
        b: common_vendor.t(effect.value),
        c: effect.name
      };
    })
  } : {}, {
    ar: $data.selectedEquipment.description || $data.selectedEquipment.描述
  }, $data.selectedEquipment.description || $data.selectedEquipment.描述 ? {
    as: common_vendor.t($data.selectedEquipment.description || $data.selectedEquipment.描述)
  } : {}, {
    at: common_vendor.o((...args) => $options.unequipSelectedEquipment && $options.unequipSelectedEquipment(...args)),
    av: common_vendor.o((...args) => $options.closeEquipmentModal && $options.closeEquipmentModal(...args)),
    aw: common_vendor.o(() => {
    }),
    ax: common_vendor.o((...args) => $options.closeEquipmentModal && $options.closeEquipmentModal(...args))
  }) : {}, {
    ay: common_vendor.o((...args) => $options.handleHealing && $options.handleHealing(...args)),
    az: common_vendor.o((...args) => $options.handleCrafting && $options.handleCrafting(...args)),
    aA: common_vendor.o((...args) => $options.handleBreakthrough && $options.handleBreakthrough(...args)),
    aB: $data.playerData.experience < (((_f = $data.playerData.realm_info) == null ? void 0 : _f.current_max) || 0),
    aC: common_vendor.o((...args) => $options.handleBackpack && $options.handleBackpack(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3387d3fa"]]);
wx.createPage(MiniProgramPage);
