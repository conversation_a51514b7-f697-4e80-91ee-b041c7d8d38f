/**
 * 仗剑江湖行 - 微信小游戏核心引擎
 * 完全重构版本，不依赖uni-app框架
 */

class GameEngine {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.systemInfo = {};
    
    // 游戏状态
    this.gameState = {
      currentScene: 'login',
      isLoggedIn: false,
      playerData: null,
      gameData: {},
      inputData: {
        username: '',
        password: '',
        registerData: {
          username: '',
          password: '',
          confirmPassword: '',
          characterName: '',
          gender: 'male'
        }
      }
    };
    
    // 场景管理器
    this.sceneManager = null;
    this.inputManager = null;
    this.networkManager = null;
    this.uiManager = null;
    
    // 游戏循环
    this.lastTime = 0;
    this.isRunning = false;
    
    this.init();
  }
  
  // 初始化游戏引擎
  async init() {
    console.log('=== 仗剑江湖行小游戏引擎启动 ===');
    
    try {
      // 获取系统信息
      this.systemInfo = wx.getSystemInfoSync();
      console.log('系统信息:', this.systemInfo);
      
      // 创建Canvas
      this.canvas = wx.createCanvas();
      this.ctx = this.canvas.getContext('2d');
      
      // 设置Canvas尺寸
      this.canvas.width = this.systemInfo.screenWidth;
      this.canvas.height = this.systemInfo.screenHeight;
      
      console.log('Canvas创建成功:', this.canvas.width, 'x', this.canvas.height);
      
      // 初始化各个管理器
      await this.initManagers();
      
      // 注册事件监听器
      this.registerEventListeners();
      
      // 启动游戏循环
      this.startGameLoop();
      
      console.log('游戏引擎初始化完成');
      
    } catch (error) {
      console.error('游戏引擎初始化失败:', error);
      this.showError('游戏初始化失败: ' + error.message);
    }
  }
  
  // 初始化管理器
  async initManagers() {
    try {
      // 导入管理器类
      const UIManager = require('./uiManager.js');
      const InputManager = require('./inputManager.js');
      const NetworkManager = require('./networkManager.js');
      const sceneModule = require('./sceneManager.js');
      const SceneManager = sceneModule.SceneManager;

      // UI管理器
      this.uiManager = new UIManager(this.ctx, this.canvas);

      // 输入管理器
      this.inputManager = new InputManager(this);

      // 网络管理器
      this.networkManager = new NetworkManager(this);

      // 场景管理器
      this.sceneManager = new SceneManager(this);

      console.log('所有管理器初始化完成');
    } catch (error) {
      console.error('管理器初始化失败:', error);
      throw error;
    }
  }
  
  // 注册事件监听器
  registerEventListeners() {
    // 触摸事件
    wx.onTouchStart((e) => this.inputManager.handleTouchStart(e));
    wx.onTouchMove((e) => this.inputManager.handleTouchMove(e));
    wx.onTouchEnd((e) => this.inputManager.handleTouchEnd(e));
    
    // 生命周期事件
    wx.onShow(() => {
      console.log('游戏进入前台');
      this.isRunning = true;
    });
    
    wx.onHide(() => {
      console.log('游戏进入后台');
      this.isRunning = false;
    });
    
    // 内存警告
    if (wx.onMemoryWarning) {
      wx.onMemoryWarning((res) => {
        console.warn('内存警告:', res.level);
        if (wx.triggerGC) {
          wx.triggerGC();
        }
      });
    }
  }
  
  // 启动游戏循环
  startGameLoop() {
    this.isRunning = true;
    this.lastTime = Date.now();
    this.gameLoop();
  }
  
  // 游戏主循环
  gameLoop() {
    if (!this.isRunning) {
      requestAnimationFrame(() => this.gameLoop());
      return;
    }
    
    const currentTime = Date.now();
    const deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;
    
    try {
      // 更新游戏逻辑
      this.update(deltaTime);
      
      // 渲染游戏画面
      this.render();
      
    } catch (error) {
      console.error('游戏循环错误:', error);
    }
    
    // 继续下一帧
    requestAnimationFrame(() => this.gameLoop());
  }
  
  // 更新游戏逻辑
  update(deltaTime) {
    if (this.sceneManager) {
      this.sceneManager.update(deltaTime);
    }
  }
  
  // 渲染游戏画面
  render() {
    if (this.sceneManager) {
      this.sceneManager.render();
    }
  }
  
  // 切换场景
  switchScene(sceneName, data = null) {
    console.log('切换场景:', sceneName);
    this.gameState.currentScene = sceneName;
    
    if (this.sceneManager) {
      this.sceneManager.switchTo(sceneName, data);
    }
  }
  
  // 显示错误信息
  showError(message) {
    console.error('游戏错误:', message);
    
    // 清空画布
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 显示错误信息
    this.ctx.fillStyle = '#dc3545';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('游戏错误', this.canvas.width / 2, 100);
    
    this.ctx.fillStyle = '#6c757d';
    this.ctx.font = '16px Arial';
    this.ctx.fillText(message, this.canvas.width / 2, 150);
    
    this.ctx.fillText('请重启小游戏', this.canvas.width / 2, 200);
  }
  
  // 显示提示信息
  showToast(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: duration
    });
  }
  
  // 显示确认对话框
  showConfirm(title, content, callback) {
    wx.showModal({
      title: title,
      content: content,
      success: (res) => {
        if (callback) {
          callback(res.confirm);
        }
      }
    });
  }
  
  // 显示输入对话框
  showInput(title, placeholder, callback) {
    wx.showModal({
      title: title,
      editable: true,
      placeholderText: placeholder,
      success: (res) => {
        if (res.confirm && callback) {
          callback(res.content || '');
        }
      }
    });
  }
  
  // 获取游戏数据
  getGameData() {
    return this.gameState;
  }
  
  // 更新玩家数据
  updatePlayerData(data) {
    this.gameState.playerData = { ...this.gameState.playerData, ...data };
    console.log('玩家数据已更新:', this.gameState.playerData);
  }
  
  // 设置登录状态
  setLoginStatus(isLoggedIn, playerData = null) {
    this.gameState.isLoggedIn = isLoggedIn;
    if (playerData) {
      this.gameState.playerData = playerData;
    }
    
    if (isLoggedIn) {
      this.switchScene('index');
    } else {
      this.switchScene('login');
    }
  }
}

// 全局游戏引擎实例
let gameEngine = null;

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { GameEngine };
} else {
  window.GameEngine = GameEngine;
}
