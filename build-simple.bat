@echo off
echo ========================================
echo      Simple Mini-game Builder
echo ========================================
echo.

set TARGET_DIR=unpackage\dist\dev\mp-weixin-game

echo Cleaning target directory...
if exist "%TARGET_DIR%" rmdir /s /q "%TARGET_DIR%"
mkdir "%TARGET_DIR%"

echo.
echo Copying game files...

REM Copy main game file
if exist "game-debug.js" (
    echo Copying game-debug.js as game.js...
    copy game-debug.js "%TARGET_DIR%\game.js" >nul 2>&1
) else if exist "game-simple.js" (
    echo Copying game-simple.js as game.js...
    copy game-simple.js "%TARGET_DIR%\game.js" >nul 2>&1
) else if exist "game.js" (
    echo Copying game.js...
    copy game.js "%TARGET_DIR%\game.js" >nul 2>&1
) else (
    echo ERROR: No game.js file found!
    pause
    exit /b 1
)

REM Copy or create game.json
if exist "game.json" (
    echo Copying game.json...
    copy game.json "%TARGET_DIR%\game.json" >nul 2>&1
) else (
    echo Creating default game.json...
    echo {"deviceOrientation":"portrait","showStatusBar":false} > "%TARGET_DIR%\game.json"
)

REM Copy optional engine files
echo Copying engine files...
if exist "gameEngine.js" copy gameEngine.js "%TARGET_DIR%\" >nul 2>&1
if exist "uiManager.js" copy uiManager.js "%TARGET_DIR%\" >nul 2>&1
if exist "inputManager.js" copy inputManager.js "%TARGET_DIR%\" >nul 2>&1
if exist "networkManager.js" copy networkManager.js "%TARGET_DIR%\" >nul 2>&1
if exist "sceneManager.js" copy sceneManager.js "%TARGET_DIR%\" >nul 2>&1
if exist "gameRenderer.js" copy gameRenderer.js "%TARGET_DIR%\" >nul 2>&1
if exist "gameScenes.js" copy gameScenes.js "%TARGET_DIR%\" >nul 2>&1

REM Copy utils directory
if exist "utils" (
    echo Copying utils directory...
    if not exist "%TARGET_DIR%\utils" mkdir "%TARGET_DIR%\utils"
    copy utils\*.js "%TARGET_DIR%\utils\" >nul 2>&1
)

REM Copy components directory
if exist "components" (
    echo Copying components directory...
    if not exist "%TARGET_DIR%\components" mkdir "%TARGET_DIR%\components"
    copy components\*.js "%TARGET_DIR%\components\" >nul 2>&1
)

REM Copy static resources
if exist "static" (
    echo Copying static directory...
    xcopy "static" "%TARGET_DIR%\static\" /E /I /Y >nul 2>&1
)

if exist "images" (
    echo Copying images directory...
    xcopy "images" "%TARGET_DIR%\images\" /E /I /Y >nul 2>&1
)

REM Create app.js
echo Creating app.js...
echo // Mini-game entry point > "%TARGET_DIR%\app.js"
echo console.log('Mini-game starting...'); >> "%TARGET_DIR%\app.js"
echo require('./game.js'); >> "%TARGET_DIR%\app.js"

echo.
echo Verifying files...
if exist "%TARGET_DIR%\game.js" (
    echo [OK] game.js exists
) else (
    echo [ERROR] game.js missing!
)

if exist "%TARGET_DIR%\game.json" (
    echo [OK] game.json exists
) else (
    echo [ERROR] game.json missing!
)

if exist "%TARGET_DIR%\app.js" (
    echo [OK] app.js exists
) else (
    echo [ERROR] app.js missing!
)

echo.
echo ========================================
echo         Build Complete!
echo ========================================
echo.
echo Mini-game directory: %cd%\%TARGET_DIR%
echo.
echo To test in WeChat Developer Tools:
echo 1. Open WeChat Developer Tools
echo 2. Select "Mini-game" project type
echo 3. Import directory: %cd%\%TARGET_DIR%
echo 4. AppID: wxfb9c395829d83b91
echo.
echo Files in target directory:
dir "%TARGET_DIR%" /b
echo.
pause
