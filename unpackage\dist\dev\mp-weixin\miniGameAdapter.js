// 仗剑江湖行 - 小游戏适配层
// 将 uni-app 页面结构转换为小游戏 Canvas 渲染系统

class MiniGameAdapter {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.currentScene = null;
    this.scenes = new Map();
    this.touchHandlers = new Map();
    this.gameData = null;
    this.wsManager = null;
    
    // 屏幕信息
    this.screenInfo = {
      width: 375,
      height: 667,
      pixelRatio: 1
    };
    
    // UI 配置
    this.uiConfig = {
      headerHeight: 60,
      tabBarHeight: 80,
      padding: 20,
      fontSize: {
        title: 24,
        normal: 16,
        small: 14
      },
      colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        background: '#f5f5f5',
        text: '#2c3e50',
        white: '#ffffff'
      }
    };
    
    this.init();
  }
  
  // 初始化小游戏适配器
  async init() {
    console.log('初始化小游戏适配器...');
    
    try {
      // 获取系统信息
      if (typeof wx !== 'undefined') {
        const systemInfo = wx.getSystemInfoSync();
        this.screenInfo = {
          width: systemInfo.screenWidth,
          height: systemInfo.screenHeight,
          pixelRatio: systemInfo.pixelRatio
        };
        
        // 创建 Canvas
        this.canvas = wx.createCanvas();
        this.ctx = this.canvas.getContext('2d');
        
        // 设置 Canvas 尺寸
        this.canvas.width = this.screenInfo.width;
        this.canvas.height = this.screenInfo.height;
        
        console.log('Canvas 创建成功:', this.canvas.width, 'x', this.canvas.height);
      }
      
      // 初始化游戏数据
      await this.initGameData();
      
      // 初始化 WebSocket
      await this.initWebSocket();
      
      // 注册触摸事件
      this.registerTouchEvents();
      
      // 注册场景
      this.registerScenes();
      
      // 显示登录场景
      this.showScene('login');
      
      console.log('小游戏适配器初始化完成');
      
    } catch (error) {
      console.error('小游戏适配器初始化失败:', error);
    }
  }
  
  // 初始化游戏数据
  async initGameData() {
    try {
      // 导入游戏数据模块
      const gameDataModule = require('./utils/gameData.js');
      this.gameData = gameDataModule.default || gameDataModule;
      console.log('游戏数据初始化完成');
    } catch (error) {
      console.error('游戏数据初始化失败:', error);
    }
  }
  
  // 初始化 WebSocket
  async initWebSocket() {
    try {
      // 导入小游戏 WebSocket 管理器
      const MiniGameWebSocketManager = require('./miniGameWebSocket.js');
      this.wsManager = new MiniGameWebSocketManager();

      // 设置适配器引用
      this.wsManager.setGameAdapter(this);

      // 注册 WebSocket 事件处理器
      this.registerWebSocketHandlers();

      // 连接 WebSocket
      await this.wsManager.connect();
      console.log('WebSocket 连接成功');
    } catch (error) {
      console.error('WebSocket 初始化失败:', error);
    }
  }

  // 注册 WebSocket 事件处理器
  registerWebSocketHandlers() {
    if (!this.wsManager) return;

    // 注册登录认证事件处理器
    this.wsManager.on('login_success', (data) => {
      this.onLoginSuccess(data);
    });

    this.wsManager.on('login_failed', (data) => {
      this.onLoginFailed(data);
    });

    this.wsManager.on('auth_success', (data) => {
      this.onAuthSuccess(data);
    });

    this.wsManager.on('auth_failed', (data) => {
      this.onAuthFailed(data);
    });

    this.wsManager.on('register_success', (data) => {
      this.onRegisterSuccess(data);
    });

    this.wsManager.on('register_failed', (data) => {
      this.onRegisterFailed(data);
    });

    // 注册游戏事件处理器
    this.wsManager.on('adventure_result', (data) => {
      this.handleAdventureResult(data);
    });

    this.wsManager.on('player_data', (data) => {
      this.handlePlayerData(data);
    });

    this.wsManager.on('battle_result', (data) => {
      this.handleBattleResult(data);
    });

    this.wsManager.on('market_data', (data) => {
      this.handleMarketData(data);
    });

    this.wsManager.on('guild_data', (data) => {
      this.handleGuildData(data);
    });
  }

  // WebSocket 事件回调
  onWebSocketConnected() {
    console.log('WebSocket 连接成功，更新UI状态');
    // 可以在这里更新连接状态显示
  }

  onWebSocketDisconnected(res) {
    console.log('WebSocket 连接断开，更新UI状态');
    // 可以在这里显示断线提示
  }

  onWebSocketError(res) {
    console.error('WebSocket 连接错误:', res);
    // 可以在这里显示错误提示
  }

  onWebSocketMessage(message) {
    console.log('收到 WebSocket 消息:', message);
    // 根据消息类型更新对应的场景
    this.updateSceneByMessage(message);
  }

  onLoginSuccess(data) {
    console.log('登录成功:', data);

    this.loginState.isLoading = false;

    // 保存登录信息
    if (data.token) {
      this.setLocalStorage('token', data.token);
    }
    if (data.userInfo) {
      this.setLocalStorage('userInfo', data.userInfo);
    }

    this.showToast('登录成功', 'success');

    // 延迟跳转到主页
    setTimeout(() => {
      this.showScene('index');
    }, 1000);
  }

  onAuthSuccess(data) {
    console.log('认证成功:', data);

    this.loginState.isLoading = false;

    // 更新用户信息
    if (data.userInfo) {
      this.setLocalStorage('userInfo', data.userInfo);
    }

    this.showToast('自动登录成功', 'success');

    // 跳转到主页
    setTimeout(() => {
      this.showScene('index');
    }, 1000);
  }

  onLoginFailed(data) {
    console.log('登录失败:', data);

    this.loginState.isLoading = false;

    const message = data && data.message ? data.message : '登录失败，请重试';
    this.showToast(message);

    // 刷新登录界面
    this.showScene('login');
  }

  onAuthFailed(data) {
    console.log('认证失败:', data);

    this.loginState.isLoading = false;

    // 清除本地存储的登录信息
    this.removeLocalStorage('token');
    this.removeLocalStorage('userInfo');

    const message = data && data.message ? data.message : '自动登录失败，请重新登录';
    this.showToast(message);

    // 跳转到登录页面
    this.showScene('login');
  }

  // 处理注册成功
  onRegisterSuccess(data) {
    console.log('注册成功:', data);

    this.loginState.isLoading = false;

    // 保存登录信息
    if (data.token) {
      this.setLocalStorage('token', data.token);
    }
    if (data.userInfo) {
      this.setLocalStorage('userInfo', data.userInfo);
    }

    this.showToast('注册成功，欢迎来到江湖！', 'success');

    // 延迟跳转到主页
    setTimeout(() => {
      this.showScene('index');
    }, 1500);
  }

  // 处理注册失败
  onRegisterFailed(data) {
    console.log('注册失败:', data);

    this.loginState.isLoading = false;

    const message = data && data.message ? data.message : '注册失败，请重试';
    this.showToast(message);

    // 刷新注册界面
    this.showScene('login');
  }

  // 处理冒险结果
  handleAdventureResult(data) {
    console.log('收到冒险结果:', data);

    // 更新玩家数据
    if (data.player_data) {
      this.updatePlayerData(data.player_data);
    }

    // 显示冒险结果
    this.showAdventureResult(data);

    // 刷新主页场景
    if (this.currentScene === 'index') {
      this.showScene('index');
    }
  }

  // 处理玩家数据
  handlePlayerData(data) {
    console.log('收到玩家数据:', data);
    this.updatePlayerData(data);

    // 刷新当前场景
    this.refreshCurrentScene();
  }

  // 处理战斗结果
  handleBattleResult(data) {
    console.log('收到战斗结果:', data);
    this.showBattleResult(data);
  }

  // 处理市场数据
  handleMarketData(data) {
    console.log('收到市场数据:', data);

    // 如果当前在商店场景，刷新显示
    if (this.currentScene === 'shop') {
      this.showScene('shop');
    }
  }

  // 处理门派数据
  handleGuildData(data) {
    console.log('收到门派数据:', data);

    // 如果当前在门派场景，刷新显示
    if (this.currentScene === 'guild') {
      this.showScene('guild');
    }
  }

  // 更新玩家数据
  updatePlayerData(data) {
    if (this.gameData && this.gameData.playerStats) {
      Object.assign(this.gameData.playerStats, data);
    }
  }

  // 根据消息更新场景
  updateSceneByMessage(message) {
    // 根据消息类型决定是否需要刷新场景
    const refreshTypes = ['player_data', 'adventure_result', 'battle_result'];

    if (refreshTypes.includes(message.type)) {
      this.refreshCurrentScene();
    }
  }

  // 刷新当前场景
  refreshCurrentScene() {
    if (this.currentScene) {
      this.showScene(this.currentScene);
    }
  }

  // 显示冒险结果
  showAdventureResult(data) {
    if (typeof wx !== 'undefined') {
      let title = '冒险完成';

      if (data.event_type) {
        switch (data.event_type) {
          case 1:
            title = '好运事件';
            break;
          case 2:
            title = '遭遇NPC';
            break;
          case 3:
            title = '采集事件';
            break;
          case 5:
            title = '奇遇事件';
            break;
          default:
            title = '江湖事件';
        }
      }

      wx.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 显示战斗结果
  showBattleResult(data) {
    if (typeof wx !== 'undefined') {
      const title = data.victory ? '战斗胜利' : '战斗失败';

      wx.showToast({
        title: title,
        icon: data.victory ? 'success' : 'none',
        duration: 2000
      });
    }
  }

  // 生命周期方法
  onShow() {
    console.log('游戏适配器：应用进入前台');

    // 重新连接 WebSocket
    if (this.wsManager && !this.wsManager.isConnected) {
      this.wsManager.reconnect();
    }

    // 刷新当前场景
    this.refreshCurrentScene();
  }

  onHide() {
    console.log('游戏适配器：应用进入后台');

    // 可以在这里暂停一些操作，但保持 WebSocket 连接
  }

  // 内存警告处理
  handleMemoryWarning(level) {
    console.warn('游戏适配器：收到内存警告，级别:', level);

    // 清理一些缓存数据
    this.clearCache();

    // 触发垃圾回收
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      wx.triggerGC();
    }
  }

  // 清理缓存
  clearCache() {
    // 清理一些不必要的数据
    console.log('清理游戏缓存数据');
  }

  // 检查自动登录
  checkAutoLogin() {
    try {
      // 模拟 uni.getStorageSync
      const token = this.getLocalStorage('token');
      const userInfo = this.getLocalStorage('userInfo');

      if (token && userInfo) {
        console.log('发现已保存的登录信息，尝试自动登录');
        this.showToast('发现已保存的登录信息');

        setTimeout(() => {
          this.autoLogin();
        }, 1500);
      }
    } catch (error) {
      console.error('检查自动登录失败:', error);
    }
  }

  // 自动登录
  async autoLogin() {
    try {
      const token = this.getLocalStorage('token');
      const userInfo = this.getLocalStorage('userInfo');

      if (!token) return;

      this.loginState.isLoading = true;
      this.showScene('login');

      // 连接 WebSocket
      if (this.wsManager) {
        await this.wsManager.connect();

        // 发送认证消息
        this.wsManager.send({
          type: 'auth',
          data: {
            token: token,
            userInfo: userInfo
          }
        });
      }

    } catch (error) {
      console.error('自动登录失败:', error);
      this.loginState.isLoading = false;
      this.showToast('自动登录失败，请手动登录');
      this.showScene('login');
    }
  }

  // 处理登录
  async handleLogin() {
    const { username, password } = this.loginState.loginForm;

    if (!username || !password) {
      this.showToast('请输入账号和密码');
      return;
    }

    try {
      this.loginState.isLoading = true;
      this.showScene('login');

      // 连接 WebSocket
      if (this.wsManager) {
        await this.wsManager.connect();

        // 发送登录消息
        this.wsManager.send({
          type: 'login',
          data: {
            username: username,
            password: password
          }
        });
      }

    } catch (error) {
      console.error('登录失败:', error);
      this.loginState.isLoading = false;
      this.showToast('登录失败，请重试');
      this.showScene('login');
    }
  }

  // 处理注册
  async handleRegister() {
    const form = this.loginState.registerForm;

    // 表单验证
    if (!this.validateRegisterForm(form)) {
      this.showToast('请检查输入信息');
      return;
    }

    try {
      this.loginState.isLoading = true;
      this.showScene('login');

      // 连接 WebSocket
      if (this.wsManager) {
        await this.wsManager.connect();

        // 发送注册消息
        this.wsManager.send({
          type: 'register',
          data: {
            username: form.username,
            password: form.password,
            characterName: form.characterName,
            gender: form.gender
          }
        });
      }

    } catch (error) {
      console.error('注册失败:', error);
      this.loginState.isLoading = false;
      this.showToast('注册失败，请重试');
      this.showScene('login');
    }
  }

  // 验证注册表单
  validateRegisterForm(form) {
    if (!form.username || form.username.length < 3 || form.username.length > 20) {
      this.showToast('账号长度应为3-20位字符');
      return false;
    }

    if (!form.password || form.password.length < 6 || form.password.length > 20) {
      this.showToast('密码长度应为6-20位字符');
      return false;
    }

    if (form.password !== form.confirmPassword) {
      this.showToast('两次输入的密码不一致');
      return false;
    }

    if (!form.characterName || form.characterName.length < 2 || form.characterName.length > 10) {
      this.showToast('角色名长度应为2-10位字符');
      return false;
    }

    return true;
  }

  // 处理输入框点击
  handleInputFieldClick(x, y) {
    if (!this.inputFields) return;

    for (const [fieldName, field] of Object.entries(this.inputFields)) {
      if (this.isPointInRect(x, y, field)) {
        this.showInputDialog(fieldName);
        break;
      }
    }
  }

  // 显示输入对话框
  showInputDialog(fieldName) {
    if (typeof wx === 'undefined') return;

    const fieldConfig = {
      'username': { title: '请输入账号', placeholder: '3-20位字符' },
      'password': { title: '请输入密码', placeholder: '6-20位字符' },
      'reg_username': { title: '请输入账号', placeholder: '3-20位字符' },
      'reg_password': { title: '请输入密码', placeholder: '6-20位字符' },
      'reg_confirm': { title: '请确认密码', placeholder: '再次输入密码' },
      'reg_character': { title: '请输入角色名', placeholder: '2-10位字符' }
    };

    const config = fieldConfig[fieldName];
    if (!config) return;

    // 获取当前值
    let currentValue = '';
    if (fieldName.startsWith('reg_')) {
      const key = fieldName.replace('reg_', '');
      if (key === 'confirm') {
        currentValue = this.loginState.registerForm.confirmPassword;
      } else if (key === 'character') {
        currentValue = this.loginState.registerForm.characterName;
      } else {
        currentValue = this.loginState.registerForm[key];
      }
    } else {
      currentValue = this.loginState.loginForm[fieldName];
    }

    // 显示输入框
    wx.showModal({
      title: config.title,
      placeholderText: config.placeholder,
      editable: true,
      content: currentValue,
      success: (res) => {
        if (res.confirm && res.content !== undefined) {
          this.updateFormField(fieldName, res.content);
          this.showScene('login');
        }
      }
    });
  }

  // 更新表单字段
  updateFormField(fieldName, value) {
    if (fieldName.startsWith('reg_')) {
      const key = fieldName.replace('reg_', '');
      if (key === 'confirm') {
        this.loginState.registerForm.confirmPassword = value;
      } else if (key === 'character') {
        this.loginState.registerForm.characterName = value;
      } else {
        this.loginState.registerForm[key] = value;
      }
    } else {
      this.loginState.loginForm[fieldName] = value;
    }
  }

  // 本地存储操作
  getLocalStorage(key) {
    try {
      if (typeof wx !== 'undefined' && wx.getStorageSync) {
        return wx.getStorageSync(key);
      }
      return localStorage.getItem(key);
    } catch (error) {
      console.error('获取本地存储失败:', error);
      return null;
    }
  }

  setLocalStorage(key, value) {
    try {
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync(key, value);
      } else {
        localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error('设置本地存储失败:', error);
    }
  }

  removeLocalStorage(key) {
    try {
      if (typeof wx !== 'undefined' && wx.removeStorageSync) {
        wx.removeStorageSync(key);
      } else {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.error('删除本地存储失败:', error);
    }
  }

  // 显示提示
  showToast(title, icon = 'none') {
    if (typeof wx !== 'undefined' && wx.showToast) {
      wx.showToast({
        title: title,
        icon: icon,
        duration: 2000
      });
    } else {
      console.log('Toast:', title);
    }
  }
  
  // 注册触摸事件
  registerTouchEvents() {
    if (typeof wx !== 'undefined') {
      wx.onTouchStart((e) => {
        this.handleTouch(e, 'start');
      });
      
      wx.onTouchMove((e) => {
        this.handleTouch(e, 'move');
      });
      
      wx.onTouchEnd((e) => {
        this.handleTouch(e, 'end');
      });
    }
  }
  
  // 处理触摸事件
  handleTouch(e, type) {
    if (!this.currentScene || !e.touches || e.touches.length === 0) return;
    
    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;
    
    // 获取当前场景的触摸处理器
    const handler = this.touchHandlers.get(this.currentScene);
    if (handler && typeof handler === 'function') {
      handler(x, y, type);
    }
  }
  
  // 注册场景
  registerScenes() {
    // 登录场景
    this.scenes.set('login', this.createLoginScene());
    this.touchHandlers.set('login', this.createLoginTouchHandler());
    
    // 主页场景
    this.scenes.set('index', this.createIndexScene());
    this.touchHandlers.set('index', this.createIndexTouchHandler());
    
    // 角色场景
    this.scenes.set('character', this.createCharacterScene());
    this.touchHandlers.set('character', this.createCharacterTouchHandler());
    
    // 武功场景
    this.scenes.set('skills', this.createSkillsScene());
    this.touchHandlers.set('skills', this.createSkillsTouchHandler());
    
    // 商店场景
    this.scenes.set('shop', this.createShopScene());
    this.touchHandlers.set('shop', this.createShopTouchHandler());
    
    // 门派场景
    this.scenes.set('guild', this.createGuildScene());
    this.touchHandlers.set('guild', this.createGuildTouchHandler());
  }
  
  // 显示场景
  showScene(sceneName) {
    const scene = this.scenes.get(sceneName);
    if (scene && typeof scene === 'function') {
      this.currentScene = sceneName;
      this.clearCanvas();
      scene();
      console.log('切换到场景:', sceneName);
    } else {
      console.error('场景不存在:', sceneName);
    }
  }
  
  // 清空画布
  clearCanvas() {
    if (this.ctx) {
      this.ctx.fillStyle = this.uiConfig.colors.background;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }
  
  // 绘制文本
  drawText(text, x, y, options = {}) {
    if (!this.ctx) return;
    
    const {
      fontSize = this.uiConfig.fontSize.normal,
      color = this.uiConfig.colors.text,
      align = 'left',
      bold = false
    } = options;
    
    this.ctx.font = `${bold ? 'bold ' : ''}${fontSize}px Arial`;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    this.ctx.fillText(text, x, y);
  }
  
  // 绘制按钮
  drawButton(text, x, y, width, height, options = {}) {
    if (!this.ctx) return;
    
    const {
      backgroundColor = this.uiConfig.colors.primary,
      textColor = this.uiConfig.colors.white,
      fontSize = this.uiConfig.fontSize.normal,
      borderRadius = 5
    } = options;
    
    // 绘制按钮背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制按钮文本
    this.drawText(text, x + width / 2, y + height / 2 + fontSize / 3, {
      fontSize,
      color: textColor,
      align: 'center'
    });
    
    return { x, y, width, height };
  }
  
  // 绘制头部导航
  drawHeader(title) {
    if (!this.ctx) return;
    
    const headerHeight = this.uiConfig.headerHeight;
    
    // 绘制头部背景
    this.ctx.fillStyle = this.uiConfig.colors.primary;
    this.ctx.fillRect(0, 0, this.canvas.width, headerHeight);
    
    // 绘制标题
    this.drawText(title, this.canvas.width / 2, headerHeight / 2 + 8, {
      fontSize: this.uiConfig.fontSize.title,
      color: this.uiConfig.colors.white,
      align: 'center',
      bold: true
    });
  }
  
  // 绘制底部导航栏
  drawTabBar() {
    if (!this.ctx) return;
    
    const tabBarHeight = this.uiConfig.tabBarHeight;
    const tabBarY = this.canvas.height - tabBarHeight;
    const tabWidth = this.canvas.width / 5;
    
    // 绘制底部背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(0, tabBarY, this.canvas.width, tabBarHeight);
    
    // 绘制分割线
    this.ctx.strokeStyle = '#e0e0e0';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(0, tabBarY);
    this.ctx.lineTo(this.canvas.width, tabBarY);
    this.ctx.stroke();
    
    // 导航项
    const tabs = [
      { name: '角色', scene: 'character' },
      { name: '武功', scene: 'skills' },
      { name: '江湖', scene: 'index' },
      { name: '市场', scene: 'shop' },
      { name: '门派', scene: 'guild' }
    ];
    
    tabs.forEach((tab, index) => {
      const x = index * tabWidth;
      const isActive = this.currentScene === tab.scene;
      
      // 绘制标签文本
      this.drawText(tab.name, x + tabWidth / 2, tabBarY + tabBarHeight / 2 + 6, {
        fontSize: this.uiConfig.fontSize.small,
        color: isActive ? this.uiConfig.colors.primary : '#999',
        align: 'center'
      });
    });
    
    return { y: tabBarY, height: tabBarHeight, tabWidth };
  }
  
  // 检查点击是否在区域内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  }

  // 创建登录场景
  createLoginScene() {
    return () => {
      this.clearCanvas();

      // 绘制背景渐变
      const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(1, '#764ba2');
      this.ctx.fillStyle = gradient;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

      // 绘制游戏标题
      this.drawText('仗剑江湖行', this.canvas.width / 2, 120, {
        fontSize: 36,
        color: this.uiConfig.colors.white,
        align: 'center',
        bold: true
      });

      // 绘制副标题
      this.drawText('武侠点击式游戏', this.canvas.width / 2, 160, {
        fontSize: 18,
        color: this.uiConfig.colors.white,
        align: 'center'
      });

      // 初始化登录状态
      if (!this.loginState) {
        this.loginState = {
          isRegistering: false,
          isLoading: false,
          loginForm: { username: '', password: '' },
          registerForm: { username: '', password: '', confirmPassword: '', characterName: '', gender: 'male' }
        };

        // 检查自动登录
        this.checkAutoLogin();
      }

      if (this.loginState.isLoading) {
        this.drawLoadingScreen();
      } else if (this.loginState.isRegistering) {
        this.drawRegisterForm();
      } else {
        this.drawLoginForm();
      }
    };
  }

  // 绘制登录表单
  drawLoginForm() {
    const formY = 220;
    const formWidth = this.canvas.width - 80;
    const formX = (this.canvas.width - formWidth) / 2;

    // 绘制表单背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    this.ctx.fillRect(formX, formY, formWidth, 280);

    // 绘制表单标题
    this.drawText('登录江湖', this.canvas.width / 2, formY + 40, {
      fontSize: 24,
      color: this.uiConfig.colors.text,
      align: 'center',
      bold: true
    });

    // 绘制账号输入框
    this.drawText('账号', formX + 20, formY + 80, {
      fontSize: 16,
      color: this.uiConfig.colors.text
    });

    const usernameInputY = formY + 100;
    this.drawInputField(formX + 20, usernameInputY, formWidth - 40, 40,
      this.loginState.loginForm.username || '请输入账号', 'username');

    // 绘制密码输入框
    this.drawText('密码', formX + 20, formY + 160, {
      fontSize: 16,
      color: this.uiConfig.colors.text
    });

    const passwordInputY = formY + 180;
    this.drawInputField(formX + 20, passwordInputY, formWidth - 40, 40,
      this.loginState.loginForm.password ? '••••••••' : '请输入密码', 'password');

    // 绘制登录按钮
    const loginButtonY = formY + 240;
    this.loginButton = this.drawButton('登录', formX + 20, loginButtonY, formWidth - 40, 45, {
      backgroundColor: this.uiConfig.colors.primary,
      fontSize: 18
    });

    // 绘制注册链接
    this.drawText('还没有账号？', formX + 20, formY + 320, {
      fontSize: 14,
      color: '#666'
    });

    const registerLinkX = formX + 120;
    this.drawText('立即注册', registerLinkX, formY + 320, {
      fontSize: 14,
      color: this.uiConfig.colors.primary
    });

    this.registerLink = {
      x: registerLinkX - 10,
      y: formY + 305,
      width: 80,
      height: 20
    };
  }

  // 绘制注册表单
  drawRegisterForm() {
    const formY = 200;
    const formWidth = this.canvas.width - 60;
    const formX = (this.canvas.width - formWidth) / 2;

    // 绘制表单背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    this.ctx.fillRect(formX, formY, formWidth, 400);

    // 绘制表单标题
    this.drawText('创建角色', this.canvas.width / 2, formY + 30, {
      fontSize: 24,
      color: this.uiConfig.colors.text,
      align: 'center',
      bold: true
    });

    // 绘制各个输入字段
    let currentY = formY + 70;

    // 账号
    this.drawText('账号', formX + 15, currentY, { fontSize: 14 });
    currentY += 20;
    this.drawInputField(formX + 15, currentY, formWidth - 30, 35,
      this.loginState.registerForm.username || '请输入账号（3-20位字符）', 'reg_username');
    currentY += 50;

    // 密码
    this.drawText('密码', formX + 15, currentY, { fontSize: 14 });
    currentY += 20;
    this.drawInputField(formX + 15, currentY, formWidth - 30, 35,
      this.loginState.registerForm.password ? '••••••••' : '请输入密码（6-20位字符）', 'reg_password');
    currentY += 50;

    // 确认密码
    this.drawText('确认密码', formX + 15, currentY, { fontSize: 14 });
    currentY += 20;
    this.drawInputField(formX + 15, currentY, formWidth - 30, 35,
      this.loginState.registerForm.confirmPassword ? '••••••••' : '请再次输入密码', 'reg_confirm');
    currentY += 50;

    // 角色名
    this.drawText('角色名', formX + 15, currentY, { fontSize: 14 });
    currentY += 20;
    this.drawInputField(formX + 15, currentY, formWidth - 30, 35,
      this.loginState.registerForm.characterName || '请输入角色名（2-10位字符）', 'reg_character');
    currentY += 50;

    // 性别选择
    this.drawText('性别', formX + 15, currentY, { fontSize: 14 });
    currentY += 25;

    const genderButtonWidth = 80;
    const maleButtonX = formX + 15;
    const femaleButtonX = formX + 15 + genderButtonWidth + 20;

    this.maleButton = this.drawButton('男', maleButtonX, currentY, genderButtonWidth, 35, {
      backgroundColor: this.loginState.registerForm.gender === 'male' ? this.uiConfig.colors.primary : '#f0f0f0',
      textColor: this.loginState.registerForm.gender === 'male' ? this.uiConfig.colors.white : this.uiConfig.colors.text,
      fontSize: 16
    });

    this.femaleButton = this.drawButton('女', femaleButtonX, currentY, genderButtonWidth, 35, {
      backgroundColor: this.loginState.registerForm.gender === 'female' ? this.uiConfig.colors.primary : '#f0f0f0',
      textColor: this.loginState.registerForm.gender === 'female' ? this.uiConfig.colors.white : this.uiConfig.colors.text,
      fontSize: 16
    });

    currentY += 55;

    // 注册按钮
    this.registerButton = this.drawButton('创建角色', formX + 15, currentY, formWidth - 30, 45, {
      backgroundColor: this.uiConfig.colors.success,
      fontSize: 18
    });

    currentY += 60;

    // 返回登录链接
    this.drawText('已有账号？', formX + 15, currentY, {
      fontSize: 14,
      color: '#666'
    });

    const backLinkX = formX + 85;
    this.drawText('返回登录', backLinkX, currentY, {
      fontSize: 14,
      color: this.uiConfig.colors.primary
    });

    this.backToLoginLink = {
      x: backLinkX - 10,
      y: currentY - 15,
      width: 80,
      height: 20
    };
  }

  // 绘制加载屏幕
  drawLoadingScreen() {
    // 绘制半透明遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // 绘制加载文本
    this.drawText('正在登录...', this.canvas.width / 2, this.canvas.height / 2, {
      fontSize: 20,
      color: this.uiConfig.colors.white,
      align: 'center'
    });
  }

  // 绘制输入框
  drawInputField(x, y, width, height, placeholder, fieldName) {
    // 绘制输入框背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(x, y, width, height);

    // 绘制边框
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制占位符或内容
    this.drawText(placeholder, x + 10, y + height / 2 + 5, {
      fontSize: 14,
      color: placeholder.includes('请') ? '#999' : this.uiConfig.colors.text
    });

    // 存储输入框信息用于触摸检测
    if (!this.inputFields) this.inputFields = {};
    this.inputFields[fieldName] = { x, y, width, height };
  }

  // 创建登录场景触摸处理器
  createLoginTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        if (this.loginState.isLoading) return;

        if (this.loginState.isRegistering) {
          // 注册界面的触摸处理
          if (this.registerButton && this.isPointInRect(x, y, this.registerButton)) {
            this.handleRegister();
          } else if (this.maleButton && this.isPointInRect(x, y, this.maleButton)) {
            this.loginState.registerForm.gender = 'male';
            this.showScene('login');
          } else if (this.femaleButton && this.isPointInRect(x, y, this.femaleButton)) {
            this.loginState.registerForm.gender = 'female';
            this.showScene('login');
          } else if (this.backToLoginLink && this.isPointInRect(x, y, this.backToLoginLink)) {
            this.loginState.isRegistering = false;
            this.showScene('login');
          } else if (this.inputFields) {
            // 处理输入框点击
            this.handleInputFieldClick(x, y);
          }
        } else {
          // 登录界面的触摸处理
          if (this.loginButton && this.isPointInRect(x, y, this.loginButton)) {
            this.handleLogin();
          } else if (this.registerLink && this.isPointInRect(x, y, this.registerLink)) {
            this.loginState.isRegistering = true;
            this.showScene('login');
          } else if (this.inputFields) {
            // 处理输入框点击
            this.handleInputFieldClick(x, y);
          }
        }
      }
    };
  }

  // 创建主页场景
  createIndexScene() {
    return () => {
      this.clearCanvas();
      this.drawHeader('仗剑江湖行');

      const contentY = this.uiConfig.headerHeight + 20;
      const tabBarInfo = this.drawTabBar();
      const contentHeight = tabBarInfo.y - contentY - 20;

      // 绘制玩家信息区域
      this.drawPlayerInfo(20, contentY, this.canvas.width - 40, 120);

      // 绘制闯江湖按钮
      const adventureButtonY = contentY + 140;
      const adventureButtonHeight = 80;
      this.drawAdventureButton(20, adventureButtonY, this.canvas.width - 40, adventureButtonHeight);

      // 绘制最近事件
      const eventsY = adventureButtonY + adventureButtonHeight + 20;
      const eventsHeight = contentHeight - (eventsY - contentY);
      this.drawRecentEvents(20, eventsY, this.canvas.width - 40, eventsHeight);
    };
  }

  // 创建主页触摸处理器
  createIndexTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        // 检查底部导航栏点击
        const tabBarInfo = this.drawTabBar();
        if (y >= tabBarInfo.y) {
          const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
          const scenes = ['character', 'skills', 'index', 'shop', 'guild'];
          if (tabIndex >= 0 && tabIndex < scenes.length) {
            this.showScene(scenes[tabIndex]);
          }
        }

        // 检查闯江湖按钮点击
        if (this.adventureButton && this.isPointInRect(x, y, this.adventureButton)) {
          this.startAdventure();
        }
      }
    };
  }

  // 绘制玩家信息
  drawPlayerInfo(x, y, width, height) {
    // 绘制背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(x, y, width, height);

    // 绘制边框
    this.ctx.strokeStyle = '#e0e0e0';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制玩家信息（模拟数据）
    this.drawText('玩家信息', x + 15, y + 25, {
      fontSize: 18,
      bold: true
    });

    this.drawText('等级: 1', x + 15, y + 50);
    this.drawText('气血: 100/100', x + 15, y + 70);
    this.drawText('内力: 50/50', x + 15, y + 90);

    this.drawText('攻击: 10', x + width / 2, y + 50);
    this.drawText('防御: 5', x + width / 2, y + 70);
    this.drawText('声望: 0', x + width / 2, y + 90);
  }

  // 绘制闯江湖按钮
  drawAdventureButton(x, y, width, height) {
    this.drawButton('闯江湖', x, y, width, height, {
      backgroundColor: this.uiConfig.colors.danger,
      fontSize: 24
    });

    this.adventureButton = { x, y, width, height };
  }

  // 绘制最近事件
  drawRecentEvents(x, y, width, height) {
    // 绘制背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(x, y, width, height);

    // 绘制边框
    this.ctx.strokeStyle = '#e0e0e0';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制标题
    this.drawText('最近事件', x + 15, y + 25, {
      fontSize: 18,
      bold: true
    });

    // 绘制事件列表（模拟数据）
    const events = [
      '你在江湖中漫步，感受着武侠世界的魅力',
      '遇到了一个善良的商人，获得了一些银两',
      '发现了一片资源丰富的区域'
    ];

    events.forEach((event, index) => {
      this.drawText(`• ${event}`, x + 15, y + 55 + index * 25, {
        fontSize: 14,
        color: '#666'
      });
    });
  }

  // 开始冒险
  startAdventure() {
    console.log('开始闯江湖');
    // 这里可以调用 WebSocket 发送冒险请求
    if (this.wsManager && typeof this.wsManager.send === 'function') {
      this.wsManager.send({
        type: 'adventure',
        action: 'start'
      });
    }
  }

  // 创建角色场景
  createCharacterScene() {
    return () => {
      this.clearCanvas();
      this.drawHeader('角色信息');

      const contentY = this.uiConfig.headerHeight + 20;
      const tabBarInfo = this.drawTabBar();

      // 绘制角色详细信息
      this.drawCharacterDetails(20, contentY, this.canvas.width - 40);
    };
  }

  // 创建角色场景触摸处理器
  createCharacterTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        // 检查底部导航栏点击
        const tabBarInfo = this.drawTabBar();
        if (y >= tabBarInfo.y) {
          const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
          const scenes = ['character', 'skills', 'index', 'shop', 'guild'];
          if (tabIndex >= 0 && tabIndex < scenes.length) {
            this.showScene(scenes[tabIndex]);
          }
        }
      }
    };
  }

  // 绘制角色详细信息
  drawCharacterDetails(x, y, width) {
    const sectionHeight = 150;
    let currentY = y;

    // 基础属性
    this.drawInfoSection('基础属性', x, currentY, width, sectionHeight, [
      '等级: 1',
      '经验: 0/100',
      '气血: 100/100',
      '内力: 50/50',
      '体力: 100/100'
    ]);

    currentY += sectionHeight + 20;

    // 战斗属性
    this.drawInfoSection('战斗属性', x, currentY, width, sectionHeight, [
      '攻击: 10',
      '防御: 5',
      '命中: 80%',
      '闪避: 10%',
      '暴击: 5%'
    ]);
  }

  // 创建武功场景
  createSkillsScene() {
    return () => {
      this.clearCanvas();
      this.drawHeader('武功修炼');

      const contentY = this.uiConfig.headerHeight + 20;
      const tabBarInfo = this.drawTabBar();

      // 绘制武功列表
      this.drawSkillsList(20, contentY, this.canvas.width - 40);
    };
  }

  // 创建武功场景触摸处理器
  createSkillsTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        // 检查底部导航栏点击
        const tabBarInfo = this.drawTabBar();
        if (y >= tabBarInfo.y) {
          const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
          const scenes = ['character', 'skills', 'index', 'shop', 'guild'];
          if (tabIndex >= 0 && tabIndex < scenes.length) {
            this.showScene(scenes[tabIndex]);
          }
        }
      }
    };
  }

  // 绘制武功列表
  drawSkillsList(x, y, width) {
    // 绘制武功分类按钮
    const buttonHeight = 40;
    const buttonSpacing = 10;
    const categories = ['内功', '外功', '轻功', '医术'];

    categories.forEach((category, index) => {
      const buttonY = y + index * (buttonHeight + buttonSpacing);
      this.drawButton(category, x, buttonY, width, buttonHeight, {
        backgroundColor: this.uiConfig.colors.secondary
      });
    });

    // 绘制武功详情区域
    const detailsY = y + categories.length * (buttonHeight + buttonSpacing) + 20;
    this.drawInfoSection('当前武功', x, detailsY, width, 120, [
      '基础内功 (黄级)',
      '等级: 1/10',
      '效果: 增加内力上限',
      '修炼进度: 0/100'
    ]);
  }

  // 创建商店场景
  createShopScene() {
    return () => {
      this.clearCanvas();
      this.drawHeader('江湖市场');

      const contentY = this.uiConfig.headerHeight + 20;
      const tabBarInfo = this.drawTabBar();

      // 绘制商店内容
      this.drawShopContent(20, contentY, this.canvas.width - 40);
    };
  }

  // 创建商店场景触摸处理器
  createShopTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        // 检查底部导航栏点击
        const tabBarInfo = this.drawTabBar();
        if (y >= tabBarInfo.y) {
          const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
          const scenes = ['character', 'skills', 'index', 'shop', 'guild'];
          if (tabIndex >= 0 && tabIndex < scenes.length) {
            this.showScene(scenes[tabIndex]);
          }
        }
      }
    };
  }

  // 绘制商店内容
  drawShopContent(x, y, width) {
    // 绘制银两信息
    this.drawText('银两: 1000', x, y + 20, {
      fontSize: 18,
      bold: true
    });

    // 绘制商品列表
    const itemHeight = 60;
    const items = [
      { name: '小还丹', price: 50, description: '恢复50点气血' },
      { name: '铁剑', price: 100, description: '攻击+5' },
      { name: '布衣', price: 80, description: '防御+3' }
    ];

    items.forEach((item, index) => {
      const itemY = y + 60 + index * (itemHeight + 10);
      this.drawShopItem(item, x, itemY, width, itemHeight);
    });
  }

  // 绘制商店物品
  drawShopItem(item, x, y, width, height) {
    // 绘制背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(x, y, width, height);

    // 绘制边框
    this.ctx.strokeStyle = '#e0e0e0';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制物品信息
    this.drawText(item.name, x + 15, y + 20, {
      fontSize: 16,
      bold: true
    });

    this.drawText(item.description, x + 15, y + 40, {
      fontSize: 14,
      color: '#666'
    });

    this.drawText(`${item.price}银`, x + width - 80, y + 30, {
      fontSize: 16,
      color: this.uiConfig.colors.warning,
      bold: true
    });
  }

  // 创建门派场景
  createGuildScene() {
    return () => {
      this.clearCanvas();
      this.drawHeader('门派');

      const contentY = this.uiConfig.headerHeight + 20;
      const tabBarInfo = this.drawTabBar();

      // 绘制门派信息
      this.drawGuildInfo(20, contentY, this.canvas.width - 40);
    };
  }

  // 创建门派场景触摸处理器
  createGuildTouchHandler() {
    return (x, y, type) => {
      if (type === 'end') {
        // 检查底部导航栏点击
        const tabBarInfo = this.drawTabBar();
        if (y >= tabBarInfo.y) {
          const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
          const scenes = ['character', 'skills', 'index', 'shop', 'guild'];
          if (tabIndex >= 0 && tabIndex < scenes.length) {
            this.showScene(scenes[tabIndex]);
          }
        }
      }
    };
  }

  // 绘制门派信息
  drawGuildInfo(x, y, width) {
    this.drawInfoSection('门派状态', x, y, width, 100, [
      '当前门派: 无门无派',
      '门派贡献: 0',
      '门派等级: -',
      '可学武功: 无'
    ]);

    // 绘制加入门派按钮
    const buttonY = y + 120;
    this.drawButton('寻找门派', x, buttonY, width, 50, {
      backgroundColor: this.uiConfig.colors.primary
    });
  }

  // 绘制信息区域
  drawInfoSection(title, x, y, width, height, items) {
    // 绘制背景
    this.ctx.fillStyle = this.uiConfig.colors.white;
    this.ctx.fillRect(x, y, width, height);

    // 绘制边框
    this.ctx.strokeStyle = '#e0e0e0';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制标题
    this.drawText(title, x + 15, y + 25, {
      fontSize: 18,
      bold: true
    });

    // 绘制信息项
    items.forEach((item, index) => {
      this.drawText(item, x + 15, y + 50 + index * 20, {
        fontSize: 14
      });
    });
  }
}

// 导出适配器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MiniGameAdapter;
} else if (typeof window !== 'undefined') {
  window.MiniGameAdapter = MiniGameAdapter;
}
