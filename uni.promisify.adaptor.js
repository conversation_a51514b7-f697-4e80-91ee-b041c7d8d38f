// 小游戏兼容性补丁已在 mini-game-patch.js 中应用

uni.addInterceptor({
  returnValue (res) {
    if (!(!!res && (typeof res === "object" || typeof res === "function") && typeof res.then === "function")) {
      return res;
    }
    return new Promise((resolve, reject) => {
      res.then((res) => {
        if (!res) return resolve(res)
        return res[0] ? reject(res[0]) : resolve(res[1])
      });
    });
  },
});