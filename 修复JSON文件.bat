@echo off
chcp 65001 >nul
echo ========================================
echo        Fix JSON Files for Mini-Game
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo ERROR: Build directory not found!
    pause
    exit /b 1
)

echo Fixing app.json...

REM Create a proper app.json file
echo {> "%SOURCE_DIR%\app.json"
echo   "pages": [>> "%SOURCE_DIR%\app.json"
echo     "pages/login/login",>> "%SOURCE_DIR%\app.json"
echo     "pages/index/index",>> "%SOURCE_DIR%\app.json"
echo     "pages/character/character",>> "%SOURCE_DIR%\app.json"
echo     "pages/skills/skills",>> "%SOURCE_DIR%\app.json"
echo     "pages/shop/shop",>> "%SOURCE_DIR%\app.json"
echo     "pages/guild/guild",>> "%SOURCE_DIR%\app.json"
echo     "pages/character/backpack",>> "%SOURCE_DIR%\app.json"
echo     "pages/crafting/crafting">> "%SOURCE_DIR%\app.json"
echo   ],>> "%SOURCE_DIR%\app.json"
echo   "window": {>> "%SOURCE_DIR%\app.json"
echo     "navigationBarTextStyle": "white",>> "%SOURCE_DIR%\app.json"
echo     "navigationBarTitleText": "仗剑江湖行",>> "%SOURCE_DIR%\app.json"
echo     "navigationBarBackgroundColor": "#2c3e50",>> "%SOURCE_DIR%\app.json"
echo     "backgroundColor": "#f5f5f5">> "%SOURCE_DIR%\app.json"
echo   },>> "%SOURCE_DIR%\app.json"
echo   "tabBar": {>> "%SOURCE_DIR%\app.json"
echo     "color": "#7A7E83",>> "%SOURCE_DIR%\app.json"
echo     "selectedColor": "#ff6b6b",>> "%SOURCE_DIR%\app.json"
echo     "borderStyle": "black",>> "%SOURCE_DIR%\app.json"
echo     "backgroundColor": "#ffffff",>> "%SOURCE_DIR%\app.json"
echo     "list": [>> "%SOURCE_DIR%\app.json"
echo       {>> "%SOURCE_DIR%\app.json"
echo         "pagePath": "pages/character/character",>> "%SOURCE_DIR%\app.json"
echo         "text": "角色",>> "%SOURCE_DIR%\app.json"
echo         "iconPath": "static/tabbar/backpack.png",>> "%SOURCE_DIR%\app.json"
echo         "selectedIconPath": "static/tabbar/backpack-active.png">> "%SOURCE_DIR%\app.json"
echo       },>> "%SOURCE_DIR%\app.json"
echo       {>> "%SOURCE_DIR%\app.json"
echo         "pagePath": "pages/skills/skills",>> "%SOURCE_DIR%\app.json"
echo         "text": "武功",>> "%SOURCE_DIR%\app.json"
echo         "iconPath": "static/tabbar/skills.png",>> "%SOURCE_DIR%\app.json"
echo         "selectedIconPath": "static/tabbar/skills-active.png">> "%SOURCE_DIR%\app.json"
echo       },>> "%SOURCE_DIR%\app.json"
echo       {>> "%SOURCE_DIR%\app.json"
echo         "pagePath": "pages/index/index",>> "%SOURCE_DIR%\app.json"
echo         "text": "江湖",>> "%SOURCE_DIR%\app.json"
echo         "iconPath": "static/tabbar/adventure.png",>> "%SOURCE_DIR%\app.json"
echo         "selectedIconPath": "static/tabbar/adventure-active.png">> "%SOURCE_DIR%\app.json"
echo       },>> "%SOURCE_DIR%\app.json"
echo       {>> "%SOURCE_DIR%\app.json"
echo         "pagePath": "pages/shop/shop",>> "%SOURCE_DIR%\app.json"
echo         "text": "市场",>> "%SOURCE_DIR%\app.json"
echo         "iconPath": "static/tabbar/shop.png",>> "%SOURCE_DIR%\app.json"
echo         "selectedIconPath": "static/tabbar/shop-active.png">> "%SOURCE_DIR%\app.json"
echo       },>> "%SOURCE_DIR%\app.json"
echo       {>> "%SOURCE_DIR%\app.json"
echo         "pagePath": "pages/guild/guild",>> "%SOURCE_DIR%\app.json"
echo         "text": "门派",>> "%SOURCE_DIR%\app.json"
echo         "iconPath": "static/tabbar/guild.png",>> "%SOURCE_DIR%\app.json"
echo         "selectedIconPath": "static/tabbar/guild-active.png">> "%SOURCE_DIR%\app.json"
echo       }>> "%SOURCE_DIR%\app.json"
echo     ]>> "%SOURCE_DIR%\app.json"
echo   },>> "%SOURCE_DIR%\app.json"
echo   "gamePlugin": true,>> "%SOURCE_DIR%\app.json"
echo   "plugins": {},>> "%SOURCE_DIR%\app.json"
echo   "usingComponents": {}>> "%SOURCE_DIR%\app.json"
echo }>> "%SOURCE_DIR%\app.json"

echo Verifying game.json...
if not exist "%SOURCE_DIR%\game.json" (
    echo Creating game.json...
    echo {> "%SOURCE_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
    echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
    echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
    echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
    echo   },>> "%SOURCE_DIR%\game.json"
    echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
    echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
    echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
    echo   "resizable": false>> "%SOURCE_DIR%\game.json"
    echo }>> "%SOURCE_DIR%\game.json"
)

echo.
echo ========================================
echo JSON Files Fixed!
echo ========================================
echo.
echo Files created/updated:
echo - app.json (proper JSON format)
echo - game.json (mini-game configuration)
echo.
echo You can now refresh the project in WeChat DevTools
echo.
pause
