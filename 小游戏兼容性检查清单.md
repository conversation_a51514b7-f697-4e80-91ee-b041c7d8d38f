# 小游戏兼容性问题检查清单

## 已解决的问题 ✅

### 1. wx.canIUse 未定义
**错误**: `TypeError: wx$2.canIUse is not a function`
**解决**: 在 API 补丁中实现了 `wx.canIUse` 方法

### 2. Page 未定义
**错误**: `ReferenceError: Page is not defined`
**解决**: 添加了 `Page` 全局函数的兼容性实现

### 3. global 未定义
**错误**: `ReferenceError: global is not defined`
**解决**: 使用跨平台的全局对象获取方法

### 4. Component 未定义
**解决**: 添加了 `Component` 全局函数的兼容性实现

### 5. App 未定义
**解决**: 添加了 `App` 全局函数的兼容性实现

### 6. getApp 未定义
**解决**: 添加了 `getApp` 全局函数的兼容性实现

### 7. getCurrentPages 未定义
**解决**: 添加了 `getCurrentPages` 全局函数的兼容性实现

## 当前 API 补丁内容

```javascript
// 小游戏 API 兼容性补丁
if (typeof wx !== 'undefined') {
  // 1. wx.canIUse 方法
  if (!wx.canIUse) {
    wx.canIUse = function(apiName) {
      const gameAPIs = [
        'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
        'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
        'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
        'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
        'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage'
      ];
      return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
    };
  }

  // 2. 基础 API 兼容性
  if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
    wx.getAppBaseInfo = wx.getSystemInfoSync;
  }
  if (!wx.getWindowInfo && wx.getSystemInfoSync) {
    wx.getWindowInfo = wx.getSystemInfoSync;
  }
  if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
    wx.getDeviceInfo = wx.getSystemInfoSync;
  }
  if (!wx.getSystemSetting && wx.getSystemInfoSync) {
    wx.getSystemSetting = wx.getSystemInfoSync;
  }
  if (!wx.getAppAuthorizeSetting) {
    wx.getAppAuthorizeSetting = function() {
      return {
        albumAuthorized: 'authorized',
        bluetoothAuthorized: 'authorized',
        cameraAuthorized: 'authorized',
        locationAuthorized: 'authorized',
        locationReducedAccuracy: false,
        microphoneAuthorized: 'authorized',
        notificationAuthorized: 'authorized'
      };
    };
  }

  // 3. 全局对象获取
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  // 4. 全局函数兼容性
  if (typeof Page === 'undefined') {
    globalObj.Page = function(options) { return options; };
  }
  if (typeof Component === 'undefined') {
    globalObj.Component = function(options) { return options; };
  }
  if (typeof App === 'undefined') {
    globalObj.App = function(options) { return options; };
  }
  if (typeof getApp === 'undefined') {
    globalObj.getApp = function(options) {
      return { $vm: null, globalData: {} };
    };
  }
  if (typeof getCurrentPages === 'undefined') {
    globalObj.getCurrentPages = function() { return []; };
  }
}
```

## 可能的其他问题 ⚠️

### 1. 导航 API
小游戏中可能不支持页面导航 API：
- `wx.navigateTo`
- `wx.redirectTo`
- `wx.switchTab`
- `wx.reLaunch`
- `wx.navigateBack`

### 2. 分享 API
小游戏的分享机制可能不同：
- `wx.showShareMenu`
- `wx.hideShareMenu`
- `wx.updateShareMenu`

### 3. 支付 API
小游戏的支付流程可能不同：
- `wx.requestPayment`

## 监控方法

在微信开发者工具控制台中查看：
1. 是否有新的 `ReferenceError` 或 `TypeError`
2. 是否有 API 调用失败的警告
3. 功能是否正常工作

## 调试建议

1. **逐步测试**: 先测试基础功能，再测试复杂功能
2. **查看日志**: 关注控制台的错误和警告信息
3. **功能验证**: 确保游戏的核心功能正常工作
4. **性能监控**: 注意小游戏的性能表现

## 成功标志

当看到以下日志时，说明补丁正常工作：
```
应用小游戏 API 兼容性补丁
小游戏 API 补丁应用完成
```

如果出现新的错误，请根据错误信息添加相应的兼容性补丁。
