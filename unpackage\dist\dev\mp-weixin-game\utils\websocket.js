"use strict";
const common_vendor = require("../common/vendor.js");
class WebSocketManager {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isAuthed = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = -1;
    this.reconnectInterval = 3e3;
    this.maxReconnectInterval = 3e4;
    this.messageQueue = [];
    this.eventHandlers = {};
    this.connecting = false;
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
    this.manualDisconnect = false;
    this.serverUrl = "ws://localhost:8080";
    this.disableAutoAuth = false;
    this.isLoginPage = false;
    this.debug = false;
    this.connectPromise = null;
    this.on("login_success", (data) => {
      this.isAuthed = true;
      this.log("WebSocket: 登录成功，认证状态已更新");
    });
    this.on("auth_success", (data) => {
      this.isAuthed = true;
      this.log("WebSocket: 认证成功，认证状态已更新");
      try {
        const gameState = require("./gameState").default || require("./gameState");
        if (gameState && typeof gameState.requestAllData === "function") {
          this.log("认证成功，自动刷新全局数据");
          gameState.requestAllData();
        }
      } catch (e) {
        this.error("自动刷新全局数据失败:", e);
      }
    });
    this.on("login_failed", (data) => {
      this.isAuthed = false;
      this.log("WebSocket: 登录失败，认证状态已重置");
    });
    this.on("auth_failed", (data) => {
      this.isAuthed = false;
      this.log("WebSocket: 认证失败，认证状态已重置");
    });
  }
  // 日志方法
  log(message, ...args) {
  }
  // 新增：重连后自动跳转Index页面并广播关闭弹窗事件
  triggerReconnectActions() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (!currentPage || !currentPage.route || !currentPage.route.includes("pages/index/index")) {
      common_vendor.index.reLaunch({ url: "/pages/index/index" });
    }
    common_vendor.index.$emit && common_vendor.index.$emit("ws_reconnected");
  }
  // 错误日志方法
  error(message, ...args) {
    console.error(`[WebSocket错误] ${message}`, ...args);
  }
  /**
   * 连接到WebSocket服务器（防止重复连接，彻底断开旧连接）
   */
  connect() {
    if (this.isConnected) {
      this.log("WebSocket已连接，无需重新连接");
      return Promise.resolve();
    }
    if (this.connecting) {
      this.log("WebSocket正在连接中，请等待...");
      return this.connectPromise || Promise.reject(new Error("正在连接中"));
    }
    this.connecting = true;
    this.connectPromise = new Promise((resolve, reject) => {
      try {
        this.log("开始连接WebSocket服务器:", this.serverUrl);
        this.ws = common_vendor.index.connectSocket({
          url: this.serverUrl,
          success: () => {
            this.log("WebSocket连接请求已发送");
          },
          fail: (error) => {
            this.error("WebSocket连接请求失败:", error);
            this.connecting = false;
            this.connectPromise = null;
            reject(error);
          }
        });
        common_vendor.index.onSocketOpen((res) => {
          this.isConnected = true;
          this.connecting = false;
          this.connectPromise = null;
          this.reconnectAttempts = 0;
          this.log("WebSocket连接已建立");
          this.processMessageQueue();
          this.startHeartbeat();
          if (this.eventHandlers["connected"]) {
            this.eventHandlers["connected"].forEach((fn) => fn());
          }
          this.triggerReconnectActions();
          if (!this.isLoginPage && !this.disableAutoAuth) {
            this.log("准备执行自动认证...");
            setTimeout(() => {
              this.autoAuthenticate();
            }, 500);
          } else {
            this.log("当前在登录页面或自动认证已禁用，跳过自动认证");
          }
          resolve(res);
        });
        common_vendor.index.onSocketClose((res) => {
          this.isConnected = false;
          this.connecting = false;
          this.connectPromise = null;
          this.stopHeartbeat();
          if (this.eventHandlers["disconnected"]) {
            this.eventHandlers["disconnected"].forEach((fn) => fn());
          }
          this.log("WebSocket连接断开，code:", res.code, "reason:", res.reason);
          if (!this.manualDisconnect && res.code !== 1e3) {
            this.log("检测到异常断开，开始重连...");
            this.handleDisconnect();
          } else if (this.manualDisconnect) {
            this.log("手动断开连接，不进行重连");
            this.manualDisconnect = false;
          }
        });
        common_vendor.index.onSocketError((res) => {
          this.isConnected = false;
          this.connecting = false;
          this.connectPromise = null;
          this.error("WebSocket连接错误:", res);
          if (this.eventHandlers["error"]) {
            this.eventHandlers["error"].forEach((fn) => fn(res));
          }
          this.handleDisconnect();
          reject(res);
        });
        common_vendor.index.onSocketMessage((res) => {
          try {
            const message = JSON.parse(res.data);
            this.log("收到消息:", message);
            if (message.type === "pong") {
              this.handlePong();
              return;
            }
            this.triggerEvent(message.type, message.data);
          } catch (error) {
            this.error("消息解析失败:", error, res.data);
          }
        });
        setTimeout(() => {
          if (!this.isConnected && this.connecting) {
            this.connecting = false;
            this.connectPromise = null;
            this.error("WebSocket连接超时");
            reject(new Error("连接超时"));
          }
        }, 15e3);
      } catch (error) {
        this.connecting = false;
        this.connectPromise = null;
        this.error("WebSocket连接异常:", error);
        reject(error);
      }
    });
    return this.connectPromise;
  }
  /**
   * 设置当前是否在登录页面
   */
  setLoginPageStatus(isLoginPage) {
    this.isLoginPage = isLoginPage;
    this.log("设置登录页面状态:", isLoginPage ? "当前在登录页面" : "当前不在登录页面");
  }
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.ws && this.isConnected) {
      this.log("手动断开WebSocket连接");
      common_vendor.index.closeSocket();
      this.isConnected = false;
      this.connecting = false;
      this.manualDisconnect = true;
    }
  }
  /**
   * 处理连接断开
   */
  handleDisconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts && this.maxReconnectAttempts !== -1) {
      this.log("达到最大重连次数，停止重连");
      return;
    }
    const delay = Math.min(
      this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts),
      this.maxReconnectInterval
    );
    this.reconnectAttempts++;
    this.log(`第${this.reconnectAttempts}次重连，延迟${delay}ms`);
    setTimeout(() => {
      if (!this.isConnected && !this.connecting) {
        this.log("执行重连...");
        this.connect().catch((error) => {
          this.error("重连失败:", error);
        });
      }
    }, delay);
  }
  /**
   * 发送消息
   */
  sendMessage(type, data = {}) {
    const message = JSON.stringify({ type, data });
    if (!this.isConnected) {
      this.log("WebSocket未连接，消息加入队列:", { type, data });
      this.messageQueue.push(message);
      return Promise.reject(new Error("WebSocket未连接"));
    }
    return new Promise((resolve, reject) => {
      this.log("发送消息:", { type, data });
      common_vendor.index.sendSocketMessage({
        data: message,
        success: () => {
          this.log("消息发送成功:", { type, data });
          resolve({ type, data });
        },
        fail: (error) => {
          this.error("消息发送失败:", error, { type, data });
          this.messageQueue.push(message);
          reject(error);
        }
      });
    });
  }
  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      return;
    }
    this.log(`处理消息队列，共${this.messageQueue.length}条消息`);
    const queue = [...this.messageQueue];
    this.messageQueue = [];
    queue.forEach((message) => {
      try {
        common_vendor.index.sendSocketMessage({
          data: message,
          success: () => {
            this.log("队列消息发送成功:", message);
          },
          fail: (error) => {
            this.error("队列消息发送失败:", error, message);
            this.messageQueue.unshift(message);
          }
        });
      } catch (error) {
        this.error("队列消息发送失败:", error, message);
        this.messageQueue.unshift(message);
      }
    });
  }
  /**
   * 触发事件
   */
  triggerEvent(eventType, data) {
    if (eventType === "game_event") {
      try {
        const gameState = require("./gameState").default;
        if (gameState && typeof gameState.handleGameEvent === "function") {
          this.log(`特殊处理game_event事件`, data);
          const eventData = {
            type: data.type || 5,
            // 默认为奇遇事件
            content: data.content || data.description || "你遇到了一个江湖事件",
            rewards: data.rewards || {},
            realm_breakthrough: data.realm_breakthrough || null
          };
          gameState.handleGameEvent(eventData);
        }
      } catch (error) {
        this.error("处理game_event事件失败:", error);
      }
    }
    if (this.eventHandlers[eventType]) {
      this.log(`触发事件: ${eventType}`, data);
      this.eventHandlers[eventType].forEach((handler) => handler(data));
    } else {
      this.log(`没有处理程序的事件: ${eventType}`, data);
    }
  }
  /**
   * 注册事件处理程序
   */
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
    this.log(`注册事件处理程序: ${eventType}`);
  }
  /**
   * 移除事件处理程序
   */
  off(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      return;
    }
    const idx = this.eventHandlers[eventType].indexOf(handler);
    if (idx > -1) {
      this.eventHandlers[eventType].splice(idx, 1);
    }
  }
  /**
   * 自动认证
   */
  autoAuthenticate() {
    this.log("开始自动认证...");
    const token = common_vendor.index.getStorageSync("token");
    this.log("认证信息检查:");
    this.log("- token存在:", !!token);
    if (token) {
      this.log("✅ 找到登录信息，发送认证消息");
      this.sendMessage("auth", {
        token
      });
      this.log("✅ 认证消息已发送");
    } else {
      this.log("❌ 未找到登录信息，无法自动认证");
    }
  }
  /**
   * 发送认证消息
   */
  authenticate() {
    const token = common_vendor.index.getStorageSync("token") || "";
    this.log("手动发送认证消息");
    this.sendMessage("auth", {
      token
    });
  }
  /**
   * 发送游戏动作
   */
  sendGameAction(action, data = {}) {
    this.sendMessage("game_action", {
      action,
      ...data
    });
  }
  /**
   * 请求玩家数据
   */
  requestPlayerData() {
    this.log("WebSocket: 请求玩家数据");
    this.sendMessage("get_player_data");
  }
  /**
   * 请求背包数据
   */
  requestInventoryData() {
    this.sendMessage("get_inventory_data");
  }
  /**
   * 请求武功数据
   */
  requestSkillsData() {
    this.sendMessage("get_skills_data");
  }
  /**
   * 发送闯江湖请求
   */
  sendAdventureRequest() {
    this.log("=== WebSocket: 发送闯江湖请求 ===");
    this.log("当前时间:", (/* @__PURE__ */ new Date()).toLocaleString());
    this.log("WebSocket连接状态:", this.isConnected);
    this.log("服务器地址:", this.serverUrl);
    if (!this.isConnected) {
      this.log("❌ WebSocket未连接，无法发送请求");
      return;
    }
    this.log("✅ 发送adventure消息");
    this.sendMessage("adventure");
    this.log("✅ WebSocket: 闯江湖请求已发送");
  }
  /**
   * 发送打坐请求
   */
  sendMeditationRequest() {
    this.sendMessage("meditation");
  }
  /**
   * 发送疗伤请求
   */
  sendHealingRequest() {
    this.sendMessage("healing");
  }
  /**
   * 发送装备操作
   */
  sendEquipmentAction(action, itemId) {
    this.sendMessage("equipment_action", {
      action,
      itemId
    });
  }
  /**
   * 发送武功操作
   */
  sendSkillAction(action, skillId) {
    this.sendMessage("skill_action", {
      action,
      skillId
    });
  }
  /**
   * 发送商店操作
   */
  sendShopAction(action, data) {
    this.sendMessage("shop_action", {
      action,
      ...data
    });
  }
  /**
   * 发送市场操作
   */
  sendMarketAction(action, data) {
    this.sendMessage("market_action", {
      action,
      ...data
    });
  }
  /**
   * 发送门派操作
   */
  sendGuildAction(action, data) {
    this.sendMessage("guild_action", {
      action,
      ...data
    });
  }
  /**
   * 发送打造操作
   */
  sendCraftingAction(action, data) {
    this.sendMessage("crafting_action", {
      action,
      ...data
    });
  }
  /**
   * 启动心跳机制
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage("ping");
        this.heartbeatTimeout = setTimeout(() => {
          this.log("心跳超时，断开连接");
          this.disconnect();
          setTimeout(() => {
            this.connect();
          }, 1e3);
        }, 1e4);
      }
    }, 3e5);
  }
  /**
   * 停止心跳机制
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }
  /**
   * 处理心跳响应
   */
  handlePong() {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }
}
const wsManager = new WebSocketManager();
exports.wsManager = wsManager;
