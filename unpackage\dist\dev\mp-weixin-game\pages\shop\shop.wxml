<view class="container data-v-2a6aaf81"><view class="header data-v-2a6aaf81"><view class="money-info data-v-2a6aaf81"><text class="money-label data-v-2a6aaf81">银两:</text><text class="money-value data-v-2a6aaf81">{{a}}</text></view><view class="shop-type data-v-2a6aaf81"><text class="shop-name data-v-2a6aaf81">{{b}}</text></view></view><view wx:if="{{c}}" class="market-section data-v-2a6aaf81"><view class="market-actions data-v-2a6aaf81"><button class="market-btn data-v-2a6aaf81" bindtap="{{d}}"><text class="btn-text data-v-2a6aaf81">上架物品</text></button><button class="market-btn data-v-2a6aaf81" bindtap="{{e}}"><text class="btn-text data-v-2a6aaf81">订单列表</text></button></view><scroll-view class="order-list-scroll data-v-2a6aaf81" scroll-y="true"><view class="order-list data-v-2a6aaf81"><view wx:if="{{f}}" class="market-empty data-v-2a6aaf81">暂无玩家上架物品</view><view wx:for="{{g}}" wx:for-item="order" wx:key="g" class="order-card data-v-2a6aaf81"><image class="item-img data-v-2a6aaf81" src="{{order.a}}" mode="aspectFill"/><view class="order-info data-v-2a6aaf81"><view class="item-name data-v-2a6aaf81">{{order.b}}</view><view class="item-detail data-v-2a6aaf81"><text class="data-v-2a6aaf81">单价：</text><text class="price data-v-2a6aaf81">{{order.c}}</text><text class="data-v-2a6aaf81"> 数量：</text><text class="data-v-2a6aaf81">{{order.d}}</text></view><view class="seller data-v-2a6aaf81">卖家：{{order.e}}</view></view><button class="buy-btn data-v-2a6aaf81" bindtap="{{order.f}}">购买</button></view></view></scroll-view></view><scroll-view wx:else class="goods-list data-v-2a6aaf81" scroll-y="true"><view wx:for="{{h}}" wx:for-item="item" wx:key="g" class="goods-item simple-row data-v-2a6aaf81" bindtap="{{item.h}}"><text class="item-name data-v-2a6aaf81">{{item.a}}</text><text class="item-quality data-v-2a6aaf81" style="{{'color:' + item.c}}">{{item.b}}</text><text class="item-type data-v-2a6aaf81">{{item.d}}</text><text class="item-price main-price data-v-2a6aaf81">{{item.e}} 银两</text><button class="buy-btn data-v-2a6aaf81" catchtap="{{item.f}}">购买</button></view><view wx:if="{{i}}" class="empty-goods data-v-2a6aaf81"><text class="data-v-2a6aaf81">暂无商品</text></view></scroll-view><view wx:if="{{j}}" class="modal-overlay data-v-2a6aaf81" bindtap="{{G}}"><view class="modal-content data-v-2a6aaf81" catchtap="{{F}}"><view class="modal-header data-v-2a6aaf81"><text class="modal-title data-v-2a6aaf81">商品详情</text><text class="modal-close data-v-2a6aaf81" bindtap="{{k}}">×</text></view><view wx:if="{{l}}" class="modal-body data-v-2a6aaf81"><text class="detail-name data-v-2a6aaf81">{{m}}</text><text class="detail-quality data-v-2a6aaf81" style="{{'color:' + o}}"> 品质: {{n}}</text><text class="detail-type data-v-2a6aaf81">类型: {{p}}</text><text wx:if="{{q}}" class="detail-desc data-v-2a6aaf81">{{r}}</text><view wx:if="{{s}}" class="detail-stats data-v-2a6aaf81"><text class="stats-title data-v-2a6aaf81">属性加成:</text><text wx:if="{{t}}" class="data-v-2a6aaf81">攻击: {{v}}</text><text wx:if="{{w}}" class="data-v-2a6aaf81">防御: {{x}}</text><text wx:if="{{y}}" class="data-v-2a6aaf81">气血: {{z}}</text><text wx:if="{{A}}" class="data-v-2a6aaf81">内力: {{B}}</text></view><view class="detail-price data-v-2a6aaf81"><text class="price-title data-v-2a6aaf81">价格:</text><text class="price-value data-v-2a6aaf81">{{C}} 银两</text></view></view><view class="modal-footer data-v-2a6aaf81"><button class="modal-btn cancel-btn data-v-2a6aaf81" bindtap="{{D}}">关闭</button><button class="modal-btn confirm-btn data-v-2a6aaf81" bindtap="{{E}}"> 购买 </button></view></view></view><view wx:if="{{H}}" class="modal-overlay data-v-2a6aaf81" bindtap="{{ad}}"><view class="modal-content data-v-2a6aaf81" catchtap="{{ac}}"><view class="modal-header data-v-2a6aaf81"><text class="modal-title data-v-2a6aaf81">上架物品</text><text class="modal-close data-v-2a6aaf81" bindtap="{{I}}">×</text></view><view class="modal-body data-v-2a6aaf81"><view wx:if="{{J}}" class="data-v-2a6aaf81"><view wx:if="{{K}}" class="empty-items data-v-2a6aaf81">暂无可上架物品</view><scroll-view class="list-select-list data-v-2a6aaf81" scroll-y="true"><view wx:for="{{L}}" wx:for-item="item" wx:key="f" class="list-select-item data-v-2a6aaf81" bindtap="{{item.g}}"><text class="item-name data-v-2a6aaf81">{{item.a}}</text><text class="item-quality data-v-2a6aaf81" style="{{'color:' + item.c}}">{{item.b}}</text><text wx:if="{{item.d}}" class="item-quantity data-v-2a6aaf81">x{{item.e}}</text></view></scroll-view></view><view wx:else class="price-input-section data-v-2a6aaf81"><view class="selected-item-info data-v-2a6aaf81"><text class="selected-item-label data-v-2a6aaf81">选中物品：</text><text class="selected-item-name data-v-2a6aaf81">{{M}}</text><text class="selected-item-quality data-v-2a6aaf81" style="{{'color:' + O}}">{{N}}</text><text class="item-stock data-v-2a6aaf81">库存：{{P}}</text></view><view class="quantity-input-wrapper data-v-2a6aaf81"><text class="quantity-label data-v-2a6aaf81">出售数量：</text><view class="quantity-controls data-v-2a6aaf81"><button class="quantity-btn data-v-2a6aaf81" bindtap="{{Q}}">-</button><input class="quantity-input data-v-2a6aaf81" type="number" max="{{R}}" min="1" value="{{S}}" bindinput="{{T}}"/><button class="quantity-btn data-v-2a6aaf81" bindtap="{{U}}">+</button></view></view><view class="price-input-wrapper data-v-2a6aaf81"><text class="price-label data-v-2a6aaf81">单价（银两）：</text><input class="price-input data-v-2a6aaf81" type="number" placeholder="请输入单价" value="{{V}}" bindinput="{{W}}"/></view><view wx:if="{{X}}" class="total-price-info data-v-2a6aaf81"><text class="total-label data-v-2a6aaf81">总价：</text><text class="total-value data-v-2a6aaf81">{{Y}} 银两</text></view></view></view><view class="modal-footer data-v-2a6aaf81"><button class="modal-btn cancel-btn data-v-2a6aaf81" bindtap="{{Z}}">取消</button><button wx:if="{{aa}}" class="modal-btn confirm-btn data-v-2a6aaf81" bindtap="{{ab}}">确认上架</button></view></view></view><view wx:if="{{ae}}" class="modal-overlay data-v-2a6aaf81" bindtap="{{ak}}"><view class="modal-content data-v-2a6aaf81" catchtap="{{aj}}"><view class="modal-header data-v-2a6aaf81"><text class="modal-title data-v-2a6aaf81">我的订单</text><text class="modal-close data-v-2a6aaf81" bindtap="{{af}}">×</text></view><view class="modal-body data-v-2a6aaf81"><scroll-view class="order-list-scroll data-v-2a6aaf81" scroll-y="true"><view class="order-list data-v-2a6aaf81"><view wx:if="{{ag}}" class="market-empty data-v-2a6aaf81">暂无订单</view><view wx:for="{{ah}}" wx:for-item="order" wx:key="i" class="order-card data-v-2a6aaf81"><image class="item-img data-v-2a6aaf81" src="{{order.a}}" mode="aspectFill"/><view class="order-info data-v-2a6aaf81"><view class="item-name data-v-2a6aaf81">{{order.b}}</view><view class="item-detail data-v-2a6aaf81"><text class="data-v-2a6aaf81">单价：</text><text class="price data-v-2a6aaf81">{{order.c}}</text><text class="data-v-2a6aaf81"> 数量：</text><text class="data-v-2a6aaf81">{{order.d}}</text></view><view class="seller data-v-2a6aaf81">卖家：{{order.e}}</view><view class="{{['order-time', 'data-v-2a6aaf81', order.g && 'time-warning']}}"> 剩余: {{order.f}}</view></view><button class="unlist-btn data-v-2a6aaf81" bindtap="{{order.h}}">下架</button></view></view></scroll-view></view><view class="modal-footer data-v-2a6aaf81"><button class="modal-btn cancel-btn data-v-2a6aaf81" bindtap="{{ai}}">关闭</button></view></view></view></view>