# Mini-Game Compatibility Patch Application Script

$sourceDir = "unpackage\dist\dev\mp-weixin"

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Mini-Game Compatibility Patch Tool" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if build directory exists
if (-not (Test-Path $sourceDir)) {
    Write-Host "ERROR: Build directory not found!" -ForegroundColor Red
    Write-Host "Please build the project first using HBuilderX" -ForegroundColor Yellow
    Read-Host "Press any key to exit"
    exit 1
}

# Create game.json
Write-Host "Creating game.json..." -ForegroundColor Yellow
$gameJsonContent = @"
{
  "deviceOrientation": "portrait",
  "showStatusBar": false,
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  },
  "subpackages": [],
  "plugins": {},
  "preloadRule": {},
  "resizable": false
}
"@
$gameJsonContent | Out-File -FilePath "$sourceDir\game.json" -Encoding UTF8

# Compatibility patch code
$patchCode = @"
// Mini-game compatibility patch - must execute before vendor.js
(function() {
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
  }

  if (typeof wx !== 'undefined') {
    console.log('Applying mini-game compatibility patch');

    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = [
          'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
          'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting'
        ];
        return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
      };
    }

    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
    }
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
    }
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
    }

    if (typeof Page === 'undefined') {
      globalObj.Page = function(options) { return options; };
    }
    if (typeof Component === 'undefined') {
      globalObj.Component = function(options) { return options; };
    }
    if (typeof App === 'undefined') {
      globalObj.App = function(options) { return options; };
    }
    if (typeof getApp === 'undefined') {
      globalObj.getApp = function(options) { return { `$vm: null, globalData: {} }; };
    }

    console.log('Mini-game compatibility patch applied successfully');
  }
})();

"@

# Apply patch to app.js
if (Test-Path "$sourceDir\app.js") {
    Write-Host "Applying patch to app.js..." -ForegroundColor Yellow

    $appContent = Get-Content "$sourceDir\app.js" -Raw

    # Insert patch before vendor.js reference
    $patchedContent = $appContent -replace '("use strict";)', "`$1`n`n$patchCode"

    $patchedContent | Out-File -FilePath "$sourceDir\app.js" -Encoding UTF8 -NoNewline

    # Copy to game.js
    Copy-Item "$sourceDir\app.js" "$sourceDir\game.js"

    Write-Host "SUCCESS: Patch applied to app.js and game.js" -ForegroundColor Green
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Patch Application Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "`nNow you can import in WeChat DevTools:" -ForegroundColor White
Write-Host "1. Select 'Mini Game' project type" -ForegroundColor White
Write-Host "2. Import directory: $(Get-Location)\$sourceDir" -ForegroundColor White
Write-Host "3. Enter your Mini Game AppID" -ForegroundColor White

Read-Host "`nPress any key to exit"
