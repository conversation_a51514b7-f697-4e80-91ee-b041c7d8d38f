# 仗剑江湖行 - 小游戏兼容性补丁应用脚本

$sourceDir = "unpackage\dist\dev\mp-weixin"

Write-Host "========================================" -ForegroundColor Green
Write-Host "    仗剑江湖行 - 小游戏兼容性补丁工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 检查编译目录是否存在
Write-Host "[1/5] 检查编译目录..." -ForegroundColor Yellow
if (-not (Test-Path $sourceDir)) {
    Write-Host "错误：编译目录不存在！" -ForegroundColor Red
    Write-Host "请先通过 HBuilderX 编译项目：" -ForegroundColor Yellow
    Write-Host "  运行 > 运行到小程序模拟器 > 微信开发者工具" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}
Write-Host "✓ 编译目录存在" -ForegroundColor Green

# 复制源码中的配置文件
Write-Host ""
Write-Host "[2/5] 复制小游戏配置文件..." -ForegroundColor Yellow
if (Test-Path "game.json") {
    Copy-Item "game.json" "$sourceDir\game.json"
    Write-Host "✓ game.json 已从源码复制" -ForegroundColor Green
} else {
    Write-Host "警告：源码中的 game.json 不存在，创建默认配置..." -ForegroundColor Yellow
    $gameJsonContent = @"
{
  "deviceOrientation": "portrait",
  "showStatusBar": false,
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  },
  "subpackages": [],
  "plugins": {},
  "preloadRule": {},
  "resizable": false
}
"@
    $gameJsonContent | Out-File -FilePath "$sourceDir\game.json" -Encoding UTF8
    Write-Host "✓ 默认 game.json 已创建" -ForegroundColor Green
}



# 复制小游戏入口文件
Write-Host ""
Write-Host "[3/5] 复制小游戏入口文件..." -ForegroundColor Yellow
if (Test-Path "game.js") {
    Copy-Item "game.js" "$sourceDir\game.js"
    Write-Host "✓ game.js 已从源码复制" -ForegroundColor Green
} elseif (Test-Path "$sourceDir\app.js") {
    Copy-Item "$sourceDir\app.js" "$sourceDir\game.js"
    Write-Host "✓ game.js 已从 app.js 创建" -ForegroundColor Green
} else {
    Write-Host "错误：无法创建 game.js 文件" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 复制兼容性补丁
Write-Host ""
Write-Host "[4/5] 复制兼容性补丁..." -ForegroundColor Yellow
if (Test-Path "mini-game-patch.js") {
    Copy-Item "mini-game-patch.js" "$sourceDir\mini-game-patch.js"
    Write-Host "✓ mini-game-patch.js 已复制" -ForegroundColor Green
} else {
    Write-Host "警告：mini-game-patch.js 不存在" -ForegroundColor Yellow
}

# 验证文件
Write-Host ""
Write-Host "[5/5] 验证小游戏文件..." -ForegroundColor Yellow
$requiredFiles = @("game.json", "game.js", "app.js", "app.json")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (-not (Test-Path "$sourceDir\$file")) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -eq 0) {
    Write-Host "✓ 所有必需文件都存在" -ForegroundColor Green
} else {
    Write-Host "警告：缺少以下文件：$($missingFiles -join ', ')" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           转换完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "现在可以在微信开发者工具中导入小游戏：" -ForegroundColor White
Write-Host "1. 打开微信开发者工具" -ForegroundColor White
Write-Host "2. 选择'小游戏'项目类型" -ForegroundColor White
Write-Host "3. 项目目录：$(Get-Location)\$sourceDir" -ForegroundColor White
Write-Host "4. 填入您的小游戏 AppID" -ForegroundColor White
Write-Host "5. 点击'导入'" -ForegroundColor White
Write-Host ""
Write-Host "注意事项：" -ForegroundColor Yellow
Write-Host "- 确保已在微信公众平台注册小游戏" -ForegroundColor White
Write-Host "- 在 manifest.json 中填入正确的 AppID" -ForegroundColor White
Write-Host "- 所有兼容性补丁已自动应用到源码中" -ForegroundColor White

Read-Host "`n按任意键退出"
