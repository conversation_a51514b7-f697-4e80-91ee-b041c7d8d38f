"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_websocket = require("../../utils/websocket.js");
const utils_gameData = require("../../utils/gameData.js");
const _sfc_main = {
  data() {
    return {
      isRegistering: false,
      isLoading: false,
      loadingText: "正在处理...",
      loginForm: {
        username: "",
        password: ""
      },
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        characterName: "",
        gender: "男"
      }
    };
  },
  computed: {
    isFormValid() {
      const form = this.registerForm;
      return form.username.length >= 3 && form.username.length <= 20 && form.password.length >= 6 && form.password.length <= 20 && form.password === form.confirmPassword && form.characterName.length >= 2 && form.characterName.length <= 10;
    }
  },
  onLoad() {
    utils_websocket.wsManager.setLoginPageStatus(true);
    setTimeout(() => {
      this.checkLoginStatus();
    }, 1e3);
  },
  onUnload() {
    utils_websocket.wsManager.setLoginPageStatus(false);
  },
  methods: {
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (token && userInfo) {
        common_vendor.index.showToast({
          title: "发现已保存的登录信息",
          icon: "none",
          duration: 1500
        });
        setTimeout(() => {
          this.autoLogin();
        }, 1500);
      }
    },
    async autoLogin() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!token) {
          return;
        }
        this.isLoading = true;
        this.loadingText = "正在自动登录...";
        await utils_websocket.wsManager.connect();
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "auth",
          data: {
            token,
            userInfo
          }
        });
        if (response && response.type === "auth_success") {
          this.handleAutoLoginSuccess(response.data);
        } else if (response && response.type === "auth_failed") {
          this.handleAutoLoginFailed(response.data);
        } else {
          this.isLoading = false;
          common_vendor.index.showToast({
            title: "自动登录失败，请手动登录",
            icon: "none"
          });
        }
      } catch (error) {
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "自动登录失败，请手动登录",
          icon: "none"
        });
      }
    },
    handleAutoLoginSuccess(data) {
      this.isLoading = false;
      if (data.userInfo) {
        common_vendor.index.setStorageSync("userInfo", data.userInfo);
      }
      common_vendor.index.showToast({
        title: "自动登录成功",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }, 1e3);
    },
    handleAutoLoginFailed(data) {
      this.isLoading = false;
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.showToast({
        title: data.message || "自动登录失败，请重新登录",
        icon: "none",
        duration: 3e3
      });
    },
    switchToRegister() {
      this.isRegistering = true;
    },
    switchToLogin() {
      this.isRegistering = false;
    },
    selectGender(gender) {
      this.registerForm.gender = gender;
    },
    async handleLogin() {
      if (!this.loginForm.username || !this.loginForm.password) {
        common_vendor.index.showToast({
          title: "请输入账号和密码",
          icon: "none"
        });
        return;
      }
      this.isLoading = true;
      this.loadingText = "正在登录...";
      if (!this._loginSuccessHandler) {
        this._loginSuccessHandler = (data) => {
          this.isLoading = false;
          if (data && data.token) {
            common_vendor.index.setStorageSync("token", data.token);
          }
          if (data && data.userInfo) {
            common_vendor.index.setStorageSync("userInfo", data.userInfo);
          }
          common_vendor.index.showToast({ title: "登录成功", icon: "success" });
          setTimeout(() => {
            common_vendor.index.switchTab({ url: "/pages/index/index" });
          }, 1e3);
          utils_websocket.wsManager.off("login_success", this._loginSuccessHandler);
          utils_websocket.wsManager.off("login_failed", this._loginFailedHandler);
        };
        utils_websocket.wsManager.on("login_success", this._loginSuccessHandler);
      }
      if (!this._loginFailedHandler) {
        this._loginFailedHandler = (data) => {
          this.isLoading = false;
          common_vendor.index.showToast({ title: data && data.message ? data.message : "登录失败，请重试", icon: "none" });
          utils_websocket.wsManager.off("login_success", this._loginSuccessHandler);
          utils_websocket.wsManager.off("login_failed", this._loginFailedHandler);
        };
        utils_websocket.wsManager.on("login_failed", this._loginFailedHandler);
      }
      await utils_websocket.wsManager.connect();
      utils_websocket.wsManager.sendMessage("login", {
        username: this.loginForm.username,
        password: this.loginForm.password
      });
    },
    async handleRegister() {
      if (!this.isFormValid) {
        common_vendor.index.showToast({
          title: "请检查输入信息",
          icon: "none"
        });
        return;
      }
      try {
        this.isLoading = true;
        this.loadingText = "正在创建角色...";
        await utils_websocket.wsManager.connect();
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "register",
          data: {
            username: this.registerForm.username,
            password: this.registerForm.password,
            characterName: this.registerForm.characterName,
            gender: this.registerForm.gender
          }
        });
        if (response && response.type === "register_success") {
          this.handleRegisterSuccess(response.data);
        } else if (response && response.type === "register_failed") {
          this.handleRegisterFailed(response.data);
        } else {
          this.isLoading = false;
          common_vendor.index.showToast({
            title: "注册失败，请重试",
            icon: "none"
          });
        }
      } catch (error) {
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "注册失败: " + error.message,
          icon: "none"
        });
      }
    },
    async handleLoginSuccess(data) {
      this.isLoading = false;
      try {
        common_vendor.index.setStorageSync("token", data.token);
        common_vendor.index.setStorageSync("userInfo", data.userInfo);
        utils_websocket.wsManager.setLoginPageStatus(false);
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success",
          duration: 1500
        });
        if (!utils_websocket.wsManager.isConnected) {
          await utils_websocket.wsManager.connect();
        }
        utils_websocket.wsManager.isAuthed = true;
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/index/index",
            success: function() {
            },
            fail: function(err) {
              common_vendor.index.redirectTo({
                url: "/pages/index/index"
              });
            }
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: "登录过程中出现错误",
          icon: "none"
        });
      }
    },
    handleLoginFailed(data) {
      this.isLoading = false;
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.showToast({
        title: data.message || "登录失败",
        icon: "none",
        duration: 3e3
      });
    },
    handleRegisterSuccess(data) {
      this.isLoading = false;
      try {
        common_vendor.index.setStorageSync("token", data.token);
        common_vendor.index.setStorageSync("userInfo", data.userInfo);
        utils_websocket.wsManager.setLoginPageStatus(false);
        let talentStr = "";
        if (data.userInfo && data.userInfo.talent) {
          talentStr = `
力量：${data.userInfo.talent["力量"]}  悟性：${data.userInfo.talent["悟性"]}  身法：${data.userInfo.talent["身法"]}  根骨：${data.userInfo.talent["根骨"]}`;
        }
        let fortuneStr = data.userInfo && data.userInfo.fortune ? `
富源：${data.userInfo.fortune}` : "";
        common_vendor.index.showModal({
          title: "角色创建成功",
          content: `恭喜你，角色创建成功！${talentStr}${fortuneStr}`,
          showCancel: false,
          success: async () => {
            try {
              if (!utils_websocket.wsManager.isConnected) {
                await utils_websocket.wsManager.connect();
              }
              utils_websocket.wsManager.isAuthed = true;
              common_vendor.index.showToast({
                title: "正在进入游戏",
                icon: "success",
                duration: 1500
              });
              setTimeout(() => {
                common_vendor.index.switchTab({
                  url: "/pages/index/index",
                  success: function() {
                  },
                  fail: function(err) {
                    common_vendor.index.redirectTo({
                      url: "/pages/index/index"
                    });
                  }
                });
              }, 1e3);
            } catch (error) {
              common_vendor.index.showToast({
                title: "进入游戏时出现错误",
                icon: "none"
              });
            }
          }
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "注册过程中出现错误",
          icon: "none"
        });
      }
    },
    handleRegisterFailed(data) {
      this.isLoading = false;
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.showToast({
        title: data.message || "注册失败",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isRegistering
  }, !$data.isRegistering ? {
    b: $data.loginForm.username,
    c: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    e: $data.loginForm.password,
    f: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    g: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    h: common_vendor.o((...args) => $options.switchToRegister && $options.switchToRegister(...args))
  } : {}, {
    i: $data.isRegistering
  }, $data.isRegistering ? common_vendor.e({
    j: $data.registerForm.username,
    k: common_vendor.o(($event) => $data.registerForm.username = $event.detail.value),
    l: $data.registerForm.password,
    m: common_vendor.o(($event) => $data.registerForm.password = $event.detail.value),
    n: $data.registerForm.confirmPassword,
    o: common_vendor.o(($event) => $data.registerForm.confirmPassword = $event.detail.value),
    p: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args)),
    q: $data.registerForm.characterName,
    r: common_vendor.o(($event) => $data.registerForm.characterName = $event.detail.value),
    s: $data.registerForm.gender === "男" ? 1 : "",
    t: common_vendor.o(($event) => $options.selectGender("男")),
    v: $data.registerForm.gender === "女" ? 1 : "",
    w: common_vendor.o(($event) => $options.selectGender("女")),
    x: $data.registerForm.characterName
  }, $data.registerForm.characterName ? {
    y: common_vendor.t($data.registerForm.characterName)
  } : {}, {
    z: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args)),
    A: !$options.isFormValid,
    B: common_vendor.o((...args) => $options.switchToLogin && $options.switchToLogin(...args))
  }) : {}, {
    C: $data.isLoading
  }, $data.isLoading ? {
    D: common_vendor.t($data.loadingText)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
