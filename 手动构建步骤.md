# 手动构建小游戏步骤

## 🎯 当前状态

源码已经完成小游戏配置，现在需要手动编译和配置。

## 📋 操作步骤

### 第一步：在 HBuilderX 中编译项目

1. **打开 HBuilderX**
2. **打开项目**：选择目录 `D:\zjjhx\仗剑江湖行`
3. **开始编译**：
   - 点击菜单：`运行` -> `运行到小程序模拟器` -> `微信开发者工具`
   - 或者使用快捷键
4. **等待编译完成**：
   - 控制台会显示编译进度
   - 编译成功后会生成 `unpackage\dist\dev\mp-weixin` 目录

### 第二步：应用小游戏补丁

编译完成后，运行以下命令：

```bash
# 在项目根目录执行
build-minigame.bat
```

或者手动执行以下操作：

1. **创建 game.json**：
   ```json
   {
     "deviceOrientation": "portrait",
     "showStatusBar": false,
     "networkTimeout": {
       "request": 60000,
       "connectSocket": 60000,
       "uploadFile": 60000,
       "downloadFile": 60000
     },
     "subpackages": [],
     "plugins": {},
     "preloadRule": {},
     "resizable": false
   }
   ```
   保存为：`unpackage\dist\dev\mp-weixin\game.json`

2. **复制 game.js**：
   ```bash
   copy unpackage\dist\dev\mp-weixin\app.js unpackage\dist\dev\mp-weixin\game.js
   ```

### 第三步：在微信开发者工具中导入

1. **打开微信开发者工具**
2. **选择项目类型**：选择"小游戏"
3. **导入项目**：
   - 项目目录：`D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin`
   - AppID：填入您的小游戏 AppID（如果没有可以使用测试号）
4. **点击导入**

### 第四步：验证运行

1. **检查控制台日志**：
   - 应该看到："开始应用小游戏兼容性补丁"
   - 应该看到："小游戏兼容性补丁应用完成"

2. **检查页面显示**：
   - 首页应该正常显示（不再黑屏）
   - 如果没有登录，会跳转到登录页面

3. **测试功能**：
   - 登录功能
   - 页面导航
   - WebSocket 连接
   - 游戏功能

## 🔧 故障排除

### 如果编译失败：
1. 检查 HBuilderX 版本是否最新
2. 检查项目依赖是否完整
3. 清理缓存后重新编译

### 如果导入失败：
1. 检查 `game.json` 和 `game.js` 是否存在
2. 检查 AppID 是否正确
3. 检查微信开发者工具版本

### 如果运行黑屏：
1. 检查控制台错误信息
2. 确认补丁是否正确应用
3. 检查首页路由配置

## 📁 关键文件检查

编译完成后，确保以下文件存在：

```
unpackage/dist/dev/mp-weixin/
├── app.js          ✓ 应用入口（包含补丁）
├── app.json        ✓ 应用配置
├── game.js         ✓ 小游戏入口
├── game.json       ✓ 小游戏配置
├── common/
│   └── vendor.js   ✓ uni-app 框架
└── pages/          ✓ 页面文件
    ├── index/
    ├── login/
    └── ...
```

## 🎮 预期结果

成功后应该看到：
- ✅ 微信开发者工具正常打开项目
- ✅ 控制台显示补丁应用成功
- ✅ 首页正常显示（江湖游戏界面）
- ✅ 登录功能正常
- ✅ 所有游戏功能可用

## 📞 如果遇到问题

请提供以下信息：
1. HBuilderX 编译时的错误信息
2. 微信开发者工具的控制台错误
3. 具体的问题描述（黑屏、报错等）

---

**现在请按照上述步骤操作，先在 HBuilderX 中编译项目！**
