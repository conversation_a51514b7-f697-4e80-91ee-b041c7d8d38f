@echo off
chcp 65001 >nul
echo ========================================
echo        Quick Mini-Game Patch Fix
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo ERROR: Build directory not found!
    echo Please build the project first using HBuilderX
    pause
    exit /b 1
)

echo Creating game.json...
echo {> "%SOURCE_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
echo   },>> "%SOURCE_DIR%\game.json"
echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
echo   "resizable": false>> "%SOURCE_DIR%\game.json"
echo }>> "%SOURCE_DIR%\game.json"

echo Creating patched app.js...
echo "use strict";> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo // Mini-game compatibility patch - must execute before vendor.js>> "%SOURCE_DIR%\app.js"
echo (function() {>> "%SOURCE_DIR%\app.js"
echo   const globalObj = (function() {>> "%SOURCE_DIR%\app.js"
echo     if (typeof globalThis !== 'undefined') return globalThis;>> "%SOURCE_DIR%\app.js"
echo     if (typeof window !== 'undefined') return window;>> "%SOURCE_DIR%\app.js"
echo     if (typeof global !== 'undefined') return global;>> "%SOURCE_DIR%\app.js"
echo     if (typeof self !== 'undefined') return self;>> "%SOURCE_DIR%\app.js"
echo     return this;>> "%SOURCE_DIR%\app.js"
echo   })();>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo   if (typeof global === 'undefined') {>> "%SOURCE_DIR%\app.js"
echo     globalObj.global = globalObj;>> "%SOURCE_DIR%\app.js"
echo   }>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo   if (typeof wx !== 'undefined') {>> "%SOURCE_DIR%\app.js"
echo     console.log('Applying mini-game compatibility patch');>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo     if (!wx.canIUse) {>> "%SOURCE_DIR%\app.js"
echo       wx.canIUse = function(apiName) {>> "%SOURCE_DIR%\app.js"
echo         const gameAPIs = ['getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo', 'getDeviceInfo'];>> "%SOURCE_DIR%\app.js"
echo         return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';>> "%SOURCE_DIR%\app.js"
echo       };>> "%SOURCE_DIR%\app.js"
echo     }>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo     if (!wx.getAppBaseInfo ^&^& wx.getSystemInfoSync) wx.getAppBaseInfo = wx.getSystemInfoSync;>> "%SOURCE_DIR%\app.js"
echo     if (!wx.getWindowInfo ^&^& wx.getSystemInfoSync) wx.getWindowInfo = wx.getSystemInfoSync;>> "%SOURCE_DIR%\app.js"
echo     if (!wx.getDeviceInfo ^&^& wx.getSystemInfoSync) wx.getDeviceInfo = wx.getSystemInfoSync;>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo     if (typeof Page === 'undefined') globalObj.Page = function(options) { return options; };>> "%SOURCE_DIR%\app.js"
echo     if (typeof Component === 'undefined') globalObj.Component = function(options) { return options; };>> "%SOURCE_DIR%\app.js"
echo     if (typeof App === 'undefined') globalObj.App = function(options) { return options; };>> "%SOURCE_DIR%\app.js"
echo     if (typeof getApp === 'undefined') globalObj.getApp = function() { return {$vm:null,globalData:{}}; };>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"
echo     console.log('Mini-game compatibility patch applied successfully');>> "%SOURCE_DIR%\app.js"
echo   }>> "%SOURCE_DIR%\app.js"
echo })();>> "%SOURCE_DIR%\app.js"
echo.>> "%SOURCE_DIR%\app.js"

REM Add the rest of the original app.js content
if exist "%SOURCE_DIR%\app.js.backup" (
    echo Adding original app.js content...
    more +2 "%SOURCE_DIR%\app.js.backup" >> "%SOURCE_DIR%\app.js"
) else (
    echo Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });>> "%SOURCE_DIR%\app.js"
    echo const common_vendor = require("./common/vendor.js");>> "%SOURCE_DIR%\app.js"
    echo if (!Math) {>> "%SOURCE_DIR%\app.js"
    echo   "./pages/login/login.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/index/index.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/character/character.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/skills/skills.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/shop/shop.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/guild/guild.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/character/backpack.js";>> "%SOURCE_DIR%\app.js"
    echo   "./pages/crafting/crafting.js";>> "%SOURCE_DIR%\app.js"
    echo }>> "%SOURCE_DIR%\app.js"
    echo const _sfc_main = {>> "%SOURCE_DIR%\app.js"
    echo   onLaunch: function() { console.log("App Launch"); },>> "%SOURCE_DIR%\app.js"
    echo   onShow: function() { console.log("App Show"); },>> "%SOURCE_DIR%\app.js"
    echo   onHide: function() { console.log("App Hide"); }>> "%SOURCE_DIR%\app.js"
    echo };>> "%SOURCE_DIR%\app.js"
    echo function createApp() {>> "%SOURCE_DIR%\app.js"
    echo   const app = common_vendor.createSSRApp(_sfc_main);>> "%SOURCE_DIR%\app.js"
    echo   return { app };>> "%SOURCE_DIR%\app.js"
    echo }>> "%SOURCE_DIR%\app.js"
    echo createApp().app.mount("#app");>> "%SOURCE_DIR%\app.js"
    echo exports.createApp = createApp;>> "%SOURCE_DIR%\app.js"
)

REM Copy to game.js
copy "%SOURCE_DIR%\app.js" "%SOURCE_DIR%\game.js" >nul

echo.
echo ========================================
echo Patch Applied Successfully!
echo ========================================
echo.
echo Now you can import in WeChat DevTools:
echo 1. Select 'Mini Game' project type
echo 2. Import directory: %cd%\%SOURCE_DIR%
echo 3. Check console for patch logs
echo.
pause
