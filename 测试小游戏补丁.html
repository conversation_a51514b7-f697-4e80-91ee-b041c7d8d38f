<!DOCTYPE html>
<html>
<head>
    <title>小游戏补丁测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>小游戏补丁测试</h1>
    <div id="output"></div>

    <script>
        // 模拟小游戏环境
        window.wx = {
            getSystemInfoSync: function() {
                return {
                    platform: 'devtools',
                    version: '8.0.5',
                    SDKVersion: '3.8.12',
                    language: 'zh_CN'
                };
            }
        };

        const output = document.getElementById('output');
        
        function log(message) {
            console.log(message);
            output.innerHTML += '<p>' + message + '</p>';
        }

        // 加载补丁
        const script = document.createElement('script');
        script.src = 'mini-game-patch.js';
        script.onload = function() {
            log('补丁加载完成');
            
            // 测试 wx.canIUse
            if (typeof wx.canIUse === 'function') {
                log('✅ wx.canIUse 存在');
                log('wx.canIUse("getSystemInfoSync"): ' + wx.canIUse("getSystemInfoSync"));
                log('wx.canIUse("getAppBaseInfo"): ' + wx.canIUse("getAppBaseInfo"));
            } else {
                log('❌ wx.canIUse 不存在');
            }
            
            // 测试基础 API
            if (typeof wx.getAppBaseInfo === 'function') {
                log('✅ wx.getAppBaseInfo 存在');
            } else {
                log('❌ wx.getAppBaseInfo 不存在');
            }
            
            // 测试全局函数
            if (typeof Page === 'function') {
                log('✅ Page 函数存在');
            } else {
                log('❌ Page 函数不存在');
            }
            
            if (typeof global !== 'undefined') {
                log('✅ global 对象存在');
            } else {
                log('❌ global 对象不存在');
            }
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
