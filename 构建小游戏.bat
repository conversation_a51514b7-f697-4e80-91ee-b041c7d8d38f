@echo off
chcp 65001 >nul
echo ========================================
echo      仗剑江湖行 - 小游戏专用构建工具
echo ========================================
echo.

echo 注意：此项目是 uni-app 项目，需要特殊的小游戏构建方式
echo.

echo [1/5] 清理旧的构建文件...
if exist "unpackage\dist\dev\mp-weixin" (
    rmdir /s /q "unpackage\dist\dev\mp-weixin"
    echo ✓ 已清理旧的构建文件
)

echo.
echo [2/5] 创建小游戏专用目录...
mkdir "unpackage\dist\dev\mp-weixin" 2>nul

echo.
echo [3/5] 复制核心文件...
copy "game.json" "unpackage\dist\dev\mp-weixin\game.json" >nul
copy "game.js" "unpackage\dist\dev\mp-weixin\game.js" >nul
copy "mini-game-patch.js" "unpackage\dist\dev\mp-weixin\mini-game-patch.js" >nul

echo.
echo [4/5] 创建小游戏入口文件...

echo // 仗剑江湖行 - 小游戏主入口> "unpackage\dist\dev\mp-weixin\app.js"
echo require('./mini-game-patch.js');>> "unpackage\dist\dev\mp-weixin\app.js"
echo.>> "unpackage\dist\dev\mp-weixin\app.js"
echo // 引入游戏主逻辑>> "unpackage\dist\dev\mp-weixin\app.js"
echo require('./game.js');>> "unpackage\dist\dev\mp-weixin\app.js"

echo.
echo [5/5] 创建项目配置文件...

echo {> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "description": "仗剑江湖行小游戏",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "packOptions": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "ignore": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "setting": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "urlCheck": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "es6": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "enhance": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "postcss": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "preloadBackgroundData": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "minified": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "newFeature": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "coverView": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "nodeModules": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "autoAudits": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "showShadowRootInWxmlPanel": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "scopeDataCheck": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "uglifyFileName": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "checkInvalidKey": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "checkSiteMap": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "uploadWithSourceMap": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "compileHotReLoad": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "lazyloadPlaceholderEnable": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "useMultiFrameRuntime": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "useApiHook": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "useApiHostProcess": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "babelSetting": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "ignore": [],>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "disablePlugins": [],>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "outputPath": "">> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "useIsolateContext": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "userConfirmedBundleSwitch": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "packNpmManually": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "packNpmRelationList": [],>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "minifyWXSS": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "disableUseStrict": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "minifyWXML": true,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "showES6CompileOption": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "useCompilerPlugins": false>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "compileType": "miniprogram",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "libVersion": "2.19.4",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "appid": "wxfb9c395829d83b91",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "projectname": "仗剑江湖行",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "debugOptions": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "hidedInDevtools": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "scripts": {},>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "staticServerOptions": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "baseURL": "",>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "servePath": "">> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "isGameTourist": false,>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   "condition": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "search": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "conversation": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "game": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "plugin": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "gamePlugin": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     },>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     "miniprogram": {>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo       "list": []>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo     }>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo   }>> "unpackage\dist\dev\mp-weixin\project.config.json"
echo }>> "unpackage\dist\dev\mp-weixin\project.config.json"

echo.
echo ========================================
echo           小游戏构建完成！
echo ========================================
echo.
echo 📁 小游戏目录：%cd%\unpackage\dist\dev\mp-weixin
echo.
echo 🎮 在微信开发者工具中导入小游戏：
echo 1. 打开微信开发者工具
echo 2. 选择"小游戏"项目类型
echo 3. 项目目录：%cd%\unpackage\dist\dev\mp-weixin
echo 4. AppID：wxfb9c395829d83b91
echo 5. 点击"导入"
echo.
echo ⚠️  注意：这是一个简化的小游戏版本
echo 如需完整功能，建议重新设计为Canvas渲染的小游戏
echo.
pause
