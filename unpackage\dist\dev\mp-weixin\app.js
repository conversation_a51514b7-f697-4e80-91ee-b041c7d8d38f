"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
require("./mini-game-patch.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/character/character.js";
  "./pages/skills/skills.js";
  "./pages/shop/shop.js";
  "./pages/guild/guild.js";
  "./pages/character/backpack.js";
  "./pages/crafting/crafting.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("App Launch");
    if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.getSystemInfoSync) {
      try {
        const systemInfo = common_vendor.wx$1.getSystemInfoSync();
        console.log("小游戏环境信息:", systemInfo);
        if (systemInfo.environment === "miniprogram") {
          console.log("运行在小游戏环境中");
        }
      } catch (e) {
        console.error("获取系统信息失败:", e);
      }
    }
    try {
      const gameData = require("./utils/gameData.js");
      console.log("游戏数据初始化完成");
    } catch (e) {
      console.error("游戏数据初始化失败:", e);
    }
  },
  onShow: function() {
    console.log("App Show");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.reconnect === "function") {
        wsManager.reconnect();
        console.log("WebSocket 重新连接");
      }
    } catch (e) {
      console.error("WebSocket 重连失败:", e);
    }
  },
  onHide: function() {
    console.log("App Hide");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.disconnect === "function") {
        wsManager.disconnect();
        console.log("全局 WebSocket 已断开");
      }
    } catch (e) {
      console.error("断开 WebSocket 失败:", e);
    }
  }
};
if (typeof common_vendor.wx$1 !== "undefined") {
  console.log("当前运行环境：微信小游戏");
  if (typeof global !== "undefined") {
    global.__MINI_GAME__ = true;
  }
  common_vendor.wx$1.onShow && common_vendor.wx$1.onShow(() => {
    console.log("小游戏显示");
  });
  common_vendor.wx$1.onHide && common_vendor.wx$1.onHide(() => {
    console.log("小游戏隐藏");
  });
  common_vendor.wx$1.onMemoryWarning && common_vendor.wx$1.onMemoryWarning((res) => {
    console.warn("内存警告:", res.level);
  });
} else {
  console.log("当前运行环境：非微信小游戏");
}
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
