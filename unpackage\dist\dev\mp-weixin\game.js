// 仗剑江湖行 - 小游戏入口文件
// 此文件是小游戏的主入口，负责初始化小游戏环境

console.log('=== 仗剑江湖行小游戏启动 ===');

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');

  // 云开发初始化（如果需要）
  if (wx.cloud) {
    try {
      wx.cloud.init({
        traceUser: true
      });
      console.log('云开发初始化成功');
    } catch (error) {
      console.warn('云开发初始化失败:', error);
    }
  }

  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      pixelRatio: systemInfo.pixelRatio
    });

    // 设置全局系统信息
    if (typeof global !== 'undefined') {
      global.__SYSTEM_INFO__ = systemInfo;
    }
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 性能监控
  if (wx.getPerformance) {
    const performance = wx.getPerformance();
    console.log('性能信息:', performance);
  }

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告 - 级别:', res.level);
      // 触发垃圾回收
      if (wx.triggerGC) {
        wx.triggerGC();
      }
    });
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');
    // 这里可以添加游戏恢复逻辑
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');
    // 这里可以添加游戏暂停逻辑
  });

  // 创建Canvas用于游戏渲染
  const canvas = wx.createCanvas();
  const ctx = canvas.getContext('2d');

  // 设置Canvas尺寸
  const systemInfo = wx.getSystemInfoSync();
  canvas.width = systemInfo.screenWidth;
  canvas.height = systemInfo.screenHeight;

  console.log('Canvas创建成功，尺寸:', canvas.width, 'x', canvas.height);

  // 简单的游戏界面
  function drawGameUI() {
    // 清空画布
    ctx.fillStyle = '#2c3e50';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制标题
    ctx.fillStyle = '#ffffff';
    ctx.font = '32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

    // 绘制提示信息
    ctx.font = '16px Arial';
    ctx.fillText('小游戏版本正在开发中...', canvas.width / 2, 200);
    ctx.fillText('请等待完整版本发布', canvas.width / 2, 230);

    // 绘制按钮
    ctx.fillStyle = '#3498db';
    ctx.fillRect(canvas.width / 2 - 100, 300, 200, 50);

    ctx.fillStyle = '#ffffff';
    ctx.fillText('开始游戏', canvas.width / 2, 330);
  }

  // 触摸事件处理
  wx.onTouchStart((e) => {
    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 检查是否点击了开始按钮
    if (x >= canvas.width / 2 - 100 && x <= canvas.width / 2 + 100 &&
        y >= 300 && y <= 350) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    }
  });

  // 初始化游戏界面
  drawGameUI();

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');
}

// 游戏启动完成
console.log('=== 仗剑江湖行小游戏启动完成 ===');
