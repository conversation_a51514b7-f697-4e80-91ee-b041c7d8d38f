// Mini-Game Entry with Built-in Compatibility Patches
"use strict";

// CRITICAL: Apply patches IMMEDIATELY - BEFORE ANY IMPORTS
console.log('🎮 Starting mini-game with critical patches');

// Get global object
const globalObj = (function() {
  if (typeof globalThis !== 'undefined') return globalThis;
  if (typeof window !== 'undefined') return window;
  if (typeof global !== 'undefined') return global;
  if (typeof self !== 'undefined') return self;
  return this;
})();

// Ensure global object exists
if (typeof global === 'undefined') {
  globalObj.global = globalObj;
  console.log('✓ Global object created');
}

// Mini-game environment patches
if (typeof wx !== 'undefined') {
  console.log('🔧 Applying WeChat mini-game patches');

  // wx.canIUse patch - CRITICAL PRIORITY
  if (!wx.canIUse) {
    wx.canIUse = function(apiName) {
      console.log('🔍 wx.canIUse called for:', apiName);

      // Define supported APIs for mini-game
      const gameAPIs = [
        'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo',
        'getWindowInfo', 'getDeviceInfo', 'getSystemSetting',
        'getAppAuthorizeSetting', 'request', 'connectSocket',
        'showToast', 'showModal', 'showLoading', 'hideLoading',
        'navigateTo', 'redirectTo', 'switchTab', 'reLaunch',
        'setStorageSync', 'getStorageSync', 'removeStorageSync'
      ];

      if (gameAPIs.includes(apiName)) {
        const result = typeof wx[apiName] === 'function';
        console.log(`✓ ${apiName}: ${result}`);
        return result;
      }

      const result = typeof wx[apiName] !== 'undefined';
      console.log(`? ${apiName}: ${result}`);
      return result;
    };
    console.log('✅ wx.canIUse patch applied successfully');
  } else {
    console.log('ℹ️ wx.canIUse already exists');
  }

  // Basic API compatibility
  if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
    wx.getAppBaseInfo = wx.getSystemInfoSync;
    console.log('✓ wx.getAppBaseInfo patched');
  }
  if (!wx.getWindowInfo && wx.getSystemInfoSync) {
    wx.getWindowInfo = wx.getSystemInfoSync;
    console.log('✓ wx.getWindowInfo patched');
  }
  if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
    wx.getDeviceInfo = wx.getSystemInfoSync;
    console.log('✓ wx.getDeviceInfo patched');
  }

  // Global functions for uni-app compatibility
  if (typeof Page === 'undefined') {
    globalObj.Page = function(options) {
      console.log('📄 Page function called');
      return options;
    };
    console.log('✓ Page function patched');
  }
  if (typeof Component === 'undefined') {
    globalObj.Component = function(options) {
      console.log('🧩 Component function called');
      return options;
    };
    console.log('✓ Component function patched');
  }
  if (typeof App === 'undefined') {
    globalObj.App = function(options) {
      console.log('📱 App function called');
      return options;
    };
    console.log('✓ App function patched');
  }
  if (typeof getApp === 'undefined') {
    globalObj.getApp = function() {
      console.log('🔍 getApp function called');
      return {$vm:null,globalData:{}};
    };
    console.log('✓ getApp function patched');
  }

  console.log('🎉 All mini-game patches applied successfully!');
} else {
  console.warn('⚠️ wx object not found - not in WeChat environment');
}

// CRITICAL: Load vendor.js AFTER patches are applied
console.log('📦 Loading vendor.js with patches applied...');

"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });

// Now it's safe to load vendor.js
const common_vendor = require("./common/vendor.js");
console.log('✅ vendor.js loaded successfully');

// Load additional patches
require("./mini-game-patch.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/character/character.js";
  "./pages/skills/skills.js";
  "./pages/shop/shop.js";
  "./pages/guild/guild.js";
  "./pages/character/backpack.js";
  "./pages/crafting/crafting.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("App Launch");
    if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.getSystemInfoSync) {
      try {
        const systemInfo = common_vendor.wx$1.getSystemInfoSync();
        console.log("小游戏环境信息:", systemInfo);
        if (systemInfo.environment === "miniprogram") {
          console.log("运行在小游戏环境中");
        }
      } catch (e) {
        console.error("获取系统信息失败:", e);
      }
    }
    try {
      const gameData = require("./utils/gameData.js");
      console.log("游戏数据初始化完成");
    } catch (e) {
      console.error("游戏数据初始化失败:", e);
    }
  },
  onShow: function() {
    console.log("App Show");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.reconnect === "function") {
        wsManager.reconnect();
        console.log("WebSocket 重新连接");
      }
    } catch (e) {
      console.error("WebSocket 重连失败:", e);
    }
  },
  onHide: function() {
    console.log("App Hide");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.disconnect === "function") {
        wsManager.disconnect();
        console.log("全局 WebSocket 已断开");
      }
    } catch (e) {
      console.error("断开 WebSocket 失败:", e);
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
console.log('Mini-game started with fallback entry');
