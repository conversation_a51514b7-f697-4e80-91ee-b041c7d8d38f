# HBuilderX 编译指导

## 🎯 当前状态

- ✅ 源码配置已完成
- ✅ 小游戏补丁已就绪
- ⏳ 需要在 HBuilderX 中编译项目

## 📋 详细编译步骤

### 第一步：打开 HBuilderX

1. **启动 HBuilderX**
   - 如果没有安装，请从官网下载：https://www.dcloud.io/hbuilderx.html
   - 建议使用正式版

### 第二步：导入项目

1. **打开项目**
   - 点击 `文件` -> `打开目录`
   - 选择项目目录：`D:\zjjhx\仗剑江湖行`
   - 确认项目在左侧项目列表中显示

### 第三步：配置运行环境

1. **检查运行配置**
   - 确保已安装微信开发者工具
   - 在 HBuilderX 中配置微信开发者工具路径：
     - `工具` -> `设置` -> `运行配置` -> `小程序运行配置`
     - 设置微信开发者工具路径

### 第四步：开始编译

1. **选择运行方式**
   - 点击菜单：`运行` -> `运行到小程序模拟器` -> `微信开发者工具`
   - 或者使用快捷键：`Ctrl + R`

2. **等待编译**
   - 控制台会显示编译进度
   - 编译过程可能需要几分钟
   - 编译成功后会自动打开微信开发者工具

### 第五步：验证编译结果

编译成功后，应该会生成以下目录结构：
```
unpackage/
└── dist/
    └── dev/
        └── mp-weixin/
            ├── app.js
            ├── app.json
            ├── common/
            ├── pages/
            └── ...
```

## 🔧 常见问题解决

### 问题1：HBuilderX 无法识别项目
**解决方案**：
- 确保项目根目录包含 `manifest.json` 文件
- 重新打开项目目录

### 问题2：编译失败
**解决方案**：
- 检查控制台错误信息
- 清理项目缓存：`项目` -> `清理`
- 重新编译

### 问题3：微信开发者工具未自动打开
**解决方案**：
- 手动打开微信开发者工具
- 导入项目：选择 `unpackage\dist\dev\mp-weixin` 目录

### 问题4：编译后缺少文件
**解决方案**：
- 检查源码是否完整
- 重新清理并编译

## 📱 编译完成后的操作

### 1. 运行检查脚本
```bash
check-build.bat
```

### 2. 在微信开发者工具中
- 如果自动打开了微信开发者工具，选择"小程序"类型
- 如果需要转换为小游戏，关闭微信开发者工具
- 重新打开，选择"小游戏"类型
- 导入目录：`D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin`

### 3. 验证运行
- 检查控制台是否有错误
- 确认页面能正常显示
- 测试登录和游戏功能

## 🎮 预期结果

编译成功后：
- ✅ `unpackage\dist\dev\mp-weixin` 目录存在
- ✅ 包含 `app.js`, `app.json`, `pages/` 等文件
- ✅ 微信开发者工具能正常打开项目
- ✅ 控制台显示小游戏补丁应用成功

## 📞 如果遇到问题

请提供：
1. HBuilderX 版本信息
2. 控制台的具体错误信息
3. 编译过程中的截图

---

**现在请按照上述步骤在 HBuilderX 中编译项目！**
