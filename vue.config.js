module.exports = {
  transpileDependencies: ['@dcloudio/uni-ui'],
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
  },
  chainWebpack: (config) => {
    // 小游戏特殊配置
    if (process.env.UNI_PLATFORM === 'mp-weixin') {
      // 移除页面相关的处理
      config.plugin('copy').tap(args => {
        args[0] = args[0].filter(item => {
          // 过滤掉页面相关的复制
          return !item.from || !item.from.includes('pages');
        });
        return args;
      });
    }
  }
};
