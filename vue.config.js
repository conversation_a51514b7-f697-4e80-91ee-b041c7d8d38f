const path = require('path')

module.exports = {
  transpileDependencies: ['@dcloudio/uni-ui'],
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
  },
  chainWebpack: (config) => {
    // 强制编译为小游戏模式
    console.log('当前编译平台:', process.env.UNI_PLATFORM)

    // 如果是微信小程序，强制使用小游戏配置
    if (process.env.UNI_PLATFORM === 'mp-weixin') {
      console.log('强制启用小游戏模式编译...')

      // 修改输出目录为小游戏目录
      config.output.path(path.resolve(__dirname, 'unpackage/dist/dev/mp-weixin-game'))

      // 确保关键文件被复制到输出目录
      config.plugin('copy').tap(args => {
        if (!args[0]) args[0] = []

        // 添加 game.json 复制规则
        args[0].push({
          from: path.resolve(__dirname, 'game.json'),
          to: 'game.json'
        })

        // 添加 game.js 复制规则
        args[0].push({
          from: path.resolve(__dirname, 'game.js'),
          to: 'game.js'
        })

        // 添加兼容性补丁复制规则
        args[0].push({
          from: path.resolve(__dirname, 'mini-game-patch.js'),
          to: 'mini-game-patch.js'
        })

        // 添加页面适配器复制规则
        args[0].push({
          from: path.resolve(__dirname, 'gamePageAdapter.js'),
          to: 'gamePageAdapter.js'
        })

        // 添加小游戏适配器复制规则
        args[0].push({
          from: path.resolve(__dirname, 'miniGameAdapter.js'),
          to: 'miniGameAdapter.js'
        })

        // 添加小游戏WebSocket管理器复制规则
        args[0].push({
          from: path.resolve(__dirname, 'miniGameWebSocket.js'),
          to: 'miniGameWebSocket.js'
        })

        return args
      })

      // 修改主入口文件
      config.entry('app').clear().add(path.resolve(__dirname, 'game.js'))
    }
  }
};
