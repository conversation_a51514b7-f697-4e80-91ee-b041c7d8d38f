const path = require('path')

module.exports = {
  transpileDependencies: ['@dcloudio/uni-ui'],
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
  },
  chainWebpack: (config) => {
    // 小游戏特殊配置
    if (process.env.UNI_PLATFORM === 'mp-weixin-game') {
      console.log('配置小游戏编译...')

      // 确保关键文件被复制到输出目录
      config.plugin('copy').tap(args => {
        if (!args[0]) args[0] = []

        // 添加 game.json 复制规则
        args[0].push({
          from: path.resolve(__dirname, 'game.json'),
          to: 'game.json'
        })

        // 添加 game.js 复制规则
        args[0].push({
          from: path.resolve(__dirname, 'game.js'),
          to: 'game.js'
        })

        // 添加兼容性补丁复制规则
        args[0].push({
          from: path.resolve(__dirname, 'mini-game-patch.js'),
          to: 'mini-game-patch.js'
        })

        // 添加页面适配器复制规则
        args[0].push({
          from: path.resolve(__dirname, 'gamePageAdapter.js'),
          to: 'gamePageAdapter.js'
        })

        return args
      })
    }
  }
};
