/**
 * 仗剑江湖行 - 微信小游戏完整重构版
 * 1:1实现所有原有小程序功能
 */

console.log('=== 仗剑江湖行小游戏启动 ===');

// 检查微信小游戏环境
if (typeof wx === 'undefined') {
  console.error('未检测到微信小游戏环境');
  throw new Error('此游戏只能在微信小游戏环境中运行');
}

// 全局游戏实例
let gameInstance = null;

// 导入完整游戏系统
try {
  // 导入核心模块
  const GameEngine = require('./gameEngine.js').GameEngine || require('./gameEngine.js');
  const UIManager = require('./uiManager.js');
  const InputManager = require('./inputManager.js');
  const NetworkManager = require('./networkManager.js');
  const SceneManager = require('./sceneManager.js').SceneManager;

  console.log('所有核心模块导入成功');

  // 创建游戏实例
  gameInstance = new GameEngine();

} catch (error) {
  console.error('模块导入失败，使用内置完整版本:', error);

  // 如果模块导入失败，使用内置完整版本
  gameInstance = createCompleteGameInstance();
}

// 创建完整游戏实例
function createCompleteGameInstance() {
  console.log('创建内置完整游戏实例...');

  // 获取系统信息
  const systemInfo = wx.getSystemInfoSync();
  console.log('系统信息:', systemInfo);

  // 创建Canvas
  const canvas = wx.createCanvas();
  const ctx = canvas.getContext('2d');

  // 设置Canvas尺寸
  canvas.width = systemInfo.screenWidth;
  canvas.height = systemInfo.screenHeight;

  console.log('Canvas创建成功:', canvas.width, 'x', canvas.height);

  // 完整游戏状态
  const gameState = {
    // 当前场景
    currentScene: 'login',

    // 登录状态
    isLoggedIn: false,
    isAuthed: false,

    // 玩家数据 - 完整对应原小程序
    player: {
      character_name: '',
      name: '',
      gender: '男',
      level: 1,
      hp: 100,
      max_hp: 100,
      mp: 50,
      max_mp: 50,
      energy: 100,
      max_energy: 100,
      spirit: 100,
      max_spirit: 100,
      experience: 0,
      money: 0,
      gold: 0,
      talent: {
        '力量': 0,
        '悟性': 0,
        '身法': 0,
        '根骨': 0
      },
      fortune: 1,
      dodge: 1,
      crit: 1,
      equipment: {},
      inventory: [],
      skills: [],
      realm_info: {
        current_realm: '初出茅庐',
        next_realm: '不堪一击',
        current_min: 0,
        current_max: 5,
        progress: 0,
        experience: 0
      },
      current_map: null,
      status: 'normal'
    },

    // 表单数据
    formData: {
      username: '',
      password: ''
    },

    // 注册数据
    registerData: {
      username: '',
      password: '',
      confirmPassword: '',
      characterName: '',
      gender: 'male'
    },

    // UI状态
    isRegistering: false,
    showMapPopup: false,
    showNpcMenuModal: false,
    showBuyModal: false,
    showBattlePopup: false,
    showGatheringPopup: false,

    // 游戏数据
    eventLog: [],
    connectionStatus: '未连接',
    currentMapName: '',
    mapNpcs: [],
    mapsConfig: {},

    // 商店数据
    shopItems: [
      { name: '铁剑', price: 100, description: '普通的铁制长剑', type: 'weapon' },
      { name: '布衣', price: 50, description: '简单的布制衣服', type: 'armor' },
      { name: '回血丹', price: 20, description: '恢复生命值的丹药', type: 'consumable' },
      { name: '内力丹', price: 30, description: '恢复内力的丹药', type: 'consumable' },
      { name: '体力丹', price: 25, description: '恢复体力的丹药', type: 'consumable' }
    ],

    // 门派数据
    guilds: [
      { name: '华山派', description: '以剑法闻名的门派', requirement: '等级5以上' },
      { name: '武当派', description: '内功深厚的道家门派', requirement: '等级10以上' },
      { name: '少林寺', description: '佛门圣地，武学渊源', requirement: '等级15以上' },
      { name: '峨眉派', description: '女子门派，剑法精妙', requirement: '等级8以上' },
      { name: '丐帮', description: '天下第一大帮', requirement: '等级3以上' }
    ]
  };

  return {
    canvas,
    ctx,
    systemInfo,
    gameState,

    // 初始化方法
    init: function() {
      console.log('开始初始化游戏实例...');

      // 初始化渲染器
      this.initRenderer();

      // 初始化网络
      this.initNetwork();

      // 初始化输入
      this.initInput();

      // 检查自动登录
      this.checkAutoLogin();

      // 首次渲染
      this.render();

      console.log('完整游戏实例初始化完成');
    },

    // 渲染器初始化
    initRenderer: function() {
      console.log('初始化渲染器...');

      // 初始化内置渲染器
      this.initBuiltinRenderer();

      // 尝试导入外部渲染器
      try {
        const renderer = require('./gameRenderer.js');
        this.RenderUtils = renderer.RenderUtils;
        this.uiConfig = renderer.uiConfig;
        console.log('外部渲染器加载成功');
      } catch (error) {
        console.log('使用内置渲染器');
      }

      // 尝试导入场景渲染器
      try {
        this.GameScenes = require('./gameScenes.js');
        console.log('场景渲染器加载成功');
      } catch (error) {
        console.log('使用内置场景渲染器');
        this.GameScenes = this.createBuiltinScenes();
      }
    },

    // 网络初始化
    initNetwork: function() {
      this.connectWebSocket();
    },

    // 输入初始化
    initInput: function() {
      this.registerTouchEvents();
    },

    // 检查自动登录
    checkAutoLogin: function() {
      try {
        const token = this.getLocalStorage('token');
        const userInfo = this.getLocalStorage('userInfo');

        if (token && userInfo) {
          console.log('发现本地登录信息，尝试自动登录');
          this.showToast('发现已保存的登录信息');

          // 延迟执行自动登录
          setTimeout(() => {
            this.autoLogin(token, userInfo);
          }, 1500);
        }
      } catch (error) {
        console.error('检查自动登录失败:', error);
      }
    },

    // 自动登录
    autoLogin: function(token, userInfo) {
      try {
        this.showToast('正在自动登录...');

        // 发送认证消息
        this.sendMessage({
          type: 'auth',
          data: {
            token: token,
            userInfo: userInfo
          }
        });

      } catch (error) {
        console.error('自动登录失败:', error);
        this.showToast('自动登录失败，请手动登录');
      }
    },

    // 渲染方法
    render: function() {
      this.renderCurrentScene();
    },

    // WebSocket连接
    connectWebSocket: function() {
      const self = this;

      try {
        console.log('连接WebSocket服务器...');

        this.socket = wx.connectSocket({
          url: 'ws://localhost:8080', // 与原小程序保持一致
          success: () => {
            console.log('WebSocket连接请求发送成功');
          },
          fail: (error) => {
            console.error('WebSocket连接失败:', error);
            this.gameState.connectionStatus = '连接失败';
            this.showToast('网络连接失败');
          }
        });

        this.socket.onOpen(() => {
          console.log('WebSocket连接已建立');
          self.gameState.connectionStatus = '已连接';
          self.showToast('网络连接成功');
          self.render();
        });

        this.socket.onMessage((res) => {
          self.handleMessage(res.data);
        });

        this.socket.onClose(() => {
          console.log('WebSocket连接已关闭');
          self.gameState.connectionStatus = '连接断开';
          self.render();
        });

        this.socket.onError((error) => {
          console.error('WebSocket连接错误:', error);
          self.gameState.connectionStatus = '连接错误';
          self.showToast('网络连接错误');
        });

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        this.gameState.connectionStatus = '初始化失败';
        this.showToast('网络初始化失败');
      }
    },

    // 处理WebSocket消息
    handleMessage: function(data) {
      try {
        const message = JSON.parse(data);
        console.log('收到服务器消息:', message);

        switch (message.type) {
          case 'login_success':
            this.handleLoginSuccess(message.data);
            break;
          case 'login_failed':
            this.handleLoginFailed(message.message);
            break;
          case 'register_success':
            this.handleRegisterSuccess(message.data);
            break;
          case 'register_failed':
            this.handleRegisterFailed(message.message);
            break;
          case 'auth_success':
            this.handleAuthSuccess(message.data);
            break;
          case 'auth_failed':
            this.handleAuthFailed(message.message);
            break;
          case 'player_data':
            this.handlePlayerData(message.data);
            break;
          case 'adventure_event':
            this.handleAdventureEvent(message.data);
            break;
          case 'battle_start':
            this.handleBattleStart(message.data);
            break;
          case 'battle_result':
            this.handleBattleResult(message.data);
            break;
          case 'gathering_event':
            this.handleGatheringEvent(message.data);
            break;
          case 'map_change':
            this.handleMapChange(message.data);
            break;
          case 'npc_interaction':
            this.handleNpcInteraction(message.data);
            break;
          case 'shop_result':
            this.handleShopResult(message.data);
            break;
          case 'skill_result':
            this.handleSkillResult(message.data);
            break;
          case 'guild_result':
            this.handleGuildResult(message.data);
            break;
          case 'error':
            this.handleServerError(message.message);
            break;
          default:
            console.log('未处理的消息类型:', message.type);
        }
      } catch (error) {
        console.error('解析服务器消息失败:', error);
      }
    },

    // 发送消息
    sendMessage: function(data) {
      if (!this.socket || this.gameState.connectionStatus !== '已连接') {
        this.showToast('网络未连接');
        return;
      }

      try {
        const message = JSON.stringify(data);
        this.socket.send({
          data: message,
          success: () => {
            console.log('消息发送成功:', data);
          },
          fail: (error) => {
            console.error('消息发送失败:', error);
            this.showToast('消息发送失败');
          }
        });
      } catch (error) {
        console.error('发送消息失败:', error);
      }
    },

    // 处理登录成功
    handleLoginSuccess: function(data) {
      console.log('登录成功:', data);
      this.gameState.isLoggedIn = true;
      this.gameState.player = { ...this.gameState.player, ...data };
      this.gameState.currentScene = 'index';

      // 保存登录信息
      this.setLocalStorage('token', data.token);
      this.setLocalStorage('userInfo', data);

      this.showToast('登录成功');
      this.render();
    },

    // 处理登录失败
    handleLoginFailed: function(message) {
      console.log('登录失败:', message);
      this.showToast('登录失败: ' + message);
    },

    // 处理注册成功
    handleRegisterSuccess: function(data) {
      console.log('注册成功:', data);

      // 显示角色创建成功信息
      let message = '恭喜你，角色创建成功！';
      if (data.talent) {
        const talent = data.talent;
        message += `\n力量：${talent['力量'] || 0}  悟性：${talent['悟性'] || 0}  身法：${talent['身法'] || 0}  根骨：${talent['根骨'] || 0}`;
      }
      if (data.fortune) {
        message += `\n富源：${data.fortune}`;
      }

      this.showConfirm('角色创建成功', message, () => {
        // 自动登录
        this.gameState.isLoggedIn = true;
        this.gameState.player = { ...this.gameState.player, ...data };
        this.gameState.currentScene = 'index';
        this.setLocalStorage('token', data.token);
        this.setLocalStorage('userInfo', data);
        this.showToast('正在进入游戏');
        this.render();
      });
    },

    // 处理注册失败
    handleRegisterFailed: function(message) {
      console.log('注册失败:', message);
      this.showToast('注册失败: ' + message);
    },

    // 处理认证成功
    handleAuthSuccess: function(data) {
      console.log('认证成功:', data);
      this.gameState.isAuthed = true;
      this.gameState.isLoggedIn = true;
      this.gameState.player = { ...this.gameState.player, ...data };
      this.gameState.currentScene = 'index';
      this.showToast('自动登录成功');
      this.render();
    },

    // 处理认证失败
    handleAuthFailed: function(message) {
      console.log('认证失败:', message);
      this.gameState.isAuthed = false;
      this.clearLocalStorage();
      this.showToast('认证失败，请重新登录');
    },

    // 处理玩家数据更新
    handlePlayerData: function(data) {
      console.log('更新玩家数据:', data);
      this.gameState.player = { ...this.gameState.player, ...data };
      this.render();
    },

    // 处理冒险事件
    handleAdventureEvent: function(data) {
      console.log('冒险事件:', data);
      this.addEventLog(data.message || '发生了冒险事件');

      if (data.event_type === 'battle') {
        this.handleBattleStart(data);
      } else if (data.event_type === 'gathering') {
        this.handleGatheringEvent(data);
      } else if (data.event_type === 'npc') {
        this.handleNpcInteraction(data);
      } else {
        this.showToast(data.message || '发生了一些事情');
      }

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.render();
    },

    // 处理战斗开始
    handleBattleStart: function(data) {
      console.log('战斗开始:', data);
      this.gameState.showBattlePopup = true;
      this.gameState.battleData = data;
      this.addEventLog(`遭遇敌人: ${data.monster_name || '未知敌人'}`);
      this.render();
    },

    // 处理战斗结果
    handleBattleResult: function(data) {
      console.log('战斗结果:', data);
      this.gameState.showBattlePopup = false;
      this.addEventLog(data.message || '战斗结束');

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.showToast(data.message || '战斗结束');
      this.render();
    },

    // 处理采集事件
    handleGatheringEvent: function(data) {
      console.log('采集事件:', data);
      this.gameState.showGatheringPopup = true;
      this.gameState.gatheringData = data;
      this.addEventLog(`发现采集物品: ${data.item_name || '未知物品'}`);
      this.render();
    },

    // 处理地图切换
    handleMapChange: function(data) {
      console.log('地图切换:', data);
      this.gameState.currentMapName = data.map_name || '';
      this.gameState.mapNpcs = data.npcs || [];
      this.addEventLog(`已切换到: ${data.map_name || '新地图'}`);

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.render();
    },

    // 处理NPC交互
    handleNpcInteraction: function(data) {
      console.log('NPC交互:', data);
      this.gameState.showNpcMenuModal = true;
      this.gameState.npcData = data;
      this.addEventLog(`与${data.npc_name || 'NPC'}对话`);
      this.render();
    },

    // 处理商店结果
    handleShopResult: function(data) {
      console.log('商店操作结果:', data);
      this.showToast(data.message || '操作完成');

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.render();
    },

    // 处理技能结果
    handleSkillResult: function(data) {
      console.log('技能操作结果:', data);
      this.showToast(data.message || '操作完成');

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.render();
    },

    // 处理门派结果
    handleGuildResult: function(data) {
      console.log('门派操作结果:', data);
      this.showToast(data.message || '操作完成');

      if (data.player_data) {
        this.gameState.player = { ...this.gameState.player, ...data.player_data };
      }

      this.render();
    },

    // 处理服务器错误
    handleServerError: function(message) {
      console.error('服务器错误:', message);
      this.showToast('服务器错误: ' + message);
    },

    // 添加事件日志
    addEventLog: function(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.gameState.eventLog.unshift(`[${timestamp}] ${message}`);

      // 保持最多50条日志
      if (this.gameState.eventLog.length > 50) {
        this.gameState.eventLog = this.gameState.eventLog.slice(0, 50);
      }
    },

    // UI工具方法
    showToast: function(message) {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    },

    showConfirm: function(title, content, callback) {
      wx.showModal({
        title: title,
        content: content,
        success: (res) => {
          if (res.confirm && callback) {
            callback();
          }
        }
      });
    },

    showInputDialog: function(title, placeholder, callback) {
      wx.showModal({
        title: title,
        editable: true,
        placeholderText: placeholder,
        success: (res) => {
          if (res.confirm && callback) {
            callback(res.content || '');
          }
        }
      });
    },

    // 本地存储方法
    setLocalStorage: function(key, value) {
      try {
        wx.setStorageSync(key, value);
      } catch (error) {
        console.error('设置本地存储失败:', error);
      }
    },

    getLocalStorage: function(key) {
      try {
        return wx.getStorageSync(key);
      } catch (error) {
        console.error('获取本地存储失败:', error);
        return null;
      }
    },

    clearLocalStorage: function() {
      try {
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
      } catch (error) {
        console.error('清除本地存储失败:', error);
      }
    },

    // 注册触摸事件
    registerTouchEvents: function() {
      const self = this;

      wx.onTouchEnd((e) => {
        if (!e.touches || e.touches.length === 0) return;

        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;

        self.handleTap(x, y);
      });

      // 生命周期事件
      wx.onShow(() => {
        console.log('游戏进入前台');
      });

      wx.onHide(() => {
        console.log('游戏进入后台');
      });
    },

    // 处理触摸事件
    handleTap: function(x, y) {
      console.log('点击事件:', x, y, '当前场景:', this.gameState.currentScene);

      switch (this.gameState.currentScene) {
        case 'login':
          this.handleLoginTap(x, y);
          break;
        case 'index':
          this.handleIndexTap(x, y);
          break;
        case 'character':
          this.handleCharacterTap(x, y);
          break;
        case 'skills':
          this.handleSkillsTap(x, y);
          break;
        case 'shop':
          this.handleShopTap(x, y);
          break;
        case 'guild':
          this.handleGuildTap(x, y);
          break;
        case 'backpack':
          this.handleBackpackTap(x, y);
          break;
      }
    },

    // 渲染当前场景
    renderCurrentScene: function() {
      try {
        console.log('渲染场景:', this.gameState.currentScene);

        // 确保渲染器已初始化
        if (!this.RenderUtils) {
          console.log('渲染器未初始化，重新初始化...');
          this.initBuiltinRenderer();
        }

        // 确保场景渲染器已初始化
        if (!this.GameScenes) {
          console.log('场景渲染器未初始化，重新初始化...');
          this.GameScenes = this.createBuiltinScenes();
        }

        // 根据当前场景渲染
        switch (this.gameState.currentScene) {
          case 'login':
            console.log('渲染登录场景');
            this.GameScenes.renderLoginScene(this);
            break;
          case 'index':
            console.log('渲染主页场景');
            this.GameScenes.renderIndexScene(this);
            break;
          case 'character':
            console.log('渲染角色场景');
            this.renderCharacterScene();
            break;
          case 'skills':
            console.log('渲染武功场景');
            this.renderSkillsScene();
            break;
          case 'shop':
            console.log('渲染商店场景');
            this.renderShopScene();
            break;
          case 'guild':
            console.log('渲染门派场景');
            this.renderGuildScene();
            break;
          case 'backpack':
            console.log('渲染背包场景');
            this.renderBackpackScene();
            break;
          default:
            console.log('默认渲染登录场景');
            this.GameScenes.renderLoginScene(this);
        }

        console.log('场景渲染完成');

      } catch (error) {
        console.error('渲染失败:', error);
        this.renderErrorScene(error.message);
      }
    },

    // 内置渲染器初始化
    initBuiltinRenderer: function() {
      this.RenderUtils = {
        clear: (ctx, canvas, color = '#ecf0f1') => {
          ctx.fillStyle = color;
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        },
        drawText: (ctx, text, x, y, options = {}) => {
          const { font = '18px Arial', color = '#2c3e50', align = 'left', baseline = 'top' } = options;
          ctx.font = font;
          ctx.fillStyle = color;
          ctx.textAlign = align;
          ctx.textBaseline = baseline;
          ctx.fillText(text, x, y);
        },
        drawButton: (ctx, text, x, y, width, height, options = {}) => {
          const { backgroundColor = '#2c3e50', textColor = '#ffffff', font = '18px Arial' } = options;
          ctx.fillStyle = backgroundColor;
          ctx.fillRect(x, y, width, height);
          ctx.font = font;
          ctx.fillStyle = textColor;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(text, x + width / 2, y + height / 2);
          return { x, y, width, height };
        },
        isPointInRect: (x, y, rect) => {
          return x >= rect.x && x <= rect.x + rect.width && y >= rect.y && y <= rect.y + rect.height;
        }
      };

      this.uiConfig = {
        colors: {
          primary: '#2c3e50',
          secondary: '#3498db',
          success: '#27ae60',
          white: '#ffffff',
          text: '#2c3e50',
          background: '#ecf0f1'
        },
        fonts: {
          title: 'bold 32px Arial',
          large: 'bold 24px Arial',
          normal: '18px Arial'
        }
      };
    },

    // 渲染错误场景
    renderErrorScene: function(message) {
      this.RenderUtils.clear(this.ctx, this.canvas, '#ff6b6b');
      this.RenderUtils.drawText(this.ctx, '游戏渲染错误', this.canvas.width / 2, 100, {
        font: 'bold 24px Arial',
        color: '#ffffff',
        align: 'center'
      });
      this.RenderUtils.drawText(this.ctx, message, this.canvas.width / 2, 150, {
        font: '16px Arial',
        color: '#ffffff',
        align: 'center'
      });
    },

    // 创建内置场景渲染器
    createBuiltinScenes: function() {
      const self = this;
      return {
        renderLoginScene: function(gameInstance) {
          const { ctx, canvas, gameState } = gameInstance;

          console.log('内置渲染器：渲染登录场景');

          // 绘制渐变背景
          const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
          gradient.addColorStop(0, '#667eea');
          gradient.addColorStop(1, '#764ba2');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 绘制标题
          ctx.font = 'bold 32px Arial';
          ctx.fillStyle = '#ffffff';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

          ctx.font = '18px Arial';
          ctx.fillText('微信小游戏版', canvas.width / 2, 140);

          // 绘制表单背景
          const formX = 30;
          const formY = 180;
          const formWidth = canvas.width - 60;
          const formHeight = gameState.isRegistering ? 500 : 320;

          ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
          ctx.fillRect(formX, formY, formWidth, formHeight);

          if (gameState.isRegistering) {
            self.renderRegisterFormBuiltin(gameInstance, formX, formY, formWidth);
          } else {
            self.renderLoginFormBuiltin(gameInstance, formX, formY, formWidth);
          }
        },

        renderIndexScene: function(gameInstance) {
          const { ctx, canvas, gameState } = gameInstance;

          console.log('内置渲染器：渲染主页场景');

          // 清空画布
          ctx.fillStyle = '#ecf0f1';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 绘制头部
          ctx.fillStyle = '#2c3e50';
          ctx.fillRect(0, 0, canvas.width, 60);

          ctx.font = 'bold 24px Arial';
          ctx.fillStyle = '#ffffff';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('仗剑江湖行', canvas.width / 2, 30);

          // 绘制连接状态
          const statusColor = gameState.connectionStatus === '已连接' ? '#27ae60' : '#e74c3c';
          ctx.font = '12px Arial';
          ctx.fillStyle = statusColor;
          ctx.textAlign = 'right';
          ctx.fillText(gameState.connectionStatus, canvas.width - 20, 30);

          // 绘制玩家信息
          const player = gameState.player;
          ctx.font = '18px Arial';
          ctx.fillStyle = '#2c3e50';
          ctx.textAlign = 'left';
          ctx.fillText(`角色: ${player.character_name || player.name || '未知'}`, 20, 100);
          ctx.fillText(`等级: ${player.level || 1}`, 20, 130);
          ctx.fillText(`银两: ${player.money || 0}`, 20, 160);

          // 闯江湖按钮
          const buttonY = 200;
          ctx.fillStyle = '#27ae60';
          ctx.fillRect(20, buttonY, canvas.width - 40, 50);

          ctx.fillStyle = '#ffffff';
          ctx.textAlign = 'center';
          ctx.font = 'bold 20px Arial';
          ctx.fillText('闯江湖', canvas.width / 2, buttonY + 30);

          gameState.adventureButton = {
            x: 20,
            y: buttonY,
            width: canvas.width - 40,
            height: 50
          };

          // 底部导航栏
          const tabBarHeight = 80;
          const tabBarY = canvas.height - tabBarHeight;
          const tabs = ['角色', '武功', '闯', '商店', '门派'];
          const tabWidth = canvas.width / tabs.length;

          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, tabBarY, canvas.width, tabBarHeight);

          // 绘制分割线
          ctx.strokeStyle = '#bdc3c7';
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(0, tabBarY);
          ctx.lineTo(canvas.width, tabBarY);
          ctx.stroke();

          gameState.tabButtons = [];

          tabs.forEach((tab, index) => {
            const x = index * tabWidth;
            const isActive = (index === 2); // '闯' 是活跃的

            ctx.font = '16px Arial';
            ctx.fillStyle = isActive ? '#2c3e50' : '#7f8c8d';
            ctx.textAlign = 'center';
            ctx.fillText(tab, x + tabWidth / 2, tabBarY + 40);

            if (isActive) {
              ctx.fillStyle = '#2c3e50';
              ctx.fillRect(x + tabWidth / 2 - 20, tabBarY + 55, 40, 3);
            }

            gameState.tabButtons.push({
              x: x,
              y: tabBarY,
              width: tabWidth,
              height: tabBarHeight,
              key: ['character', 'skills', 'index', 'shop', 'guild'][index]
            });
          });
        }
      };
    },

    // 渲染登录表单（内置）
    renderLoginFormBuiltin: function(gameInstance, formX, formY, formWidth) {
      const { ctx, gameState } = gameInstance;

      // 表单标题
      ctx.font = 'bold 24px Arial';
      ctx.fillStyle = '#2c3e50';
      ctx.textAlign = 'center';
      ctx.fillText('登录江湖', formX + formWidth / 2, formY + 40);

      // 账号输入框
      ctx.font = '16px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('账号:', formX + 20, formY + 80);

      // 绘制输入框
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(formX + 20, formY + 100, formWidth - 40, 40);
      ctx.strokeStyle = '#bdc3c7';
      ctx.lineWidth = 1;
      ctx.strokeRect(formX + 20, formY + 100, formWidth - 40, 40);

      // 输入框文本
      ctx.fillStyle = gameState.formData.username ? '#2c3e50' : '#7f8c8d';
      ctx.fillText(gameState.formData.username || '请输入账号', formX + 30, formY + 125);

      gameState.usernameInput = {
        x: formX + 20,
        y: formY + 100,
        width: formWidth - 40,
        height: 40
      };

      // 密码输入框
      ctx.fillStyle = '#2c3e50';
      ctx.fillText('密码:', formX + 20, formY + 160);

      ctx.fillStyle = '#ffffff';
      ctx.fillRect(formX + 20, formY + 180, formWidth - 40, 40);
      ctx.strokeStyle = '#bdc3c7';
      ctx.strokeRect(formX + 20, formY + 180, formWidth - 40, 40);

      ctx.fillStyle = gameState.formData.password ? '#2c3e50' : '#7f8c8d';
      const passwordText = gameState.formData.password ? '••••••••' : '请输入密码';
      ctx.fillText(passwordText, formX + 30, formY + 205);

      gameState.passwordInput = {
        x: formX + 20,
        y: formY + 180,
        width: formWidth - 40,
        height: 40
      };

      // 登录按钮
      ctx.fillStyle = '#2c3e50';
      ctx.fillRect(formX + 20, formY + 240, formWidth - 40, 45);

      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('登录', formX + formWidth / 2, formY + 267);

      gameState.loginButton = {
        x: formX + 20,
        y: formY + 240,
        width: formWidth - 40,
        height: 45
      };

      // 注册链接
      ctx.fillStyle = '#3498db';
      ctx.font = '14px Arial';
      ctx.fillText('还没有账号？点击注册', formX + formWidth / 2, formY + 310);

      gameState.registerLink = {
        x: formX + 20,
        y: formY + 300,
        width: formWidth - 40,
        height: 20
      };
    },

    // 渲染注册表单（内置）
    renderRegisterFormBuiltin: function(gameInstance, formX, formY, formWidth) {
      const { ctx, gameState } = gameInstance;

      // 表单标题
      ctx.font = 'bold 24px Arial';
      ctx.fillStyle = '#2c3e50';
      ctx.textAlign = 'center';
      ctx.fillText('创建角色', formX + formWidth / 2, formY + 40);

      let currentY = formY + 70;
      const fieldHeight = 65;

      // 简化的注册表单 - 只显示主要字段
      ctx.font = '16px Arial';
      ctx.textAlign = 'left';

      // 账号
      ctx.fillStyle = '#2c3e50';
      ctx.fillText('账号:', formX + 20, currentY);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(formX + 20, currentY + 15, formWidth - 40, 35);
      ctx.strokeStyle = '#bdc3c7';
      ctx.strokeRect(formX + 20, currentY + 15, formWidth - 40, 35);

      gameState.regUsernameInput = {
        x: formX + 20,
        y: currentY + 15,
        width: formWidth - 40,
        height: 35
      };
      currentY += fieldHeight;

      // 密码
      ctx.fillStyle = '#2c3e50';
      ctx.fillText('密码:', formX + 20, currentY);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(formX + 20, currentY + 15, formWidth - 40, 35);
      ctx.strokeRect(formX + 20, currentY + 15, formWidth - 40, 35);

      gameState.regPasswordInput = {
        x: formX + 20,
        y: currentY + 15,
        width: formWidth - 40,
        height: 35
      };
      currentY += fieldHeight;

      // 角色名
      ctx.fillStyle = '#2c3e50';
      ctx.fillText('角色名:', formX + 20, currentY);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(formX + 20, currentY + 15, formWidth - 40, 35);
      ctx.strokeRect(formX + 20, currentY + 15, formWidth - 40, 35);

      gameState.regCharacterNameInput = {
        x: formX + 20,
        y: currentY + 15,
        width: formWidth - 40,
        height: 35
      };
      currentY += fieldHeight;

      // 性别选择
      ctx.fillStyle = '#2c3e50';
      ctx.fillText('性别:', formX + 20, currentY);

      // 男按钮
      const maleActive = gameState.registerData.gender === 'male';
      ctx.fillStyle = maleActive ? '#2c3e50' : '#ffffff';
      ctx.fillRect(formX + 20, currentY + 15, 80, 35);
      ctx.strokeStyle = '#bdc3c7';
      ctx.strokeRect(formX + 20, currentY + 15, 80, 35);

      ctx.fillStyle = maleActive ? '#ffffff' : '#2c3e50';
      ctx.textAlign = 'center';
      ctx.fillText('男', formX + 60, currentY + 37);

      gameState.maleButton = {
        x: formX + 20,
        y: currentY + 15,
        width: 80,
        height: 35
      };

      // 女按钮
      const femaleActive = gameState.registerData.gender === 'female';
      ctx.fillStyle = femaleActive ? '#2c3e50' : '#ffffff';
      ctx.fillRect(formX + 120, currentY + 15, 80, 35);
      ctx.strokeRect(formX + 120, currentY + 15, 80, 35);

      ctx.fillStyle = femaleActive ? '#ffffff' : '#2c3e50';
      ctx.fillText('女', formX + 160, currentY + 37);

      gameState.femaleButton = {
        x: formX + 120,
        y: currentY + 15,
        width: 80,
        height: 35
      };

      currentY += 70;

      // 注册按钮
      ctx.fillStyle = '#27ae60';
      ctx.fillRect(formX + 20, currentY, formWidth - 40, 45);

      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 20px Arial';
      ctx.fillText('创建角色', formX + formWidth / 2, currentY + 27);

      gameState.registerButton = {
        x: formX + 20,
        y: currentY,
        width: formWidth - 40,
        height: 45
      };

      // 返回登录链接
      ctx.fillStyle = '#3498db';
      ctx.font = '14px Arial';
      ctx.fillText('已有账号？返回登录', formX + formWidth / 2, currentY + 65);

      gameState.backToLoginLink = {
        x: formX + 20,
        y: currentY + 55,
        width: formWidth - 40,
        height: 20
      };
    },

    // 处理登录页面点击
    handleLoginTap: function(x, y) {
      const gameState = this.gameState;

      if (gameState.isRegistering) {
        // 注册页面处理
        if (gameState.regUsernameInput && this.RenderUtils.isPointInRect(x, y, gameState.regUsernameInput)) {
          this.showInputDialog('输入账号', '请输入账号', (value) => {
            gameState.registerData.username = value;
            this.render();
          });
        } else if (gameState.regPasswordInput && this.RenderUtils.isPointInRect(x, y, gameState.regPasswordInput)) {
          this.showInputDialog('输入密码', '请输入密码', (value) => {
            gameState.registerData.password = value;
            this.render();
          });
        } else if (gameState.regConfirmPasswordInput && this.RenderUtils.isPointInRect(x, y, gameState.regConfirmPasswordInput)) {
          this.showInputDialog('确认密码', '请再次输入密码', (value) => {
            gameState.registerData.confirmPassword = value;
            this.render();
          });
        } else if (gameState.regCharacterNameInput && this.RenderUtils.isPointInRect(x, y, gameState.regCharacterNameInput)) {
          this.showInputDialog('输入角色名', '请输入角色名', (value) => {
            gameState.registerData.characterName = value;
            this.render();
          });
        } else if (gameState.maleButton && this.RenderUtils.isPointInRect(x, y, gameState.maleButton)) {
          gameState.registerData.gender = 'male';
          this.render();
        } else if (gameState.femaleButton && this.RenderUtils.isPointInRect(x, y, gameState.femaleButton)) {
          gameState.registerData.gender = 'female';
          this.render();
        } else if (gameState.registerButton && this.RenderUtils.isPointInRect(x, y, gameState.registerButton)) {
          this.handleRegister();
        } else if (gameState.backToLoginLink && this.RenderUtils.isPointInRect(x, y, gameState.backToLoginLink)) {
          gameState.isRegistering = false;
          this.render();
        }
      } else {
        // 登录页面处理
        if (gameState.usernameInput && this.RenderUtils.isPointInRect(x, y, gameState.usernameInput)) {
          this.showInputDialog('输入账号', '请输入账号', (value) => {
            gameState.formData.username = value;
            this.render();
          });
        } else if (gameState.passwordInput && this.RenderUtils.isPointInRect(x, y, gameState.passwordInput)) {
          this.showInputDialog('输入密码', '请输入密码', (value) => {
            gameState.formData.password = value;
            this.render();
          });
        } else if (gameState.loginButton && this.RenderUtils.isPointInRect(x, y, gameState.loginButton)) {
          this.handleLogin();
        } else if (gameState.registerLink && this.RenderUtils.isPointInRect(x, y, gameState.registerLink)) {
          gameState.isRegistering = true;
          this.render();
        }
      }
    },

    // 处理主页面点击
    handleIndexTap: function(x, y) {
      const gameState = this.gameState;

      // 检查闯江湖按钮
      if (gameState.adventureButton && this.RenderUtils.isPointInRect(x, y, gameState.adventureButton)) {
        this.handleAdventure();
      }

      // 检查底部导航栏
      if (gameState.tabButtons) {
        for (const tab of gameState.tabButtons) {
          if (this.RenderUtils.isPointInRect(x, y, tab)) {
            this.switchScene(tab.key);
            break;
          }
        }
      }
    },

    // 处理登录
    handleLogin: function() {
      const { username, password } = this.gameState.formData;

      if (!username || !password) {
        this.showToast('请输入账号和密码');
        return;
      }

      console.log('执行登录:', username);
      this.showToast('正在登录...');

      this.sendMessage({
        type: 'login',
        data: { username, password }
      });
    },

    // 处理注册
    handleRegister: function() {
      const { username, password, confirmPassword, characterName, gender } = this.gameState.registerData;

      if (!username || !password || !confirmPassword || !characterName) {
        this.showToast('请填写完整信息');
        return;
      }

      if (password !== confirmPassword) {
        this.showToast('两次密码输入不一致');
        return;
      }

      if (username.length < 3 || username.length > 20) {
        this.showToast('账号长度应为3-20位字符');
        return;
      }

      if (password.length < 6 || password.length > 20) {
        this.showToast('密码长度应为6-20位字符');
        return;
      }

      if (characterName.length < 2 || characterName.length > 10) {
        this.showToast('角色名长度应为2-10位字符');
        return;
      }

      console.log('执行注册:', { username, characterName, gender });
      this.showToast('正在注册...');

      this.sendMessage({
        type: 'register',
        data: { username, password, characterName, gender }
      });
    },

    // 处理冒险
    handleAdventure: function() {
      const player = this.gameState.player;

      if (player.energy <= 0) {
        this.showToast('体力不足，无法冒险');
        return;
      }

      this.showToast('开始闯江湖...');
      this.sendMessage({
        type: 'adventure',
        action: 'start'
      });
    },

    // 切换场景
    switchScene: function(sceneName) {
      console.log('切换场景:', sceneName);
      this.gameState.currentScene = sceneName;
      this.render();
    },

    // 其他场景的点击处理（占位符）
    handleCharacterTap: function(x, y) {
      console.log('角色页面点击:', x, y);
      // TODO: 实现角色页面交互
    },

    handleSkillsTap: function(x, y) {
      console.log('武功页面点击:', x, y);
      // TODO: 实现武功页面交互
    },

    handleShopTap: function(x, y) {
      console.log('商店页面点击:', x, y);
      // TODO: 实现商店页面交互
    },

    handleGuildTap: function(x, y) {
      console.log('门派页面点击:', x, y);
      // TODO: 实现门派页面交互
    },

    handleBackpackTap: function(x, y) {
      console.log('背包页面点击:', x, y);
      // TODO: 实现背包页面交互
    }
  };
}

// 游戏初始化
async function initGame() {
  try {
    console.log('开始初始化游戏...');

    // 检查游戏实例是否创建成功
    if (!gameInstance) {
      throw new Error('游戏实例创建失败');
    }

    // 初始化游戏实例
    if (typeof gameInstance.init === 'function') {
      await gameInstance.init();
    } else {
      // 如果是内置实例，直接调用初始化
      gameInstance.init();
    }

    console.log('游戏初始化完成');

  } catch (error) {
    console.error('游戏初始化失败:', error);
    showErrorScreen('游戏初始化失败: ' + error.message);
  }
}

// 显示错误屏幕
function showErrorScreen(message) {
  try {
    const systemInfo = wx.getSystemInfoSync();
    const canvas = wx.createCanvas();
    const ctx = canvas.getContext('2d');

    canvas.width = systemInfo.screenWidth;
    canvas.height = systemInfo.screenHeight;

    // 绘制错误信息
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = '#dc3545';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

    ctx.fillStyle = '#6c757d';
    ctx.font = '16px Arial';
    ctx.fillText('小游戏版本', canvas.width / 2, 140);

    ctx.fillStyle = '#495057';
    ctx.font = '14px Arial';
    ctx.fillText('游戏启动失败', canvas.width / 2, 200);
    ctx.fillText(message, canvas.width / 2, 230);
    ctx.fillText('请重启小游戏重试', canvas.width / 2, 260);

    // 重试按钮
    ctx.fillStyle = '#007bff';
    ctx.fillRect(canvas.width / 2 - 60, 300, 120, 40);

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText('重试', canvas.width / 2, 325);

    // 添加重试触摸事件
    wx.onTouchEnd((e) => {
      if (!e.touches || e.touches.length === 0) return;

      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;

      // 检查重试按钮
      if (x >= canvas.width / 2 - 60 && x <= canvas.width / 2 + 60 &&
          y >= 300 && y <= 340) {
        console.log('重试按钮被点击');

        // 重新初始化游戏
        initGame();
      }
    });

  } catch (error) {
    console.error('显示错误屏幕失败:', error);
  }
}

// 启动游戏
console.log('启动游戏引擎...');

// 启动游戏实例
if (gameInstance) {
  initGame();
} else {
  console.error('游戏实例创建失败，无法启动游戏');
  showErrorScreen('游戏创建失败，请重试');
}

console.log('=== 仗剑江湖行小游戏启动完成 ===');
