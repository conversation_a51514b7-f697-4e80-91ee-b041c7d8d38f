// 仗剑江湖行 - 小游戏入口文件
console.log('=== 仗剑江湖行小游戏启动 ===');

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');
  
  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight
    });
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');
  });

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告:', res.level);
      if (wx.triggerGC) {
        wx.triggerGC();
      }
    });
  }

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');
}

// 小游戏主逻辑
console.log('开始初始化小游戏主逻辑...');

// 获取系统信息
const systemInfo = wx.getSystemInfoSync();

// 创建Canvas
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 设置Canvas尺寸
canvas.width = systemInfo.screenWidth;
canvas.height = systemInfo.screenHeight;

console.log('Canvas创建成功:', canvas.width, 'x', canvas.height);

// 游戏状态
let gameState = {
  currentScene: 'login',
  playerData: null,
  isLoggedIn: false
};

// 绘制登录界面
function drawLoginScene() {
  // 清空画布
  ctx.fillStyle = '#667eea';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制标题
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 32px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 120);

  // 绘制副标题
  ctx.font = '16px Arial';
  ctx.fillText('小游戏版本', canvas.width / 2, 160);

  // 绘制登录表单背景
  const formX = 40;
  const formY = 220;
  const formWidth = canvas.width - 80;
  const formHeight = 300;

  ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
  ctx.fillRect(formX, formY, formWidth, formHeight);

  // 绘制表单标题
  ctx.fillStyle = '#333333';
  ctx.font = 'bold 24px Arial';
  ctx.fillText('登录江湖', canvas.width / 2, formY + 50);

  // 绘制输入框提示
  ctx.font = '16px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('账号:', formX + 30, formY + 100);
  ctx.fillText('密码:', formX + 30, formY + 160);

  // 绘制输入框
  ctx.strokeStyle = '#cccccc';
  ctx.lineWidth = 1;
  ctx.strokeRect(formX + 30, formY + 110, formWidth - 60, 35);
  ctx.strokeRect(formX + 30, formY + 170, formWidth - 60, 35);

  // 绘制登录按钮
  ctx.fillStyle = '#3498db';
  ctx.fillRect(formX + 30, formY + 220, formWidth - 60, 45);

  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 18px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('登录', canvas.width / 2, formY + 248);

  // 绘制注册链接
  ctx.fillStyle = '#666666';
  ctx.font = '14px Arial';
  ctx.fillText('还没有账号？点击注册', canvas.width / 2, formY + 280);
}

// 绘制主游戏界面
function drawMainScene() {
  // 清空画布
  ctx.fillStyle = '#2c3e50';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制标题
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 24px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 50);

  // 绘制玩家信息
  if (gameState.playerData) {
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`角色: ${gameState.playerData.name || '无名侠客'}`, 20, 100);
    ctx.fillText(`等级: ${gameState.playerData.level || 1}`, 20, 130);
    ctx.fillText(`银两: ${gameState.playerData.money || 0}`, 20, 160);
  }

  // 绘制功能按钮
  const buttonWidth = 120;
  const buttonHeight = 40;
  const buttonSpacing = 20;
  const startY = 200;

  const buttons = ['角色', '武功', '商店', '门派', '背包'];

  buttons.forEach((text, index) => {
    const x = 20;
    const y = startY + index * (buttonHeight + buttonSpacing);

    ctx.fillStyle = '#3498db';
    ctx.fillRect(x, y, buttonWidth, buttonHeight);

    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(text, x + buttonWidth / 2, y + 25);
  });

  // 绘制返回登录按钮
  ctx.fillStyle = '#e74c3c';
  ctx.fillRect(canvas.width - 100, 20, 80, 30);
  ctx.fillStyle = '#ffffff';
  ctx.font = '14px Arial';
  ctx.fillText('退出', canvas.width - 60, 40);
}

// 触摸事件处理
wx.onTouchEnd((e) => {
  if (!e.touches || e.touches.length === 0) return;

  const touch = e.touches[0];
  const x = touch.clientX;
  const y = touch.clientY;

  console.log('触摸事件:', x, y, '当前场景:', gameState.currentScene);

  if (gameState.currentScene === 'login') {
    // 登录界面触摸处理
    const formX = 40;
    const formY = 220;
    const formWidth = canvas.width - 80;

    // 检查登录按钮
    if (x >= formX + 30 && x <= formX + formWidth - 30 &&
        y >= formY + 220 && y <= formY + 265) {
      console.log('登录按钮被点击');

      // 模拟登录成功
      gameState.isLoggedIn = true;
      gameState.currentScene = 'main';
      gameState.playerData = {
        name: '测试玩家',
        level: 1,
        money: 1000
      };

      drawMainScene();
    }

    // 检查注册链接
    if (y >= formY + 270 && y <= formY + 290) {
      console.log('注册链接被点击');
      wx.showToast({
        title: '注册功能开发中',
        icon: 'none'
      });
    }
  } else if (gameState.currentScene === 'main') {
    // 主界面触摸处理

    // 检查退出按钮
    if (x >= canvas.width - 100 && x <= canvas.width - 20 &&
        y >= 20 && y <= 50) {
      console.log('退出按钮被点击');
      gameState.currentScene = 'login';
      gameState.isLoggedIn = false;
      gameState.playerData = null;
      drawLoginScene();
      return;
    }

    // 检查功能按钮
    const buttonWidth = 120;
    const buttonHeight = 40;
    const buttonSpacing = 20;
    const startY = 200;

    const buttons = ['角色', '武功', '商店', '门派', '背包'];

    buttons.forEach((text, index) => {
      const buttonX = 20;
      const buttonY = startY + index * (buttonHeight + buttonSpacing);

      if (x >= buttonX && x <= buttonX + buttonWidth &&
          y >= buttonY && y <= buttonY + buttonHeight) {
        console.log(`${text}按钮被点击`);
        wx.showToast({
          title: `${text}功能开发中`,
          icon: 'none'
        });
      }
    });
  }
});

// 初始化游戏
function initGame() {
  console.log('初始化游戏界面...');
  drawLoginScene();
  console.log('游戏界面初始化完成');
}

// 启动游戏
initGame();

console.log('=== 仗剑江湖行小游戏启动完成 ===');
