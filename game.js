/**
 * 仗剑江湖行 - 微信小游戏入口文件
 * 完全重构版本，不依赖uni-app框架
 */

console.log('=== 仗剑江湖行小游戏启动 ===');

// 检查微信小游戏环境
if (typeof wx === 'undefined') {
  console.error('未检测到微信小游戏环境');
  throw new Error('此游戏只能在微信小游戏环境中运行');
}

// 导入所有必要的模块
let GameEngine, UIManager, InputManager, NetworkManager, SceneManager;
let BaseScene, LoginScene, IndexScene, CharacterScene, SkillsScene, ShopScene, GuildScene, BackpackScene;

try {
  // 导入游戏引擎
  const gameEngineModule = require('./gameEngine.js');
  GameEngine = gameEngineModule.GameEngine || gameEngineModule;

  // 导入UI管理器
  UIManager = require('./uiManager.js');

  // 导入输入管理器
  InputManager = require('./inputManager.js');

  // 导入网络管理器
  NetworkManager = require('./networkManager.js');

  // 导入场景管理器和所有场景
  const sceneModule = require('./sceneManager.js');
  SceneManager = sceneModule.SceneManager;
  BaseScene = sceneModule.BaseScene;
  LoginScene = sceneModule.LoginScene;
  IndexScene = sceneModule.IndexScene;
  CharacterScene = sceneModule.CharacterScene;
  SkillsScene = sceneModule.SkillsScene;
  ShopScene = sceneModule.ShopScene;
  GuildScene = sceneModule.GuildScene;
  BackpackScene = sceneModule.BackpackScene;

  console.log('所有模块导入成功');

} catch (error) {
  console.error('模块导入失败:', error);
  showErrorScreen('模块加载失败: ' + error.message);
  return;
}

// 全局变量
let gameEngine = null;

// 游戏初始化
async function initGame() {
  try {
    console.log('开始初始化游戏...');

    // 创建游戏引擎实例
    gameEngine = new GameEngine();

    // 等待游戏引擎初始化完成
    await gameEngine.init();

    console.log('游戏初始化完成');

  } catch (error) {
    console.error('游戏初始化失败:', error);
    showErrorScreen('游戏初始化失败: ' + error.message);
  }
}

// 显示错误屏幕
function showErrorScreen(message) {
  try {
    const systemInfo = wx.getSystemInfoSync();
    const canvas = wx.createCanvas();
    const ctx = canvas.getContext('2d');

    canvas.width = systemInfo.screenWidth;
    canvas.height = systemInfo.screenHeight;

    // 绘制错误信息
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = '#dc3545';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

    ctx.fillStyle = '#6c757d';
    ctx.font = '16px Arial';
    ctx.fillText('小游戏版本', canvas.width / 2, 140);

    ctx.fillStyle = '#495057';
    ctx.font = '14px Arial';
    ctx.fillText('游戏启动失败', canvas.width / 2, 200);
    ctx.fillText(message, canvas.width / 2, 230);
    ctx.fillText('请重启小游戏重试', canvas.width / 2, 260);

    // 重试按钮
    ctx.fillStyle = '#007bff';
    ctx.fillRect(canvas.width / 2 - 60, 300, 120, 40);

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText('重试', canvas.width / 2, 325);

    // 添加重试触摸事件
    wx.onTouchEnd((e) => {
      if (!e.touches || e.touches.length === 0) return;

      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;

      // 检查重试按钮
      if (x >= canvas.width / 2 - 60 && x <= canvas.width / 2 + 60 &&
          y >= 300 && y <= 340) {
        console.log('重试按钮被点击');

        // 重新初始化游戏
        initGame();
      }
    });

  } catch (error) {
    console.error('显示错误屏幕失败:', error);
  }
}

// 启动游戏
console.log('启动游戏引擎...');
initGame();

console.log('=== 仗剑江湖行小游戏启动完成 ===');
