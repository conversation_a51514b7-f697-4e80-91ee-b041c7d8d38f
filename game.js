// 仗剑江湖行 - 小游戏入口文件
console.log('=== 仗剑江湖行小游戏启动 ===');

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');
  
  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight
    });
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');
  });

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告:', res.level);
      if (wx.triggerGC) {
        wx.triggerGC();
      }
    });
  }

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');
}

// 小游戏主逻辑
console.log('开始初始化小游戏主逻辑...');

// 导入完整的游戏适配器
const CompleteGameAdapter = require('./completeGameAdapter.js');

// 创建游戏适配器实例
let gameAdapter = null;

try {
  gameAdapter = new CompleteGameAdapter();
  console.log('完整游戏适配器创建成功');
} catch (error) {
  console.error('游戏适配器创建失败:', error);

  // 降级到简单模式
  console.log('降级到简单模式...');

  // 获取系统信息
  const systemInfo = wx.getSystemInfoSync();

  // 创建Canvas
  const canvas = wx.createCanvas();
  const ctx = canvas.getContext('2d');

  // 设置Canvas尺寸
  canvas.width = systemInfo.screenWidth;
  canvas.height = systemInfo.screenHeight;

  console.log('简单模式Canvas创建成功:', canvas.width, 'x', canvas.height);

  // 绘制错误信息
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  ctx.fillStyle = '#dc3545';
  ctx.font = 'bold 24px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

  ctx.fillStyle = '#6c757d';
  ctx.font = '16px Arial';
  ctx.fillText('小游戏版本', canvas.width / 2, 140);

  ctx.fillStyle = '#495057';
  ctx.font = '14px Arial';
  ctx.fillText('正在加载完整功能...', canvas.width / 2, 200);
  ctx.fillText('如果长时间无响应，请重启小游戏', canvas.width / 2, 230);

  // 重试按钮
  ctx.fillStyle = '#007bff';
  ctx.fillRect(canvas.width / 2 - 60, 280, 120, 40);

  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 16px Arial';
  ctx.fillText('重试', canvas.width / 2, 305);

  // 添加重试触摸事件
  wx.onTouchEnd((e) => {
    if (!e.touches || e.touches.length === 0) return;

    const touch = e.touches[0];
    const x = touch.clientX;
    const y = touch.clientY;

    // 检查重试按钮
    if (x >= canvas.width / 2 - 60 && x <= canvas.width / 2 + 60 &&
        y >= 280 && y <= 320) {
      console.log('重试按钮被点击');

      // 重新加载页面
      wx.exitMiniProgram();
    }
  });
}

console.log('=== 仗剑江湖行小游戏启动完成 ===');
