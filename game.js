// 仗剑江湖行 - 小游戏入口文件
// 此文件是小游戏的主入口，负责初始化小游戏环境

console.log('=== 仗剑江湖行小游戏启动 ===');

// 引入uni-app框架适配器
import './uni.promisify.adaptor'

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');

  // 云开发初始化（如果需要）
  if (wx.cloud) {
    try {
      wx.cloud.init({
        traceUser: true
      });
      console.log('云开发初始化成功');
    } catch (error) {
      console.warn('云开发初始化失败:', error);
    }
  }

  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      pixelRatio: systemInfo.pixelRatio
    });

    // 设置全局系统信息
    if (typeof global !== 'undefined') {
      global.__SYSTEM_INFO__ = systemInfo;
    }
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 性能监控
  if (wx.getPerformance) {
    const performance = wx.getPerformance();
    console.log('性能信息:', performance);
  }

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告 - 级别:', res.level);
      // 触发垃圾回收
      if (wx.triggerGC) {
        wx.triggerGC();
      }
    });
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');
  });

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');
}

// 引入主应用
import './main.js'

// 游戏启动完成
console.log('=== 仗剑江湖行小游戏启动完成 ===');
