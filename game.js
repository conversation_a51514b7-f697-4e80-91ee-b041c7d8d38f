// 仗剑江湖行 - 小游戏入口文件
// 此文件是小游戏的主入口，负责初始化小游戏环境并启动完整的游戏功能

console.log('=== 仗剑江湖行小游戏启动 ===');

// 全局游戏实例
let gameAdapter = null;

// 小游戏环境初始化
if (typeof wx !== 'undefined') {
  console.log('初始化微信小游戏环境...');

  // 云开发初始化（如果需要）
  if (wx.cloud) {
    try {
      wx.cloud.init({
        traceUser: true
      });
      console.log('云开发初始化成功');
    } catch (error) {
      console.warn('云开发初始化失败:', error);
    }
  }

  // 获取系统信息
  try {
    const systemInfo = wx.getSystemInfoSync();
    console.log('小游戏系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      pixelRatio: systemInfo.pixelRatio
    });

    // 设置全局系统信息
    if (typeof global !== 'undefined') {
      global.__SYSTEM_INFO__ = systemInfo;
      global.__MINI_GAME__ = true;
    }
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }

  // 性能监控
  if (wx.getPerformance) {
    const performance = wx.getPerformance();
    console.log('性能信息:', performance);
  }

  // 内存监控
  if (wx.onMemoryWarning) {
    wx.onMemoryWarning((res) => {
      console.warn('内存警告 - 级别:', res.level);
      // 触发垃圾回收
      if (wx.triggerGC) {
        wx.triggerGC();
      }

      // 通知游戏适配器进行内存清理
      if (gameAdapter && typeof gameAdapter.handleMemoryWarning === 'function') {
        gameAdapter.handleMemoryWarning(res.level);
      }
    });
  }

  // 小游戏生命周期管理
  wx.onShow(() => {
    console.log('小游戏进入前台');

    // 通知游戏适配器恢复游戏
    if (gameAdapter && typeof gameAdapter.onShow === 'function') {
      gameAdapter.onShow();
    }
  });

  wx.onHide(() => {
    console.log('小游戏进入后台');

    // 通知游戏适配器暂停游戏
    if (gameAdapter && typeof gameAdapter.onHide === 'function') {
      gameAdapter.onHide();
    }
  });

  // 初始化游戏适配器
  try {
    // 导入游戏适配器
    const MiniGameAdapter = require('./miniGameAdapter.js');

    // 创建游戏实例
    gameAdapter = new MiniGameAdapter();

    // 设置全局游戏实例
    if (typeof global !== 'undefined') {
      global.__GAME_ADAPTER__ = gameAdapter;
    }

    console.log('游戏适配器初始化成功');

  } catch (error) {
    console.error('游戏适配器初始化失败:', error);

    // 降级到简单模式
    initSimpleMode();
  }

  console.log('微信小游戏环境初始化完成');
} else {
  console.warn('未检测到微信小游戏环境');

  // 非微信环境的处理
  initSimpleMode();
}

// 简单模式初始化（降级方案）
function initSimpleMode() {
  console.log('启动简单模式...');

  if (typeof wx !== 'undefined') {
    // 创建Canvas用于游戏渲染
    const canvas = wx.createCanvas();
    const ctx = canvas.getContext('2d');

    // 设置Canvas尺寸
    const systemInfo = wx.getSystemInfoSync();
    canvas.width = systemInfo.screenWidth;
    canvas.height = systemInfo.screenHeight;

    console.log('Canvas创建成功，尺寸:', canvas.width, 'x', canvas.height);

    // 简单的游戏界面
    function drawGameUI() {
      // 清空画布
      ctx.fillStyle = '#2c3e50';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 绘制标题
      ctx.fillStyle = '#ffffff';
      ctx.font = '32px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

      // 绘制提示信息
      ctx.font = '16px Arial';
      ctx.fillText('正在加载完整版本...', canvas.width / 2, 200);
      ctx.fillText('请稍候', canvas.width / 2, 230);

      // 绘制按钮
      ctx.fillStyle = '#3498db';
      ctx.fillRect(canvas.width / 2 - 100, 300, 200, 50);

      ctx.fillStyle = '#ffffff';
      ctx.fillText('重新加载', canvas.width / 2, 330);
    }

    // 触摸事件处理
    wx.onTouchStart((e) => {
      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;

      // 检查是否点击了重新加载按钮
      if (x >= canvas.width / 2 - 100 && x <= canvas.width / 2 + 100 &&
          y >= 300 && y <= 350) {
        // 尝试重新初始化游戏适配器
        try {
          const MiniGameAdapter = require('./miniGameAdapter.js');
          gameAdapter = new MiniGameAdapter();
          console.log('游戏适配器重新加载成功');
        } catch (error) {
          console.error('游戏适配器重新加载失败:', error);
          wx.showToast({
            title: '加载失败，请重启游戏',
            icon: 'none'
          });
        }
      }
    });

    // 初始化游戏界面
    drawGameUI();
  }
}

// 游戏启动完成
console.log('=== 仗剑江湖行小游戏启动完成 ===');
