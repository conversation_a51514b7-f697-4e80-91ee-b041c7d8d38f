@echo off
chcp 65001 >nul
echo ========================================
echo    仗剑江湖行 - 完整小游戏构建工具
echo ========================================
echo.

echo 正在构建包含所有原有功能的小游戏版本...
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

echo [1/6] 清理旧的构建文件...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%" 2>nul
    echo ✓ 已清理旧的构建文件
)

echo.
echo [2/6] 创建小游戏目录结构...
mkdir "%BUILD_DIR%" 2>nul
mkdir "%BUILD_DIR%\utils" 2>nul
echo ✓ 目录结构创建完成

echo.
echo [3/6] 复制核心文件...

REM 复制小游戏配置文件
copy "game.json" "%BUILD_DIR%\game.json" >nul 2>&1
if exist "%BUILD_DIR%\game.json" (
    echo ✓ game.json 已复制
) else (
    echo ❌ game.json 复制失败
)

REM 复制小游戏入口文件
copy "game.js" "%BUILD_DIR%\game.js" >nul 2>&1
if exist "%BUILD_DIR%\game.js" (
    echo ✓ game.js 已复制
) else (
    echo ❌ game.js 复制失败
)

REM 复制兼容性补丁
copy "mini-game-patch.js" "%BUILD_DIR%\mini-game-patch.js" >nul 2>&1
if exist "%BUILD_DIR%\mini-game-patch.js" (
    echo ✓ mini-game-patch.js 已复制
) else (
    echo ❌ mini-game-patch.js 复制失败
)

REM 复制小游戏适配器
copy "miniGameAdapter.js" "%BUILD_DIR%\miniGameAdapter.js" >nul 2>&1
if exist "%BUILD_DIR%\miniGameAdapter.js" (
    echo ✓ miniGameAdapter.js 已复制
) else (
    echo ❌ miniGameAdapter.js 复制失败
)

REM 复制小游戏WebSocket管理器
copy "miniGameWebSocket.js" "%BUILD_DIR%\miniGameWebSocket.js" >nul 2>&1
if exist "%BUILD_DIR%\miniGameWebSocket.js" (
    echo ✓ miniGameWebSocket.js 已复制
) else (
    echo ❌ miniGameWebSocket.js 复制失败
)

echo.
echo [4/6] 复制工具文件...

REM 复制游戏数据文件
if exist "utils\gameData.js" (
    copy "utils\gameData.js" "%BUILD_DIR%\utils\gameData.js" >nul 2>&1
    echo ✓ gameData.js 已复制
) else (
    echo ⚠️  gameData.js 不存在
)

REM 复制游戏状态文件
if exist "utils\gameState.js" (
    copy "utils\gameState.js" "%BUILD_DIR%\utils\gameState.js" >nul 2>&1
    echo ✓ gameState.js 已复制
) else (
    echo ⚠️  gameState.js 不存在
)

REM 复制原始WebSocket文件作为参考
if exist "utils\websocket.js" (
    copy "utils\websocket.js" "%BUILD_DIR%\utils\websocket.js" >nul 2>&1
    echo ✓ websocket.js 已复制
) else (
    echo ⚠️  websocket.js 不存在
)

echo.
echo [5/6] 创建小游戏入口文件...

echo // 仗剑江湖行 - 小游戏主入口> "%BUILD_DIR%\app.js"
echo // 引入兼容性补丁>> "%BUILD_DIR%\app.js"
echo require('./mini-game-patch.js');>> "%BUILD_DIR%\app.js"
echo.>> "%BUILD_DIR%\app.js"
echo // 引入游戏主逻辑>> "%BUILD_DIR%\app.js"
echo require('./game.js');>> "%BUILD_DIR%\app.js"
echo.>> "%BUILD_DIR%\app.js"
echo console.log('小游戏 app.js 加载完成');>> "%BUILD_DIR%\app.js"

echo ✓ app.js 已创建

echo.
echo [6/6] 创建项目配置文件...

echo {> "%BUILD_DIR%\project.config.json"
echo   "description": "仗剑江湖行小游戏 - 完整功能版本",>> "%BUILD_DIR%\project.config.json"
echo   "packOptions": {>> "%BUILD_DIR%\project.config.json"
echo     "ignore": []>> "%BUILD_DIR%\project.config.json"
echo   },>> "%BUILD_DIR%\project.config.json"
echo   "setting": {>> "%BUILD_DIR%\project.config.json"
echo     "urlCheck": false,>> "%BUILD_DIR%\project.config.json"
echo     "es6": true,>> "%BUILD_DIR%\project.config.json"
echo     "enhance": true,>> "%BUILD_DIR%\project.config.json"
echo     "postcss": true,>> "%BUILD_DIR%\project.config.json"
echo     "minified": true,>> "%BUILD_DIR%\project.config.json"
echo     "newFeature": false,>> "%BUILD_DIR%\project.config.json"
echo     "coverView": true,>> "%BUILD_DIR%\project.config.json"
echo     "nodeModules": false,>> "%BUILD_DIR%\project.config.json"
echo     "autoAudits": false,>> "%BUILD_DIR%\project.config.json"
echo     "showShadowRootInWxmlPanel": true,>> "%BUILD_DIR%\project.config.json"
echo     "scopeDataCheck": false,>> "%BUILD_DIR%\project.config.json"
echo     "uglifyFileName": false,>> "%BUILD_DIR%\project.config.json"
echo     "checkInvalidKey": true,>> "%BUILD_DIR%\project.config.json"
echo     "checkSiteMap": true,>> "%BUILD_DIR%\project.config.json"
echo     "uploadWithSourceMap": true,>> "%BUILD_DIR%\project.config.json"
echo     "compileHotReLoad": false,>> "%BUILD_DIR%\project.config.json"
echo     "lazyloadPlaceholderEnable": false,>> "%BUILD_DIR%\project.config.json"
echo     "useMultiFrameRuntime": true,>> "%BUILD_DIR%\project.config.json"
echo     "useApiHook": true,>> "%BUILD_DIR%\project.config.json"
echo     "useApiHostProcess": true,>> "%BUILD_DIR%\project.config.json"
echo     "babelSetting": {>> "%BUILD_DIR%\project.config.json"
echo       "ignore": [],>> "%BUILD_DIR%\project.config.json"
echo       "disablePlugins": [],>> "%BUILD_DIR%\project.config.json"
echo       "outputPath": "">> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "useIsolateContext": true,>> "%BUILD_DIR%\project.config.json"
echo     "userConfirmedBundleSwitch": false,>> "%BUILD_DIR%\project.config.json"
echo     "packNpmManually": false,>> "%BUILD_DIR%\project.config.json"
echo     "packNpmRelationList": [],>> "%BUILD_DIR%\project.config.json"
echo     "minifyWXSS": true,>> "%BUILD_DIR%\project.config.json"
echo     "disableUseStrict": false,>> "%BUILD_DIR%\project.config.json"
echo     "minifyWXML": true,>> "%BUILD_DIR%\project.config.json"
echo     "showES6CompileOption": false,>> "%BUILD_DIR%\project.config.json"
echo     "useCompilerPlugins": false>> "%BUILD_DIR%\project.config.json"
echo   },>> "%BUILD_DIR%\project.config.json"
echo   "compileType": "miniprogram",>> "%BUILD_DIR%\project.config.json"
echo   "libVersion": "2.19.4",>> "%BUILD_DIR%\project.config.json"
echo   "appid": "wxfb9c395829d83b91",>> "%BUILD_DIR%\project.config.json"
echo   "projectname": "仗剑江湖行",>> "%BUILD_DIR%\project.config.json"
echo   "debugOptions": {>> "%BUILD_DIR%\project.config.json"
echo     "hidedInDevtools": []>> "%BUILD_DIR%\project.config.json"
echo   },>> "%BUILD_DIR%\project.config.json"
echo   "scripts": {},>> "%BUILD_DIR%\project.config.json"
echo   "staticServerOptions": {>> "%BUILD_DIR%\project.config.json"
echo     "baseURL": "",>> "%BUILD_DIR%\project.config.json"
echo     "servePath": "">> "%BUILD_DIR%\project.config.json"
echo   },>> "%BUILD_DIR%\project.config.json"
echo   "isGameTourist": false,>> "%BUILD_DIR%\project.config.json"
echo   "condition": {>> "%BUILD_DIR%\project.config.json"
echo     "search": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "conversation": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "game": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "plugin": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "gamePlugin": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     },>> "%BUILD_DIR%\project.config.json"
echo     "miniprogram": {>> "%BUILD_DIR%\project.config.json"
echo       "list": []>> "%BUILD_DIR%\project.config.json"
echo     }>> "%BUILD_DIR%\project.config.json"
echo   }>> "%BUILD_DIR%\project.config.json"
echo }>> "%BUILD_DIR%\project.config.json"

echo ✓ project.config.json 已创建

echo.
echo ========================================
echo         完整小游戏构建完成！
echo ========================================
echo.
echo 📁 小游戏目录：%cd%\%BUILD_DIR%
echo.
echo 🎮 功能特性：
echo ✅ 完整的游戏逻辑（角色、武功、商店、门派等）
echo ✅ WebSocket 实时通信
echo ✅ Canvas 渲染系统
echo ✅ 触摸交互支持
echo ✅ 小游戏 API 兼容性
echo ✅ 内存管理和性能优化
echo.
echo 🚀 在微信开发者工具中导入：
echo 1. 打开微信开发者工具
echo 2. 选择"小游戏"项目类型
echo 3. 项目目录：%cd%\%BUILD_DIR%
echo 4. AppID：wxfb9c395829d83b91
echo 5. 点击"导入"
echo.
echo 📋 注意事项：
echo • 确保后端服务器正在运行（运行 start_server.bat）
echo • 小游戏使用 Canvas 渲染，界面与原版略有不同
echo • 所有原有功能都已保留并适配到小游戏环境
echo • 支持与原版后端服务器完全兼容的通信
echo.
pause
