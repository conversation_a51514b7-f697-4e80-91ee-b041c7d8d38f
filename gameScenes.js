/**
 * 游戏场景渲染和处理
 */

// 场景渲染方法
const GameScenes = {
  // 渲染登录场景
  renderLoginScene: function(gameInstance) {
    const { ctx, canvas, gameState, RenderUtils, uiConfig } = gameInstance;
    
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制标题
    RenderUtils.drawText(ctx, '仗剑江湖行', canvas.width / 2, 80, {
      font: uiConfig.fonts.title,
      color: uiConfig.colors.white,
      align: 'center'
    });
    
    RenderUtils.drawText(ctx, '微信小游戏版', canvas.width / 2, 130, {
      font: uiConfig.fonts.normal,
      color: uiConfig.colors.white,
      align: 'center'
    });
    
    // 绘制表单
    const formX = 30;
    const formY = 180;
    const formWidth = canvas.width - 60;
    const formHeight = gameState.isRegistering ? 500 : 320;
    
    ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
    ctx.fillRect(formX, formY, formWidth, formHeight);
    
    if (gameState.isRegistering) {
      this.renderRegisterForm(gameInstance, formX, formY, formWidth);
    } else {
      this.renderLoginForm(gameInstance, formX, formY, formWidth);
    }
  },
  
  // 渲染登录表单
  renderLoginForm: function(gameInstance, formX, formY, formWidth) {
    const { ctx, gameState, RenderUtils, uiConfig } = gameInstance;
    
    // 表单标题
    RenderUtils.drawText(ctx, '登录江湖', formX + formWidth / 2, formY + 30, {
      font: uiConfig.fonts.large,
      color: uiConfig.colors.text,
      align: 'center'
    });
    
    // 账号输入框
    RenderUtils.drawText(ctx, '账号:', formX + 20, formY + 80);
    gameState.usernameInput = RenderUtils.drawInputBox(
      ctx, formX + 20, formY + 105, formWidth - 40, 40,
      gameState.formData.username, '请输入账号'
    );
    
    // 密码输入框
    RenderUtils.drawText(ctx, '密码:', formX + 20, formY + 160);
    gameState.passwordInput = RenderUtils.drawInputBox(
      ctx, formX + 20, formY + 185, formWidth - 40, 40,
      gameState.formData.password ? '••••••••' : '', '请输入密码'
    );
    
    // 登录按钮
    gameState.loginButton = RenderUtils.drawButton(
      ctx, '登录', formX + 20, formY + 245, formWidth - 40, 45,
      { backgroundColor: uiConfig.colors.primary, font: 'bold 20px Arial' }
    );
    
    // 注册链接
    RenderUtils.drawText(ctx, '还没有账号？点击注册', formX + formWidth / 2, formY + 310, {
      font: uiConfig.fonts.small,
      color: uiConfig.colors.secondary,
      align: 'center'
    });
    
    gameState.registerLink = {
      x: formX + 20,
      y: formY + 300,
      width: formWidth - 40,
      height: 20
    };
  },
  
  // 渲染注册表单
  renderRegisterForm: function(gameInstance, formX, formY, formWidth) {
    const { ctx, gameState, RenderUtils, uiConfig } = gameInstance;
    
    // 表单标题
    RenderUtils.drawText(ctx, '创建角色', formX + formWidth / 2, formY + 30, {
      font: uiConfig.fonts.large,
      color: uiConfig.colors.text,
      align: 'center'
    });
    
    let currentY = formY + 70;
    const fieldHeight = 65;
    
    // 账号输入框
    RenderUtils.drawText(ctx, '账号:', formX + 20, currentY);
    gameState.regUsernameInput = RenderUtils.drawInputBox(
      ctx, formX + 20, currentY + 20, formWidth - 40, 35,
      gameState.registerData.username, '请输入账号'
    );
    currentY += fieldHeight;
    
    // 密码输入框
    RenderUtils.drawText(ctx, '密码:', formX + 20, currentY);
    gameState.regPasswordInput = RenderUtils.drawInputBox(
      ctx, formX + 20, currentY + 20, formWidth - 40, 35,
      gameState.registerData.password ? '••••••••' : '', '请输入密码'
    );
    currentY += fieldHeight;
    
    // 确认密码输入框
    RenderUtils.drawText(ctx, '确认密码:', formX + 20, currentY);
    gameState.regConfirmPasswordInput = RenderUtils.drawInputBox(
      ctx, formX + 20, currentY + 20, formWidth - 40, 35,
      gameState.registerData.confirmPassword ? '••••••••' : '', '请再次输入密码'
    );
    currentY += fieldHeight;
    
    // 角色名输入框
    RenderUtils.drawText(ctx, '角色名:', formX + 20, currentY);
    gameState.regCharacterNameInput = RenderUtils.drawInputBox(
      ctx, formX + 20, currentY + 20, formWidth - 40, 35,
      gameState.registerData.characterName, '请输入角色名'
    );
    currentY += fieldHeight;
    
    // 性别选择
    RenderUtils.drawText(ctx, '性别:', formX + 20, currentY);
    gameState.maleButton = RenderUtils.drawButton(
      ctx, '男', formX + 20, currentY + 20, 80, 35,
      {
        backgroundColor: gameState.registerData.gender === 'male' ? 
          uiConfig.colors.primary : uiConfig.colors.background,
        textColor: gameState.registerData.gender === 'male' ? 
          uiConfig.colors.white : uiConfig.colors.text
      }
    );
    
    gameState.femaleButton = RenderUtils.drawButton(
      ctx, '女', formX + 120, currentY + 20, 80, 35,
      {
        backgroundColor: gameState.registerData.gender === 'female' ? 
          uiConfig.colors.primary : uiConfig.colors.background,
        textColor: gameState.registerData.gender === 'female' ? 
          uiConfig.colors.white : uiConfig.colors.text
      }
    );
    currentY += 70;
    
    // 注册按钮
    gameState.registerButton = RenderUtils.drawButton(
      ctx, '创建角色', formX + 20, currentY, formWidth - 40, 45,
      { backgroundColor: uiConfig.colors.success, font: 'bold 20px Arial' }
    );
    
    // 返回登录链接
    RenderUtils.drawText(ctx, '已有账号？返回登录', formX + formWidth / 2, currentY + 65, {
      font: uiConfig.fonts.small,
      color: uiConfig.colors.secondary,
      align: 'center'
    });
    
    gameState.backToLoginLink = {
      x: formX + 20,
      y: currentY + 55,
      width: formWidth - 40,
      height: 20
    };
  },
  
  // 渲染主页面
  renderIndexScene: function(gameInstance) {
    const { ctx, canvas, gameState, RenderUtils, uiConfig } = gameInstance;
    
    // 清空画布
    RenderUtils.clear(ctx, canvas, uiConfig.colors.background);
    
    // 绘制头部
    ctx.fillStyle = uiConfig.colors.primary;
    ctx.fillRect(0, 0, canvas.width, 60);
    
    RenderUtils.drawText(ctx, '仗剑江湖行', canvas.width / 2, 30, {
      font: uiConfig.fonts.large,
      color: uiConfig.colors.white,
      align: 'center',
      baseline: 'middle'
    });
    
    // 绘制连接状态
    const statusColor = gameState.connectionStatus === '已连接' ? 
      uiConfig.colors.success : uiConfig.colors.danger;
    RenderUtils.drawText(ctx, gameState.connectionStatus, canvas.width - 20, 30, {
      font: uiConfig.fonts.small,
      color: statusColor,
      align: 'right',
      baseline: 'middle'
    });
    
    // 绘制玩家信息卡片
    const cardY = 80;
    const cardHeight = 120;
    RenderUtils.drawCard(ctx, 20, cardY, canvas.width - 40, cardHeight);
    
    const player = gameState.player;
    
    // 角色名和等级
    RenderUtils.drawText(ctx, `${player.character_name || player.name || '未知'}`, 30, cardY + 15, {
      font: uiConfig.fonts.large,
      color: uiConfig.colors.text
    });
    
    RenderUtils.drawText(ctx, `等级 ${player.level || 1}`, 30, cardY + 45, {
      font: uiConfig.fonts.normal,
      color: uiConfig.colors.textSecondary
    });
    
    // 属性进度条
    const barY = cardY + 70;
    const barWidth = (canvas.width - 80) / 2;
    
    // 气血
    const hpPercent = (player.hp || 0) / Math.max(player.max_hp || 100, 1);
    RenderUtils.drawProgressBar(ctx, 30, barY, barWidth, 20, hpPercent, {
      progressColor: '#e74c3c',
      text: `气血 ${player.hp || 0}/${player.max_hp || 100}`
    });
    
    // 内力
    const mpPercent = (player.mp || 0) / Math.max(player.max_mp || 50, 1);
    RenderUtils.drawProgressBar(ctx, 50 + barWidth, barY, barWidth, 20, mpPercent, {
      progressColor: '#3498db',
      text: `内力 ${player.mp || 0}/${player.max_mp || 50}`
    });
    
    // 银两和经验
    RenderUtils.drawText(ctx, `银两: ${this.formatNumber(player.money || 0)}`, 30, cardY + 100, {
      font: uiConfig.fonts.normal,
      color: uiConfig.colors.text
    });
    
    RenderUtils.drawText(ctx, `经验: ${this.formatNumber(player.experience || 0)}`, 200, cardY + 100, {
      font: uiConfig.fonts.normal,
      color: uiConfig.colors.text
    });
    
    // 功能按钮
    const buttonY = 220;
    gameState.adventureButton = RenderUtils.drawButton(
      ctx, '闯江湖', 30, buttonY, canvas.width - 60, 50,
      { backgroundColor: uiConfig.colors.success, font: 'bold 20px Arial' }
    );
    
    // 事件日志
    const logY = 290;
    RenderUtils.drawText(ctx, '江湖动态', 30, logY, {
      font: uiConfig.fonts.large,
      color: uiConfig.colors.text
    });
    
    const logs = gameState.eventLog.slice(0, 5);
    logs.forEach((log, index) => {
      RenderUtils.drawText(ctx, log, 30, logY + 30 + index * 25, {
        font: uiConfig.fonts.small,
        color: uiConfig.colors.textSecondary,
        maxWidth: canvas.width - 60
      });
    });
    
    // 绘制底部导航栏
    const tabs = [
      { key: 'character', name: '角色' },
      { key: 'skills', name: '武功' },
      { key: 'index', name: '闯' },
      { key: 'shop', name: '商店' },
      { key: 'guild', name: '门派' }
    ];
    
    gameState.tabButtons = RenderUtils.drawTabBar(ctx, canvas, tabs, 'index');
  },
  
  // 格式化数字
  formatNumber: function(num) {
    if (num >= 10000) {
      return Math.floor(num / 10000) + '万';
    }
    return num.toString();
  }
};

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GameScenes;
} else {
  window.GameScenes = GameScenes;
}
