{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__89070E0", "name": "仗剑江湖行", "version": {"name": "1.0.0", "code": "100"}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#2c3e50"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.75", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#7A7E83", "selectedColor": "#ff6b6b", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "24rpx", "iconWidth": "48rpx", "spacing": "3px", "height": "120px", "list": [{"pagePath": "pages/character/character", "text": "角色", "iconPath": "/static/tabbar/backpack.png", "selectedIconPath": "/static/tabbar/backpack-active.png"}, {"pagePath": "pages/skills/skills", "text": "武功", "iconPath": "/static/tabbar/skills.png", "selectedIconPath": "/static/tabbar/skills-active.png"}, {"pagePath": "pages/index/index", "text": "江湖", "iconPath": "/static/tabbar/adventure.png", "selectedIconPath": "/static/tabbar/adventure-active.png"}, {"pagePath": "pages/shop/shop", "text": "市场", "iconPath": "/static/tabbar/shop.png", "selectedIconPath": "/static/tabbar/shop-active.png"}, {"pagePath": "pages/guild/guild", "text": "门派", "iconPath": "/static/tabbar/guild.png", "selectedIconPath": "/static/tabbar/guild-active.png"}], "backgroundColor": "#ffffff", "selectedIndex": 0, "shown": true}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}}, "locale": "zh-Hans", "launch_path": "__uniappview.html"}