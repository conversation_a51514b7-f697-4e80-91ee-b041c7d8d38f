// 小游戏全局兼容性补丁
// 此文件必须在所有其他模块之前加载

(function() {
  'use strict';
  
  console.log('开始应用小游戏兼容性补丁');

  // 获取全局对象
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  // 确保 global 对象存在
  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
    console.log('已添加 global 对象');
  }

  // 小游戏环境检测和 API 补丁
  if (typeof wx !== 'undefined') {
    console.log('检测到微信小游戏环境');

    // wx.canIUse 方法 - 最高优先级
    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = [
          'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
          'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
          'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
          'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
          'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage',
          'removeStorage', 'clearStorage', 'setStorageSync', 'getStorageSync',
          'removeStorageSync', 'clearStorageSync', 'onShow', 'onHide', 'offShow',
          'offHide', 'exitMiniProgram', 'navigateToMiniProgram', 'getUpdateManager'
        ];
        
        if (gameAPIs.includes(apiName)) {
          return typeof wx[apiName] === 'function';
        }
        return typeof wx[apiName] !== 'undefined';
      };
      console.log('已添加 wx.canIUse 方法');
    }

    // 基础 API 兼容性
    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
      console.log('已添加 wx.getAppBaseInfo');
    }
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
      console.log('已添加 wx.getWindowInfo');
    }
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
      console.log('已添加 wx.getDeviceInfo');
    }
    if (!wx.getSystemSetting && wx.getSystemInfoSync) {
      wx.getSystemSetting = wx.getSystemInfoSync;
      console.log('已添加 wx.getSystemSetting');
    }
    if (!wx.getAppAuthorizeSetting) {
      wx.getAppAuthorizeSetting = function() {
        return {
          albumAuthorized: 'authorized',
          bluetoothAuthorized: 'authorized',
          cameraAuthorized: 'authorized',
          locationAuthorized: 'authorized',
          locationReducedAccuracy: false,
          microphoneAuthorized: 'authorized',
          notificationAuthorized: 'authorized'
        };
      };
      console.log('已添加 wx.getAppAuthorizeSetting');
    }

    // 全局函数兼容性
    if (typeof Page === 'undefined') {
      globalObj.Page = function(options) {
        return options;
      };
      console.log('已添加 Page 函数');
    }
    if (typeof Component === 'undefined') {
      globalObj.Component = function(options) {
        return options;
      };
      console.log('已添加 Component 函数');
    }
    if (typeof App === 'undefined') {
      globalObj.App = function(options) {
        return options;
      };
      console.log('已添加 App 函数');
    }
    if (typeof getApp === 'undefined') {
      globalObj.getApp = function(options) {
        return { $vm: null, globalData: {} };
      };
      console.log('已添加 getApp 函数');
    }
    if (typeof getCurrentPages === 'undefined') {
      globalObj.getCurrentPages = function() {
        return [];
      };
      console.log('已添加 getCurrentPages 函数');
    }

    console.log('小游戏兼容性补丁应用完成');
  } else {
    console.log('未检测到微信环境');
  }
})();
