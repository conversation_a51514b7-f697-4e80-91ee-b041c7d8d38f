"use strict";
const common_vendor = require("./common/vendor.js");
(function() {
  console.log("开始应用小游戏兼容性补丁");
  const globalObj = function() {
    if (typeof globalThis !== "undefined")
      return globalThis;
    if (typeof window !== "undefined")
      return window;
    if (typeof global !== "undefined")
      return global;
    if (typeof self !== "undefined")
      return self;
    return this;
  }();
  if (typeof global === "undefined") {
    globalObj.global = globalObj;
    console.log("已添加 global 对象");
  }
  if (typeof common_vendor.wx$1 !== "undefined") {
    console.log("检测到微信小游戏环境");
    if (!common_vendor.wx$1.canIUse) {
      common_vendor.wx$1.canIUse = function(apiName) {
        const gameAPIs = [
          "getSystemInfoSync",
          "getSystemInfo",
          "getAppBaseInfo",
          "getWindowInfo",
          "getDeviceInfo",
          "getSystemSetting",
          "getAppAuthorizeSetting",
          "request",
          "connectSocket",
          "onSocketOpen",
          "onSocketClose",
          "onSocketMessage",
          "onSocketError",
          "sendSocketMessage",
          "closeSocket",
          "showToast",
          "showModal",
          "showLoading",
          "hideLoading",
          "setStorage",
          "getStorage",
          "removeStorage",
          "clearStorage",
          "setStorageSync",
          "getStorageSync",
          "removeStorageSync",
          "clearStorageSync",
          "onShow",
          "onHide",
          "offShow",
          "offHide",
          "exitMiniProgram",
          "navigateToMiniProgram",
          "getUpdateManager",
          "createCanvas",
          "createImage",
          "createInnerAudioContext",
          "getFileSystemManager",
          "downloadFile",
          "uploadFile",
          "createRequestTask",
          "createDownloadTask",
          "createUploadTask",
          "createWebAudioContext",
          "createVideoDecoder",
          "createOffscreenCanvas",
          "createWorker",
          "getPerformance",
          "triggerGC",
          "onMemoryWarning",
          "offMemoryWarning",
          "getMenuButtonBoundingClientRect",
          "setKeepScreenOn",
          "getScreenBrightness",
          "setScreenBrightness",
          "onDeviceMotionChange",
          "offDeviceMotionChange",
          "startDeviceMotionListening",
          "stopDeviceMotionListening",
          "onCompassChange",
          "offCompassChange",
          "startCompass",
          "stopCompass",
          "makePhoneCall",
          "scanCode",
          "setClipboardData",
          "getClipboardData",
          "openDocument",
          "chooseImage",
          "previewImage",
          "getImageInfo",
          "saveImageToPhotosAlbum",
          "compressImage",
          "chooseVideo",
          "saveVideoToPhotosAlbum",
          "getVideoInfo",
          "compressVideo",
          "chooseMedia",
          "getRecorderManager",
          "getBackgroundAudioManager",
          "createCameraContext",
          "createLivePlayerContext",
          "createLivePusherContext",
          "createMapContext",
          "createVideoContext",
          "createCanvasContext",
          "canvasToTempFilePath",
          "canvasPutImageData",
          "canvasGetImageData",
          "drawCanvas"
        ];
        if (gameAPIs.includes(apiName)) {
          return typeof common_vendor.wx$1[apiName] === "function";
        }
        return typeof common_vendor.wx$1[apiName] !== "undefined";
      };
      console.log("已添加 wx.canIUse 方法");
    }
    if (!common_vendor.wx$1.getAppBaseInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getAppBaseInfo = function() {
        const info = common_vendor.wx$1.getSystemInfoSync();
        return {
          appId: info.appId || "",
          enableDebug: info.enableDebug || false,
          host: info.host || { appId: info.appId || "" },
          language: info.language || "zh_CN",
          version: info.version || "1.0.0",
          theme: info.theme || "light"
        };
      };
      console.log("已添加 wx.getAppBaseInfo");
    }
    if (!common_vendor.wx$1.getWindowInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getWindowInfo = function() {
        const info = common_vendor.wx$1.getSystemInfoSync();
        return {
          pixelRatio: info.pixelRatio || 1,
          screenWidth: info.screenWidth || 375,
          screenHeight: info.screenHeight || 667,
          windowWidth: info.windowWidth || 375,
          windowHeight: info.windowHeight || 667,
          statusBarHeight: info.statusBarHeight || 20,
          safeArea: info.safeArea || {
            left: 0,
            right: info.windowWidth || 375,
            top: info.statusBarHeight || 20,
            bottom: info.windowHeight || 667,
            width: info.windowWidth || 375,
            height: (info.windowHeight || 667) - (info.statusBarHeight || 20)
          }
        };
      };
      console.log("已添加 wx.getWindowInfo");
    }
    if (!common_vendor.wx$1.getDeviceInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getDeviceInfo = function() {
        const info = common_vendor.wx$1.getSystemInfoSync();
        return {
          abi: info.abi || "arm64",
          benchmarkLevel: info.benchmarkLevel || 1,
          brand: info.brand || "iPhone",
          model: info.model || "iPhone",
          platform: info.platform || "ios",
          system: info.system || "iOS 14.0"
        };
      };
      console.log("已添加 wx.getDeviceInfo");
    }
    if (!common_vendor.wx$1.getSystemSetting && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getSystemSetting = function() {
        return {
          bluetoothEnabled: true,
          locationEnabled: true,
          wifiEnabled: true,
          deviceOrientation: "portrait"
        };
      };
      console.log("已添加 wx.getSystemSetting");
    }
    if (!common_vendor.wx$1.getAppAuthorizeSetting) {
      common_vendor.wx$1.getAppAuthorizeSetting = function() {
        return {
          albumAuthorized: "authorized",
          bluetoothAuthorized: "authorized",
          cameraAuthorized: "authorized",
          locationAuthorized: "authorized",
          locationReducedAccuracy: false,
          microphoneAuthorized: "authorized",
          notificationAuthorized: "authorized"
        };
      };
      console.log("已添加 wx.getAppAuthorizeSetting");
    }
    if (typeof Page === "undefined") {
      globalObj.Page = function(options) {
        return options;
      };
      console.log("已添加 Page 函数");
    }
    if (typeof Component === "undefined") {
      globalObj.Component = function(options) {
        return options;
      };
      console.log("已添加 Component 函数");
    }
    if (typeof App === "undefined") {
      globalObj.App = function(options) {
        return options;
      };
      console.log("已添加 App 函数");
    }
    if (typeof getApp === "undefined") {
      globalObj.getApp = function(options) {
        return { $vm: null, globalData: {} };
      };
      console.log("已添加 getApp 函数");
    }
    if (typeof getCurrentPages === "undefined") {
      globalObj.getCurrentPages = function() {
        return [];
      };
      console.log("已添加 getCurrentPages 函数");
    }
    if (!common_vendor.wx$1.createCanvas && typeof document !== "undefined") {
      common_vendor.wx$1.createCanvas = function() {
        return document.createElement("canvas");
      };
      console.log("已添加 wx.createCanvas");
    }
    if (!common_vendor.wx$1.createImage) {
      common_vendor.wx$1.createImage = function() {
        const img = new Image();
        return img;
      };
      console.log("已添加 wx.createImage");
    }
    if (!common_vendor.wx$1.getFileSystemManager) {
      common_vendor.wx$1.getFileSystemManager = function() {
        return {
          readFile: function(options) {
            if (options.fail)
              options.fail({ errMsg: "readFile:fail not supported in mini-game" });
          },
          writeFile: function(options) {
            if (options.fail)
              options.fail({ errMsg: "writeFile:fail not supported in mini-game" });
          },
          access: function(options) {
            if (options.fail)
              options.fail({ errMsg: "access:fail not supported in mini-game" });
          },
          mkdir: function(options) {
            if (options.fail)
              options.fail({ errMsg: "mkdir:fail not supported in mini-game" });
          },
          rmdir: function(options) {
            if (options.fail)
              options.fail({ errMsg: "rmdir:fail not supported in mini-game" });
          },
          unlink: function(options) {
            if (options.fail)
              options.fail({ errMsg: "unlink:fail not supported in mini-game" });
          }
        };
      };
      console.log("已添加 wx.getFileSystemManager");
    }
    if (!common_vendor.wx$1.getMenuButtonBoundingClientRect) {
      common_vendor.wx$1.getMenuButtonBoundingClientRect = function() {
        return {
          width: 87,
          height: 32,
          top: 24,
          right: 365,
          bottom: 56,
          left: 278
        };
      };
      console.log("已添加 wx.getMenuButtonBoundingClientRect");
    }
    if (!common_vendor.wx$1.onMemoryWarning) {
      common_vendor.wx$1.onMemoryWarning = function(callback) {
        console.log("已注册内存警告监听");
      };
      console.log("已添加 wx.onMemoryWarning");
    }
    if (!common_vendor.wx$1.offMemoryWarning) {
      common_vendor.wx$1.offMemoryWarning = function(callback) {
        console.log("已取消内存警告监听");
      };
      console.log("已添加 wx.offMemoryWarning");
    }
    console.log("小游戏兼容性补丁应用完成");
  } else {
    console.log("未检测到微信环境");
  }
})();
