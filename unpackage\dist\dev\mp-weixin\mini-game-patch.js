"use strict";
const common_vendor = require("./common/vendor.js");
(function() {
  console.log("开始应用小游戏兼容性补丁");
  const globalObj = function() {
    if (typeof globalThis !== "undefined")
      return globalThis;
    if (typeof window !== "undefined")
      return window;
    if (typeof global !== "undefined")
      return global;
    if (typeof self !== "undefined")
      return self;
    return this;
  }();
  if (typeof global === "undefined") {
    globalObj.global = globalObj;
    console.log("已添加 global 对象");
  }
  if (typeof common_vendor.wx$1 !== "undefined") {
    console.log("检测到微信小游戏环境");
    if (!common_vendor.wx$1.canIUse) {
      common_vendor.wx$1.canIUse = function(apiName) {
        const gameAPIs = [
          "getSystemInfoSync",
          "getSystemInfo",
          "getAppBaseInfo",
          "getWindowInfo",
          "getDeviceInfo",
          "getSystemSetting",
          "getAppAuthorizeSetting",
          "request",
          "connectSocket",
          "onSocketOpen",
          "onSocketClose",
          "onSocketMessage",
          "onSocketError",
          "sendSocketMessage",
          "closeSocket",
          "showToast",
          "showModal",
          "showLoading",
          "hideLoading",
          "setStorage",
          "getStorage",
          "removeStorage",
          "clearStorage",
          "setStorageSync",
          "getStorageSync",
          "removeStorageSync",
          "clearStorageSync",
          "onShow",
          "onHide",
          "offShow",
          "offHide",
          "exitMiniProgram",
          "navigateToMiniProgram",
          "getUpdateManager"
        ];
        if (gameAPIs.includes(apiName)) {
          return typeof common_vendor.wx$1[apiName] === "function";
        }
        return typeof common_vendor.wx$1[apiName] !== "undefined";
      };
      console.log("已添加 wx.canIUse 方法");
    }
    if (!common_vendor.wx$1.getAppBaseInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getAppBaseInfo = common_vendor.wx$1.getSystemInfoSync;
      console.log("已添加 wx.getAppBaseInfo");
    }
    if (!common_vendor.wx$1.getWindowInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getWindowInfo = common_vendor.wx$1.getSystemInfoSync;
      console.log("已添加 wx.getWindowInfo");
    }
    if (!common_vendor.wx$1.getDeviceInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getDeviceInfo = common_vendor.wx$1.getSystemInfoSync;
      console.log("已添加 wx.getDeviceInfo");
    }
    if (!common_vendor.wx$1.getSystemSetting && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getSystemSetting = common_vendor.wx$1.getSystemInfoSync;
      console.log("已添加 wx.getSystemSetting");
    }
    if (!common_vendor.wx$1.getAppAuthorizeSetting) {
      common_vendor.wx$1.getAppAuthorizeSetting = function() {
        return {
          albumAuthorized: "authorized",
          bluetoothAuthorized: "authorized",
          cameraAuthorized: "authorized",
          locationAuthorized: "authorized",
          locationReducedAccuracy: false,
          microphoneAuthorized: "authorized",
          notificationAuthorized: "authorized"
        };
      };
      console.log("已添加 wx.getAppAuthorizeSetting");
    }
    if (typeof Page === "undefined") {
      globalObj.Page = function(options) {
        return options;
      };
      console.log("已添加 Page 函数");
    }
    if (typeof Component === "undefined") {
      globalObj.Component = function(options) {
        return options;
      };
      console.log("已添加 Component 函数");
    }
    if (typeof App === "undefined") {
      globalObj.App = function(options) {
        return options;
      };
      console.log("已添加 App 函数");
    }
    if (typeof getApp === "undefined") {
      globalObj.getApp = function(options) {
        return { $vm: null, globalData: {} };
      };
      console.log("已添加 getApp 函数");
    }
    if (typeof getCurrentPages === "undefined") {
      globalObj.getCurrentPages = function() {
        return [];
      };
      console.log("已添加 getCurrentPages 函数");
    }
    console.log("小游戏兼容性补丁应用完成");
  } else {
    console.log("未检测到微信环境");
  }
})();
