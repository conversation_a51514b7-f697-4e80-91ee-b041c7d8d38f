/**
 * 网络管理器 - 处理所有网络通信
 */

class NetworkManager {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 3000;
    
    // 服务器配置
    this.serverConfig = {
      url: 'ws://localhost:3000', // 根据实际服务器地址修改
      heartbeatInterval: 30000,
      timeout: 10000
    };
    
    this.heartbeatTimer = null;
    this.messageQueue = [];
    
    this.init();
  }
  
  // 初始化网络连接
  init() {
    console.log('初始化网络管理器...');
    this.connect();
  }
  
  // 连接WebSocket
  connect() {
    try {
      console.log('连接服务器:', this.serverConfig.url);
      
      this.socket = wx.connectSocket({
        url: this.serverConfig.url,
        success: () => {
          console.log('WebSocket连接请求发送成功');
        },
        fail: (error) => {
          console.error('WebSocket连接失败:', error);
          this.handleConnectionError();
        }
      });
      
      // 连接打开
      this.socket.onOpen(() => {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.processMessageQueue();
      });
      
      // 接收消息
      this.socket.onMessage((res) => {
        this.handleMessage(res.data);
      });
      
      // 连接关闭
      this.socket.onClose((res) => {
        console.log('WebSocket连接已关闭:', res);
        this.isConnected = false;
        this.stopHeartbeat();
        this.handleDisconnection();
      });
      
      // 连接错误
      this.socket.onError((error) => {
        console.error('WebSocket连接错误:', error);
        this.isConnected = false;
        this.handleConnectionError();
      });
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      this.handleConnectionError();
    }
  }
  
  // 处理接收到的消息
  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      console.log('收到服务器消息:', message);
      
      switch (message.type) {
        case 'login_success':
          this.handleLoginSuccess(message.data);
          break;
          
        case 'login_failed':
          this.handleLoginFailed(message.message);
          break;
          
        case 'register_success':
          this.handleRegisterSuccess(message.data);
          break;
          
        case 'register_failed':
          this.handleRegisterFailed(message.message);
          break;
          
        case 'player_data':
          this.handlePlayerData(message.data);
          break;
          
        case 'adventure_result':
          this.handleAdventureResult(message.data);
          break;
          
        case 'shop_result':
          this.handleShopResult(message.data);
          break;
          
        case 'skill_result':
          this.handleSkillResult(message.data);
          break;
          
        case 'guild_result':
          this.handleGuildResult(message.data);
          break;
          
        case 'error':
          this.handleServerError(message.message);
          break;
          
        case 'heartbeat':
          // 心跳响应，不需要处理
          break;
          
        default:
          console.log('未处理的消息类型:', message.type);
      }
      
    } catch (error) {
      console.error('解析服务器消息失败:', error);
    }
  }
  
  // 发送消息
  send(data) {
    if (!this.isConnected) {
      console.log('连接未建立，消息加入队列:', data);
      this.messageQueue.push(data);
      return;
    }
    
    try {
      const message = JSON.stringify(data);
      this.socket.send({
        data: message,
        success: () => {
          console.log('消息发送成功:', data);
        },
        fail: (error) => {
          console.error('消息发送失败:', error);
          // 重新加入队列
          this.messageQueue.push(data);
        }
      });
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  }
  
  // 处理登录成功
  handleLoginSuccess(data) {
    console.log('登录成功:', data);
    this.gameEngine.setLoginStatus(true, data);
    this.gameEngine.showToast('登录成功');
  }
  
  // 处理登录失败
  handleLoginFailed(message) {
    console.log('登录失败:', message);
    this.gameEngine.showToast('登录失败: ' + message);
  }
  
  // 处理注册成功
  handleRegisterSuccess(data) {
    console.log('注册成功:', data);
    this.gameEngine.showToast('注册成功，请登录');
    this.gameEngine.switchScene('login');
  }
  
  // 处理注册失败
  handleRegisterFailed(message) {
    console.log('注册失败:', message);
    this.gameEngine.showToast('注册失败: ' + message);
  }
  
  // 处理玩家数据
  handlePlayerData(data) {
    console.log('更新玩家数据:', data);
    this.gameEngine.updatePlayerData(data);
  }
  
  // 处理冒险结果
  handleAdventureResult(data) {
    console.log('冒险结果:', data);
    this.gameEngine.showToast(data.message || '冒险完成');
    if (data.playerData) {
      this.gameEngine.updatePlayerData(data.playerData);
    }
  }
  
  // 处理商店结果
  handleShopResult(data) {
    console.log('商店操作结果:', data);
    this.gameEngine.showToast(data.message || '操作完成');
    if (data.playerData) {
      this.gameEngine.updatePlayerData(data.playerData);
    }
  }
  
  // 处理技能结果
  handleSkillResult(data) {
    console.log('技能操作结果:', data);
    this.gameEngine.showToast(data.message || '操作完成');
    if (data.playerData) {
      this.gameEngine.updatePlayerData(data.playerData);
    }
  }
  
  // 处理门派结果
  handleGuildResult(data) {
    console.log('门派操作结果:', data);
    this.gameEngine.showToast(data.message || '操作完成');
    if (data.playerData) {
      this.gameEngine.updatePlayerData(data.playerData);
    }
  }
  
  // 处理服务器错误
  handleServerError(message) {
    console.error('服务器错误:', message);
    this.gameEngine.showToast('服务器错误: ' + message);
  }
  
  // 处理连接错误
  handleConnectionError() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay);
    } else {
      console.error('重连失败，已达到最大重试次数');
      this.gameEngine.showToast('网络连接失败，请检查网络设置');
    }
  }
  
  // 处理断开连接
  handleDisconnection() {
    // 尝试重连
    this.handleConnectionError();
  }
  
  // 开始心跳
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'heartbeat' });
      }
    }, this.serverConfig.heartbeatInterval);
  }
  
  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
  
  // 处理消息队列
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }
  
  // 断开连接
  disconnect() {
    this.stopHeartbeat();
    if (this.socket) {
      this.socket.close();
    }
    this.isConnected = false;
  }
  
  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NetworkManager;
} else {
  window.NetworkManager = NetworkManager;
}
