/**
 * 简化版小游戏 - 确保编译通过
 */

console.log('=== 仗剑江湖行小游戏启动 ===');

// 检查微信小游戏环境
if (typeof wx === 'undefined') {
  console.error('未检测到微信小游戏环境');
  throw new Error('此游戏只能在微信小游戏环境中运行');
}

// 获取系统信息
const systemInfo = wx.getSystemInfoSync();
console.log('系统信息:', systemInfo);

// 创建Canvas
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 设置Canvas尺寸
canvas.width = systemInfo.screenWidth;
canvas.height = systemInfo.screenHeight;

console.log('Canvas创建成功:', canvas.width, 'x', canvas.height);

// 游戏状态
const gameState = {
  currentScene: 'login',
  isLoggedIn: false,
  playerData: null,
  formData: {
    username: '',
    password: ''
  }
};

// UI配置
const uiConfig = {
  colors: {
    primary: '#2c3e50',
    secondary: '#3498db',
    success: '#27ae60',
    white: '#ffffff',
    text: '#2c3e50',
    textSecondary: '#7f8c8d',
    background: '#ecf0f1'
  },
  fonts: {
    title: 'bold 32px Arial',
    large: 'bold 24px Arial',
    normal: '18px Arial',
    small: '14px Arial'
  }
};

// 绘制文本
function drawText(text, x, y, options = {}) {
  const {
    font = uiConfig.fonts.normal,
    color = uiConfig.colors.text,
    align = 'left',
    baseline = 'top'
  } = options;
  
  ctx.font = font;
  ctx.fillStyle = color;
  ctx.textAlign = align;
  ctx.textBaseline = baseline;
  ctx.fillText(text, x, y);
}

// 绘制按钮
function drawButton(text, x, y, width, height, options = {}) {
  const {
    backgroundColor = uiConfig.colors.primary,
    textColor = uiConfig.colors.white,
    font = uiConfig.fonts.normal
  } = options;
  
  // 绘制按钮背景
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(x, y, width, height);
  
  // 绘制按钮文本
  drawText(text, x + width / 2, y + height / 2, {
    font,
    color: textColor,
    align: 'center',
    baseline: 'middle'
  });
  
  return { x, y, width, height };
}

// 绘制输入框
function drawInputBox(x, y, width, height, value, placeholder) {
  // 绘制背景
  ctx.fillStyle = uiConfig.colors.white;
  ctx.fillRect(x, y, width, height);
  
  // 绘制边框
  ctx.strokeStyle = uiConfig.colors.textSecondary;
  ctx.lineWidth = 1;
  ctx.strokeRect(x, y, width, height);
  
  // 绘制文本
  const displayText = value || placeholder;
  const textColor = value ? uiConfig.colors.text : uiConfig.colors.textSecondary;
  
  drawText(displayText, x + 10, y + height / 2, {
    color: textColor,
    baseline: 'middle'
  });
  
  return { x, y, width, height };
}

// 检查点是否在矩形内
function isPointInRect(x, y, rect) {
  return x >= rect.x && x <= rect.x + rect.width &&
         y >= rect.y && y <= rect.y + rect.height;
}

// 显示输入对话框
function showInputDialog(title, placeholder, callback) {
  wx.showModal({
    title: title,
    editable: true,
    placeholderText: placeholder,
    success: (res) => {
      if (res.confirm && callback) {
        callback(res.content || '');
      }
    }
  });
}

// 显示提示
function showToast(message) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}

// 渲染登录页面
function renderLoginPage() {
  // 清空画布
  ctx.fillStyle = uiConfig.colors.background;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制标题
  drawText('仗剑江湖行', canvas.width / 2, 80, {
    font: uiConfig.fonts.title,
    color: uiConfig.colors.white,
    align: 'center'
  });
  
  drawText('微信小游戏版', canvas.width / 2, 130, {
    font: uiConfig.fonts.normal,
    color: uiConfig.colors.white,
    align: 'center'
  });
  
  // 绘制表单背景
  const formX = 30;
  const formY = 180;
  const formWidth = canvas.width - 60;
  
  ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
  ctx.fillRect(formX, formY, formWidth, 320);
  
  // 表单标题
  drawText('登录江湖', formX + formWidth / 2, formY + 30, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.text,
    align: 'center'
  });
  
  // 账号输入框
  drawText('账号:', formX + 20, formY + 80);
  gameState.usernameInput = drawInputBox(
    formX + 20, formY + 105, formWidth - 40, 40,
    gameState.formData.username, '请输入账号'
  );
  
  // 密码输入框
  drawText('密码:', formX + 20, formY + 160);
  gameState.passwordInput = drawInputBox(
    formX + 20, formY + 185, formWidth - 40, 40,
    gameState.formData.password ? '••••••••' : '', '请输入密码'
  );
  
  // 登录按钮
  gameState.loginButton = drawButton(
    '登录', formX + 20, formY + 245, formWidth - 40, 45,
    { backgroundColor: uiConfig.colors.primary, font: 'bold 20px Arial' }
  );
  
  // 注册链接
  drawText('还没有账号？点击注册', formX + formWidth / 2, formY + 310, {
    font: uiConfig.fonts.small,
    color: uiConfig.colors.secondary,
    align: 'center'
  });
  
  gameState.registerLink = {
    x: formX + 20,
    y: formY + 300,
    width: formWidth - 40,
    height: 20
  };
}

// 渲染主页面
function renderIndexPage() {
  // 清空画布
  ctx.fillStyle = uiConfig.colors.background;
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制头部
  ctx.fillStyle = uiConfig.colors.primary;
  ctx.fillRect(0, 0, canvas.width, 60);
  
  drawText('仗剑江湖行', canvas.width / 2, 30, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.white,
    align: 'center',
    baseline: 'middle'
  });
  
  // 绘制玩家信息
  const contentY = 100;
  
  drawText('角色信息', 30, contentY, {
    font: uiConfig.fonts.large,
    color: uiConfig.colors.text
  });
  
  if (gameState.playerData) {
    drawText(`角色名: ${gameState.playerData.characterName || '测试玩家'}`, 30, contentY + 35);
    drawText(`等级: ${gameState.playerData.level || 1}`, 30, contentY + 65);
    drawText(`银两: ${gameState.playerData.money || 1000}`, 30, contentY + 95);
  }
  
  // 绘制功能按钮
  gameState.adventureButton = drawButton(
    '闯江湖', 30, contentY + 150, canvas.width - 60, 50,
    { backgroundColor: uiConfig.colors.success, font: 'bold 20px Arial' }
  );
  
  // 退出按钮
  gameState.logoutButton = drawButton(
    '退出登录', 30, contentY + 220, canvas.width - 60, 40,
    { backgroundColor: uiConfig.colors.textSecondary }
  );
}

// 渲染当前页面
function render() {
  switch (gameState.currentScene) {
    case 'login':
      renderLoginPage();
      break;
    case 'index':
      renderIndexPage();
      break;
  }
}

// 处理登录
function handleLogin() {
  const { username, password } = gameState.formData;
  
  if (!username || !password) {
    showToast('请输入账号和密码');
    return;
  }
  
  console.log('执行登录:', username);
  
  // 模拟登录成功
  gameState.isLoggedIn = true;
  gameState.currentScene = 'index';
  gameState.playerData = {
    characterName: '测试玩家',
    level: 1,
    money: 1000
  };
  
  showToast('登录成功');
  render();
}

// 处理触摸事件
function handleTap(x, y) {
  console.log('点击事件:', x, y, '当前场景:', gameState.currentScene);
  
  if (gameState.currentScene === 'login') {
    // 登录页面处理
    if (isPointInRect(x, y, gameState.usernameInput)) {
      showInputDialog('输入账号', '请输入账号', (value) => {
        gameState.formData.username = value;
        render();
      });
    } else if (isPointInRect(x, y, gameState.passwordInput)) {
      showInputDialog('输入密码', '请输入密码', (value) => {
        gameState.formData.password = value;
        render();
      });
    } else if (isPointInRect(x, y, gameState.loginButton)) {
      handleLogin();
    } else if (isPointInRect(x, y, gameState.registerLink)) {
      showToast('注册功能开发中');
    }
  } else if (gameState.currentScene === 'index') {
    // 主页面处理
    if (isPointInRect(x, y, gameState.adventureButton)) {
      showToast('开始冒险...');
    } else if (isPointInRect(x, y, gameState.logoutButton)) {
      gameState.currentScene = 'login';
      gameState.isLoggedIn = false;
      gameState.playerData = null;
      render();
    }
  }
}

// 注册触摸事件
wx.onTouchEnd((e) => {
  if (!e.touches || e.touches.length === 0) return;
  
  const touch = e.touches[0];
  const x = touch.clientX;
  const y = touch.clientY;
  
  handleTap(x, y);
});

// 生命周期事件
wx.onShow(() => {
  console.log('游戏进入前台');
});

wx.onHide(() => {
  console.log('游戏进入后台');
});

// 初始化游戏
function initGame() {
  console.log('初始化游戏...');
  render();
  console.log('游戏初始化完成');
}

// 启动游戏
initGame();

console.log('=== 仗剑江湖行小游戏启动完成 ===');
