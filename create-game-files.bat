@echo off
echo ========================================
echo     Create Mini-Game Files with Patches
echo ========================================
echo.

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%BUILD_DIR%" (
    echo ERROR: Build directory not found
    echo Please compile the project in HBuilderX first
    pause
    exit /b 1
)

echo Creating game.json...
echo {> "%BUILD_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%BUILD_DIR%\game.json"
echo   "showStatusBar": false,>> "%BUILD_DIR%\game.json"
echo   "networkTimeout": {>> "%BUILD_DIR%\game.json"
echo     "request": 60000,>> "%BUILD_DIR%\game.json"
echo     "connectSocket": 60000,>> "%BUILD_DIR%\game.json"
echo     "uploadFile": 60000,>> "%BUILD_DIR%\game.json"
echo     "downloadFile": 60000>> "%BUILD_DIR%\game.json"
echo   },>> "%BUILD_DIR%\game.json"
echo   "subpackages": [],>> "%BUILD_DIR%\game.json"
echo   "plugins": {},>> "%BUILD_DIR%\game.json"
echo   "preloadRule": {},>> "%BUILD_DIR%\game.json"
echo   "resizable": false>> "%BUILD_DIR%\game.json"
echo }>> "%BUILD_DIR%\game.json"

echo Creating game.js with built-in patches...
echo // Mini-Game Entry with Built-in Compatibility Patches> "%BUILD_DIR%\game.js"
echo "use strict";>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo // CRITICAL: Apply patches BEFORE any other code>> "%BUILD_DIR%\game.js"
echo (function() {>> "%BUILD_DIR%\game.js"
echo   console.log('Applying critical mini-game patches');>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo   // Get global object>> "%BUILD_DIR%\game.js"
echo   const globalObj = (function() {>> "%BUILD_DIR%\game.js"
echo     if (typeof globalThis !== 'undefined') return globalThis;>> "%BUILD_DIR%\game.js"
echo     if (typeof window !== 'undefined') return window;>> "%BUILD_DIR%\game.js"
echo     if (typeof global !== 'undefined') return global;>> "%BUILD_DIR%\game.js"
echo     if (typeof self !== 'undefined') return self;>> "%BUILD_DIR%\game.js"
echo     return this;>> "%BUILD_DIR%\game.js"
echo   })();>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo   // Ensure global object exists>> "%BUILD_DIR%\game.js"
echo   if (typeof global === 'undefined') {>> "%BUILD_DIR%\game.js"
echo     globalObj.global = globalObj;>> "%BUILD_DIR%\game.js"
echo   }>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo   // Mini-game environment patches>> "%BUILD_DIR%\game.js"
echo   if (typeof wx !== 'undefined') {>> "%BUILD_DIR%\game.js"
echo     // wx.canIUse patch - HIGHEST PRIORITY>> "%BUILD_DIR%\game.js"
echo     if (!wx.canIUse) {>> "%BUILD_DIR%\game.js"
echo       wx.canIUse = function(apiName) {>> "%BUILD_DIR%\game.js"
echo         const gameAPIs = [>> "%BUILD_DIR%\game.js"
echo           'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo',>> "%BUILD_DIR%\game.js"
echo           'getWindowInfo', 'getDeviceInfo', 'getSystemSetting',>> "%BUILD_DIR%\game.js"
echo           'getAppAuthorizeSetting', 'request', 'connectSocket'>> "%BUILD_DIR%\game.js"
echo         ];>> "%BUILD_DIR%\game.js"
echo         if (gameAPIs.includes(apiName)) {>> "%BUILD_DIR%\game.js"
echo           return typeof wx[apiName] === 'function';>> "%BUILD_DIR%\game.js"
echo         }>> "%BUILD_DIR%\game.js"
echo         return typeof wx[apiName] !== 'undefined';>> "%BUILD_DIR%\game.js"
echo       };>> "%BUILD_DIR%\game.js"
echo       console.log('wx.canIUse patch applied');>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo     // Basic API compatibility>> "%BUILD_DIR%\game.js"
echo     if (!wx.getAppBaseInfo ^&^& wx.getSystemInfoSync) {>> "%BUILD_DIR%\game.js"
echo       wx.getAppBaseInfo = wx.getSystemInfoSync;>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo     if (!wx.getWindowInfo ^&^& wx.getSystemInfoSync) {>> "%BUILD_DIR%\game.js"
echo       wx.getWindowInfo = wx.getSystemInfoSync;>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo     if (!wx.getDeviceInfo ^&^& wx.getSystemInfoSync) {>> "%BUILD_DIR%\game.js"
echo       wx.getDeviceInfo = wx.getSystemInfoSync;>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo     // Global functions>> "%BUILD_DIR%\game.js"
echo     if (typeof Page === 'undefined') {>> "%BUILD_DIR%\game.js"
echo       globalObj.Page = function(options) { return options; };>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo     if (typeof Component === 'undefined') {>> "%BUILD_DIR%\game.js"
echo       globalObj.Component = function(options) { return options; };>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo     if (typeof App === 'undefined') {>> "%BUILD_DIR%\game.js"
echo       globalObj.App = function(options) { return options; };>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo     if (typeof getApp === 'undefined') {>> "%BUILD_DIR%\game.js"
echo       globalObj.getApp = function() { return {$vm:null,globalData:{}}; };>> "%BUILD_DIR%\game.js"
echo     }>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"
echo     console.log('All mini-game patches applied successfully');>> "%BUILD_DIR%\game.js"
echo   }>> "%BUILD_DIR%\game.js"
echo })();>> "%BUILD_DIR%\game.js"
echo.>> "%BUILD_DIR%\game.js"

REM Add the original app.js content if it exists
if exist "%BUILD_DIR%\app.js" (
    echo // Original app.js content follows:>> "%BUILD_DIR%\game.js"
    type "%BUILD_DIR%\app.js" >> "%BUILD_DIR%\game.js"
    echo OK: game.js created with app.js content
) else (
    echo // Fallback mini-game entry>> "%BUILD_DIR%\game.js"
    echo Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });>> "%BUILD_DIR%\game.js"
    echo console.log('Mini-game started with fallback entry');>> "%BUILD_DIR%\game.js"
    echo OK: game.js created with fallback content
)

echo.
echo ========================================
echo Mini-Game Files Created!
echo ========================================
echo.
echo Files created:
echo - game.json ^(mini-game configuration^)
echo - game.js ^(with built-in compatibility patches^)
echo.
echo Now you can import in WeChat DevTools:
echo 1. Select "Mini Game" project type
echo 2. Import directory: %cd%\%BUILD_DIR%
echo 3. Enter your Mini Game AppID
echo.
pause
