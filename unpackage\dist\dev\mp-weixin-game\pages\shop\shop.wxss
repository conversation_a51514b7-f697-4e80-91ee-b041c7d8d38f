
.container.data-v-2a6aaf81 {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}
.header.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.money-info.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
}
.money-label.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}
.money-value.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.shop-name.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #666;
}
.shop-tabs.data-v-2a6aaf81 {
	display: flex;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.tab-item.data-v-2a6aaf81 {
	flex: 1;
	text-align: center;
	padding: 15rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}
.tab-item.active.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #667eea, #764ba2);
}
.tab-text.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #333;
}
.tab-item.active .tab-text.data-v-2a6aaf81 {
	color: white;
}
.market-section.data-v-2a6aaf81 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	flex: 1;
	margin-bottom: 20rpx;
	display: flex;
	flex-direction: column;
	min-height: 0;
}
.market-info.data-v-2a6aaf81 {
	margin-bottom: 20rpx;
}
.market-title.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.market-desc.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #666;
	display: block;
}
.market-actions.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.market-btn.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
}
.btn-text.data-v-2a6aaf81 {
	font-size: 28rpx;
	font-weight: bold;
	color: white;
}
.market-list.data-v-2a6aaf81 {
	height: 300rpx;
}
.market-empty.data-v-2a6aaf81 {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}
.goods-list.data-v-2a6aaf81 {
	height: 400rpx;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.goods-item.simple-row.data-v-2a6aaf81 {
	display: grid;
	grid-template-columns: 180rpx 70rpx 30rpx 110rpx minmax(120rpx, 1fr);
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1px solid #eee;
	background: rgba(255,255,255,0.92);
	border-radius: 8rpx;
	margin-bottom: 8rpx;
}
.item-name.data-v-2a6aaf81 {
	font-size: 30rpx;
	font-weight: bold;
	margin-right: 0;
	max-width: 180rpx;
	flex-shrink: 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.item-quality.data-v-2a6aaf81 {
	font-size: 26rpx;
	margin-right: 0;
	width: 70rpx;
	flex-shrink: 0;
	text-align: center;
}
.item-type.data-v-2a6aaf81 {
	font-size: 26rpx;
	color: #666;
	margin-right: 0;
	width: 30rpx;
	flex-shrink: 0;
	text-align: center;
}
.item-sep.data-v-2a6aaf81 {
	color: #bbb;
	margin: 0 10rpx;
	font-size: 26rpx;
}
.item-price.data-v-2a6aaf81, .main-price.data-v-2a6aaf81 {
	display: inline-block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	vertical-align: middle;
	font-size: 26rpx;
}
.buy-btn.data-v-2a6aaf81 {
	font-size: 26rpx;
	padding: 8rpx 24rpx;
	background: linear-gradient(90deg, #5a8dee 60%, #42e9f6 100%);
	color: #fff;
	border-radius: 8rpx;
	justify-self: end;
	min-width: 120rpx;
	box-shadow: 0 2rpx 8rpx rgba(90,141,238,0.12);
	transition: background 0.2s;
}
.buy-btn.data-v-2a6aaf81:active {
	background: #3a6fd6;
}
.empty-goods.data-v-2a6aaf81 {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}
.my-items-section.data-v-2a6aaf81 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	flex: 1;
}
.section-header.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.section-title.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.section-subtitle.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #666;
}
.my-items-list.data-v-2a6aaf81 {
	height: 300rpx;
}
.my-item.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 10rpx;
}
.my-item.data-v-2a6aaf81:last-child {
	border-bottom: none;
	margin-bottom: 0;
}
.item-quantity.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}
.sell-price.data-v-2a6aaf81 {
	text-align: right;
}
.empty-items.data-v-2a6aaf81 {
	text-align: center;
	padding: 60rpx 20rpx;
	color: #8e8e93;
	font-size: 30rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	margin: 20rpx 0;
	border: 2rpx dashed #dee2e6;
}
.modal-overlay.data-v-2a6aaf81 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	-webkit-backdrop-filter: blur(4rpx);
	        backdrop-filter: blur(4rpx);
}
.modal-content.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 24rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.modal-header.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 30rpx 30rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	position: relative;
}
.modal-title.data-v-2a6aaf81 {
	font-size: 38rpx;
	font-weight: bold;
	color: white;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.modal-close.data-v-2a6aaf81 {
	font-size: 44rpx;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1;
	padding: 10rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}
.modal-close.data-v-2a6aaf81:hover {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}
.modal-body.data-v-2a6aaf81 {
	padding: 40rpx 30rpx;
	max-height: 500rpx;
	overflow-y: auto;
	background: rgba(255, 255, 255, 0.95);
}
.detail-name.data-v-2a6aaf81 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.detail-quality.data-v-2a6aaf81,
.detail-type.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.detail-desc.data-v-2a6aaf81 {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-bottom: 20rpx;
	line-height: 1.5;
}
.detail-stats.data-v-2a6aaf81 {
	margin-bottom: 20rpx;
}
.stats-title.data-v-2a6aaf81 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.detail-stats text.data-v-2a6aaf81 {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}
.detail-price.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
}
.price-title.data-v-2a6aaf81 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-right: 10rpx;
}
.price-value.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #e74c3c;
}
.modal-footer.data-v-2a6aaf81 {
	display: flex;
	padding: 30rpx;
	background: rgba(248, 249, 250, 0.8);
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
	gap: 20rpx;
}
.modal-btn.data-v-2a6aaf81 {
	flex: 1;
	padding: 24rpx 20rpx;
	border: none;
	border-radius: 20rpx;
	font-size: 30rpx;
	font-weight: bold;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.cancel-btn.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	color: #6c757d;
	border: 1rpx solid rgba(108, 117, 125, 0.2);
}
.cancel-btn.data-v-2a6aaf81:hover {
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	transform: translateY(-2rpx);
}
.confirm-btn.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}
.confirm-btn.data-v-2a6aaf81:hover {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
}
.order-list.data-v-2a6aaf81 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	padding: 20rpx;
}
.order-card.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
	padding: 20rpx;
	gap: 20rpx;
	position: relative;
}
.item-img.data-v-2a6aaf81 {
	width: 100rpx;
	height: 100rpx;
	border-radius: 12rpx;
	background: #f5f5f5;
	object-fit: cover;
}
.order-info.data-v-2a6aaf81 {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.item-name.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.item-detail.data-v-2a6aaf81 {
	font-size: 26rpx;
	color: #666;
}
.price.data-v-2a6aaf81 {
	color: #e43d33;
	font-weight: bold;
}
.seller.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #999;
}
.buy-btn.data-v-2a6aaf81 {
	background: linear-gradient(90deg, #ffb347, #ffcc33);
	color: #fff;
	border: none;
	border-radius: 24rpx;
	padding: 0 32rpx;
	font-size: 28rpx;
	height: 60rpx;
	line-height: 60rpx;
}
.list-tabs.data-v-2a6aaf81 {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
	padding: 10rpx 0;
	border-bottom: 1rpx solid #eee;
}
.list-tab.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #666;
	padding: 10rpx 20rpx;
	border-bottom: 2rpx solid transparent;
}
.list-tab.active.data-v-2a6aaf81 {
	color: #333;
	border-bottom-color: #667eea;
	font-weight: bold;
}
.list-select-list.data-v-2a6aaf81 {
	max-height: 500rpx;
	min-height: 300rpx;
	overflow-y: auto;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 16rpx;
	padding: 10rpx;
	margin: 20rpx 0;
}
.list-select-item.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	padding: 20rpx 16rpx;
	margin: 8rpx 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 12rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.list-select-item.data-v-2a6aaf81:hover {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.2);
}
.list-select-item.data-v-2a6aaf81:last-child {
	margin-bottom: 0;
}
.item-name.data-v-2a6aaf81 {
	flex: 1;
}

/* 价格输入区域样式 */
.price-input-section.data-v-2a6aaf81 {
	padding: 20rpx 0;
}
.selected-item-info.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}
.selected-item-label.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #6c757d;
	margin-right: 12rpx;
}
.selected-item-name.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 12rpx;
}
.selected-item-quality.data-v-2a6aaf81 {
	font-size: 26rpx;
	font-weight: bold;
}
.price-input-wrapper.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	background: white;
	border-radius: 16rpx;
	padding: 20rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}
.price-input-wrapper.data-v-2a6aaf81:focus-within {
	border-color: #667eea;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.price-label.data-v-2a6aaf81 {
	font-size: 30rpx;
	color: #495057;
	margin-right: 16rpx;
	white-space: nowrap;
}
.price-input.data-v-2a6aaf81 {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	border: none;
	outline: none;
	background: transparent;
}
.item-name.data-v-2a6aaf81 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 10rpx;
}
.item-quality.data-v-2a6aaf81 {
	font-size: 26rpx;
	color: #666;
	flex-shrink: 0;
}
.item-quantity.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #999;
	flex-shrink: 0;
}
.item-level.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #999;
	flex-shrink: 0;
}
.order-list-scroll.data-v-2a6aaf81 {
	flex: 1;
	min-height: 200rpx;
	overflow-y: auto;
	margin-bottom: 10rpx;
}

/* 下架按钮样式 */
.unlist-btn.data-v-2a6aaf81 {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
	color: #fff;
	border: none;
	border-radius: 12rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	min-width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.unlist-btn.data-v-2a6aaf81:active {
	background: linear-gradient(135deg, #ee5a52 0%, #dd4b39 100%);
}

/* 订单时间样式 */
.order-time.data-v-2a6aaf81 {
	font-size: 22rpx;
	color: #666;
	margin-top: 4rpx;
}
.order-time.time-warning.data-v-2a6aaf81 {
	color: #ff6b6b;
	font-weight: 500;
}

/* 数量控制样式 */
.quantity-input-wrapper.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.quantity-label.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
	min-width: 140rpx;
}
.quantity-controls.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	flex: 1;
}
.quantity-btn.data-v-2a6aaf81 {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}
.quantity-btn.data-v-2a6aaf81:active {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
.quantity-input.data-v-2a6aaf81 {
	flex: 1;
	height: 60rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	text-align: center;
	font-size: 28rpx;
	margin: 0 10rpx;
	background: white;
}

/* 库存显示样式 */
.item-stock.data-v-2a6aaf81 {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}

/* 总价显示样式 */
.total-price-info.data-v-2a6aaf81 {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	padding: 15rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 8rpx;
	border-left: 4rpx solid #667eea;
}
.total-label.data-v-2a6aaf81 {
	font-size: 28rpx;
	color: #333;
	margin-right: 10rpx;
}
.total-value.data-v-2a6aaf81 {
	font-size: 32rpx;
	font-weight: bold;
	color: #667eea;
}
