<view wx:if="{{a}}" class="battle-popup-mask data-v-66bb1315"><view class="battle-popup-content data-v-66bb1315"><view class="battle-title data-v-66bb1315">战斗中</view><view class="battle-roles data-v-66bb1315"><view class="role player data-v-66bb1315"><image class="role-avatar data-v-66bb1315" src="{{b}}" mode="aspectFill"/><view class="role-name data-v-66bb1315">{{c}}</view><view class="role-hp-bar data-v-66bb1315"><text class="data-v-66bb1315">气血</text><progress class="data-v-66bb1315" percent="{{d}}" stroke-width="8" activeColor="#e74c3c"/><text class="data-v-66bb1315">{{e}}/{{f}}</text></view><view class="role-mp-bar data-v-66bb1315"><text class="data-v-66bb1315">内力</text><progress class="data-v-66bb1315" percent="{{g}}" stroke-width="8" activeColor="#3498db"/><text class="data-v-66bb1315">{{h}}/{{i}}</text></view></view><view class="role monster data-v-66bb1315"><image class="role-avatar data-v-66bb1315" src="{{j}}" mode="aspectFill"/><view class="role-name data-v-66bb1315">{{k}}</view><view class="role-hp-bar data-v-66bb1315"><text class="data-v-66bb1315">气血</text><progress class="data-v-66bb1315" percent="{{l}}" stroke-width="8" activeColor="#e67e22"/><text class="data-v-66bb1315">{{m}}/{{n}}</text></view></view></view><scroll-view class="battle-log data-v-66bb1315" scroll-y="true" scroll-with-animation="true" show-scrollbar="true" scroll-top="{{q}}" scroll-into-view="{{r}}" bindscroll="{{s}}"><view wx:if="{{o}}" class="battle-log-content data-v-66bb1315"><view wx:for="{{p}}" wx:for-item="round" wx:key="f" class="battle-round data-v-66bb1315"><view class="round-header data-v-66bb1315"><text class="round-number data-v-66bb1315">第{{round.a}}回合</text><text class="round-time data-v-66bb1315">{{round.b}}</text></view><view class="round-desc data-v-66bb1315"><rich-text class="data-v-66bb1315" nodes="{{round.c}}"/></view><view wx:if="{{round.d}}" class="effect-desc data-v-66bb1315"><rich-text class="data-v-66bb1315" nodes="{{round.e}}"/></view></view><view id="battle-log-bottom" class="battle-log-bottom data-v-66bb1315"></view></view><view wx:else class="battle-log-placeholder data-v-66bb1315"><text class="data-v-66bb1315">等待战斗开始...</text></view></scroll-view><view class="battle-popup-buttons data-v-66bb1315"><block wx:if="{{t}}"><button class="main-btn data-v-66bb1315" catchtap="{{v}}">攻击</button><button class="sub-btn data-v-66bb1315" catchtap="{{w}}">逃跑</button></block><block wx:elif="{{x}}"><button class="sub-btn data-v-66bb1315" catchtap="{{y}}">逃离</button></block><block wx:elif="{{z}}"><button class="main-btn data-v-66bb1315" catchtap="{{A}}">逃跑</button></block><block wx:elif="{{B}}"><button class="main-btn data-v-66bb1315" catchtap="{{C}}">关闭</button></block></view></view></view>