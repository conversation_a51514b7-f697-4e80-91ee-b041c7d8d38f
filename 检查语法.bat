@echo off
chcp 65001 >nul
echo ========================================
echo        JavaScript Syntax Check
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo ERROR: Build directory not found!
    pause
    exit /b 1
)

echo Checking JavaScript syntax...
echo.

REM Check app.js syntax
if exist "%SOURCE_DIR%\app.js" (
    echo Checking app.js...
    node -c "%SOURCE_DIR%\app.js" 2>nul
    if %errorlevel% equ 0 (
        echo OK: app.js syntax is valid
    ) else (
        echo ERROR: app.js has syntax errors
        node -c "%SOURCE_DIR%\app.js"
    )
) else (
    echo ERROR: app.js not found
)

REM Check game.js syntax
if exist "%SOURCE_DIR%\game.js" (
    echo Checking game.js...
    node -c "%SOURCE_DIR%\game.js" 2>nul
    if %errorlevel% equ 0 (
        echo OK: game.js syntax is valid
    ) else (
        echo ERROR: game.js has syntax errors
        node -c "%SOURCE_DIR%\game.js"
    )
) else (
    echo ERROR: game.js not found
)

REM Check if files have required content
echo.
echo Checking file content...

findstr "Mini-game compatibility patch" "%SOURCE_DIR%\app.js" >nul
if %errorlevel% equ 0 (
    echo OK: app.js contains compatibility patch
) else (
    echo WARNING: app.js missing compatibility patch
)

findstr "Mini-game compatibility patch" "%SOURCE_DIR%\game.js" >nul
if %errorlevel% equ 0 (
    echo OK: game.js contains compatibility patch
) else (
    echo WARNING: game.js missing compatibility patch
)

echo.
echo ========================================
echo Syntax Check Complete
echo ========================================
echo.
echo If all checks pass, the files should work in WeChat DevTools
echo.
pause
