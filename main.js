// 导入小游戏兼容性补丁 - 必须在所有其他模块之前
import './mini-game-patch.js'

// 小游戏环境检测
if (typeof wx !== 'undefined') {
  console.log('当前运行环境：微信小游戏');

  // 设置小游戏环境标识
  if (typeof global !== 'undefined') {
    global.__MINI_GAME__ = true;
  }

  // 小游戏生命周期
  wx.onShow && wx.onShow(() => {
    console.log('小游戏显示');
  });

  wx.onHide && wx.onHide(() => {
    console.log('小游戏隐藏');
  });

  // 内存警告处理
  wx.onMemoryWarning && wx.onMemoryWarning((res) => {
    console.warn('内存警告:', res.level);
    // 可以在这里添加内存清理逻辑
  });
} else {
  console.log('当前运行环境：非微信小游戏');
}

import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif