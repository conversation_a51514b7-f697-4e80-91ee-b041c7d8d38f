// 导入小游戏兼容性补丁 - 必须在所有其他模块之前
import './mini-game-patch.js'

// 小游戏环境检测和初始化
if (typeof wx !== 'undefined') {
  console.log('当前运行环境：微信小游戏');

  // 设置小游戏环境标识
  if (typeof global !== 'undefined') {
    global.__MINI_GAME__ = true;
  }

  // 导入小游戏适配器
  import('./miniGameAdapter.js').then((module) => {
    const MiniGameAdapter = module.default || module;

    // 创建游戏实例
    const gameAdapter = new MiniGameAdapter();

    // 设置全局游戏实例
    if (typeof global !== 'undefined') {
      global.__GAME_ADAPTER__ = gameAdapter;
    }

    console.log('小游戏适配器初始化成功');

    // 小游戏生命周期
    wx.onShow && wx.onShow(() => {
      console.log('小游戏显示');
      if (gameAdapter && typeof gameAdapter.onShow === 'function') {
        gameAdapter.onShow();
      }
    });

    wx.onHide && wx.onHide(() => {
      console.log('小游戏隐藏');
      if (gameAdapter && typeof gameAdapter.onHide === 'function') {
        gameAdapter.onHide();
      }
    });

    // 内存警告处理
    wx.onMemoryWarning && wx.onMemoryWarning((res) => {
      console.warn('内存警告:', res.level);
      if (gameAdapter && typeof gameAdapter.handleMemoryWarning === 'function') {
        gameAdapter.handleMemoryWarning(res.level);
      }
    });

  }).catch((error) => {
    console.error('小游戏适配器加载失败:', error);

    // 降级到简单模式
    initSimpleMode();
  });

} else {
  console.log('当前运行环境：非微信小游戏');

  // 非小游戏环境，使用原有的 Vue 应用
  initVueApp();
}

// 简单模式初始化（降级方案）
function initSimpleMode() {
  console.log('启动简单模式...');

  if (typeof wx !== 'undefined') {
    // 创建Canvas用于游戏渲染
    const canvas = wx.createCanvas();
    const ctx = canvas.getContext('2d');

    // 设置Canvas尺寸
    const systemInfo = wx.getSystemInfoSync();
    canvas.width = systemInfo.screenWidth;
    canvas.height = systemInfo.screenHeight;

    console.log('Canvas创建成功，尺寸:', canvas.width, 'x', canvas.height);

    // 简单的游戏界面
    function drawGameUI() {
      // 清空画布
      ctx.fillStyle = '#2c3e50';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 绘制标题
      ctx.fillStyle = '#ffffff';
      ctx.font = '32px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

      // 绘制提示信息
      ctx.font = '16px Arial';
      ctx.fillText('正在加载完整版本...', canvas.width / 2, 200);
      ctx.fillText('请稍候', canvas.width / 2, 230);

      // 绘制按钮
      ctx.fillStyle = '#3498db';
      ctx.fillRect(canvas.width / 2 - 100, 300, 200, 50);

      ctx.fillStyle = '#ffffff';
      ctx.fillText('重新加载', canvas.width / 2, 330);
    }

    // 触摸事件处理
    wx.onTouchStart((e) => {
      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;

      // 检查是否点击了重新加载按钮
      if (x >= canvas.width / 2 - 100 && x <= canvas.width / 2 + 100 &&
          y >= 300 && y <= 350) {
        // 尝试重新初始化游戏适配器
        location.reload();
      }
    });

    // 初始化游戏界面
    drawGameUI();
  }
}

// Vue 应用初始化（非小游戏环境）
function initVueApp() {
  import('./App').then((AppModule) => {
    const App = AppModule.default || AppModule;

    // #ifndef VUE3
    import('./uni.promisify.adaptor').then(() => {
      import('vue').then((VueModule) => {
        const Vue = VueModule.default || VueModule;
        Vue.config.productionTip = false;
        App.mpType = 'app';
        const app = new Vue({
          ...App
        });
        app.$mount();
      });
    });
    // #endif

    // #ifdef VUE3
    import('vue').then((VueModule) => {
      const { createSSRApp } = VueModule;
      const app = createSSRApp(App);
      // 这里需要根据实际情况挂载
    });
    // #endif
  });
}