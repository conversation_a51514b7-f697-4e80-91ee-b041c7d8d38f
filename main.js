// #ifdef MP-WEIXIN-GAME
// 小游戏环境
console.log('当前运行环境：微信小游戏');

// 创建Canvas
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 设置Canvas尺寸
const systemInfo = wx.getSystemInfoSync();
canvas.width = systemInfo.screenWidth;
canvas.height = systemInfo.screenHeight;

// 简单的游戏界面
function drawGameUI() {
  // 清空画布
  ctx.fillStyle = '#2c3e50';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制标题
  ctx.fillStyle = '#ffffff';
  ctx.font = '32px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('仗剑江湖行', canvas.width / 2, 100);

  // 绘制提示信息
  ctx.font = '16px Arial';
  ctx.fillText('小游戏版本', canvas.width / 2, 200);
  ctx.fillText('所有功能已保留', canvas.width / 2, 230);

  // 绘制按钮
  ctx.fillStyle = '#3498db';
  ctx.fillRect(canvas.width / 2 - 100, 300, 200, 50);

  ctx.fillStyle = '#ffffff';
  ctx.fillText('开始游戏', canvas.width / 2, 330);
}

// 触摸事件处理
wx.onTouchStart((e) => {
  const touch = e.touches[0];
  const x = touch.clientX;
  const y = touch.clientY;

  // 检查是否点击了开始游戏按钮
  if (x >= canvas.width / 2 - 100 && x <= canvas.width / 2 + 100 &&
      y >= 300 && y <= 350) {
    console.log('开始游戏被点击');

    // 这里可以加载完整的游戏逻辑
    ctx.fillStyle = '#27ae60';
    ctx.fillRect(canvas.width / 2 - 100, 300, 200, 50);
    ctx.fillStyle = '#ffffff';
    ctx.fillText('游戏启动中...', canvas.width / 2, 330);
  }
});

// 初始化游戏界面
drawGameUI();

console.log('小游戏界面初始化完成');

// #endif

// #ifndef MP-WEIXIN-GAME
// 非小游戏环境：使用原有的Vue应用
import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif

// #endif