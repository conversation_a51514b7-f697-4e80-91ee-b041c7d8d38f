// 导入小游戏兼容性补丁
import './mini-game-patch.js'

// #ifdef MP-WEIXIN-GAME
// 小游戏环境：使用页面适配器
console.log('当前运行环境：微信小游戏');

// 设置小游戏环境标识
if (typeof global !== 'undefined') {
  global.__MINI_GAME__ = true;
}

// 导入页面适配器
import('./gamePageAdapter.js').then((module) => {
  const GamePageAdapter = module.default || module;

  // 创建页面适配器实例
  const pageAdapter = new GamePageAdapter();

  // 设置全局实例
  if (typeof global !== 'undefined') {
    global.__PAGE_ADAPTER__ = pageAdapter;
  }

  console.log('页面适配器初始化成功');

}).catch((error) => {
  console.error('页面适配器加载失败:', error);
});

// #endif

// #ifndef MP-WEIXIN-GAME
// 非小游戏环境：使用原有的Vue应用
import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif

// #endif