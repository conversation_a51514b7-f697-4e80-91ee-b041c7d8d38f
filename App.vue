<script>
	export default {
		onLaunch: function() {
			console.log('App Launch - 小游戏模式')

			// 小游戏环境检测
			// #ifdef MP-WEIXIN
			if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
				try {
					const systemInfo = wx.getSystemInfoSync()
					console.log('小游戏环境信息:', systemInfo)

					// 设置小游戏环境标识
					if (typeof global !== 'undefined') {
						global.__MINI_GAME__ = true;
						global.__SYSTEM_INFO__ = systemInfo;
					}

					console.log('运行在小游戏环境中')
				} catch (e) {
					console.error('获取系统信息失败:', e)
				}
			}
			// #endif

			// 在小游戏环境中，不需要初始化传统的游戏数据
			// 游戏数据将由 miniGameAdapter 管理
			console.log('小游戏模式：跳过传统游戏数据初始化')
		},
		onShow: function() {
			console.log('App Show - 小游戏模式')

			// 小游戏显示时的逻辑
			// #ifdef MP-WEIXIN
			try {
				// 通知游戏适配器应用进入前台
				if (typeof global !== 'undefined' && global.__GAME_ADAPTER__) {
					const gameAdapter = global.__GAME_ADAPTER__;
					if (typeof gameAdapter.onShow === 'function') {
						gameAdapter.onShow();
					}
				}
				console.log('小游戏前台逻辑处理完成');
			} catch (e) {
				console.error('小游戏前台逻辑处理失败:', e);
			}
			// #endif
		},
		onHide: function() {
			console.log('App Hide - 小游戏模式')

			// 小游戏隐藏时的逻辑
			// #ifdef MP-WEIXIN
			try {
				// 通知游戏适配器应用进入后台
				if (typeof global !== 'undefined' && global.__GAME_ADAPTER__) {
					const gameAdapter = global.__GAME_ADAPTER__;
					if (typeof gameAdapter.onHide === 'function') {
						gameAdapter.onHide();
					}
				}
				console.log('小游戏后台逻辑处理完成');
			} catch (e) {
				console.error('小游戏后台逻辑处理失败:', e);
			}
			// #endif
			try {
				const wsManager = require('./utils/websocket.js').default || require('./utils/websocket.js');
				if (wsManager && typeof wsManager.disconnect === 'function') {
					wsManager.disconnect();
					console.log('全局 WebSocket 已断开');
				}
			} catch (e) {
				console.error('断开 WebSocket 失败:', e);
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	/* 自定义底部导航栏样式 */
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__icon {
		width: 80rpx !important;
		height: 80rpx !important;
		border-radius: 50% !important;
		background: linear-gradient(135deg, #667eea, #764ba2) !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		margin-bottom: 8rpx !important;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3) !important;
	}
	
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__label {
		color: #667eea !important;
		font-weight: bold !important;
	}

	/* 确保其他按钮正常显示 */
	.uni-tabbar__item .uni-tabbar__icon {
		width: 48rpx;
		height: 48rpx;
	}
</style>
