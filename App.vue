<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')

			// 小游戏环境检测
			// #ifdef MP-WEIXIN-GAME
			console.log('运行在小游戏环境中')
			// #endif

			// #ifndef MP-WEIXIN-GAME
			console.log('运行在小程序环境中')
			// #endif
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
			// 只在小程序彻底隐藏时断开 WebSocket
			try {
				const wsManager = require('./utils/websocket.js').default || require('./utils/websocket.js');
				if (wsManager && typeof wsManager.disconnect === 'function') {
					wsManager.disconnect();
					console.log('全局 WebSocket 已断开');
				}
			} catch (e) {
				console.error('断开 WebSocket 失败:', e);
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	/* 自定义底部导航栏样式 */
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__icon {
		width: 80rpx !important;
		height: 80rpx !important;
		border-radius: 50% !important;
		background: linear-gradient(135deg, #667eea, #764ba2) !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		margin-bottom: 8rpx !important;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3) !important;
	}
	
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__label {
		color: #667eea !important;
		font-weight: bold !important;
	}

	/* 确保其他按钮正常显示 */
	.uni-tabbar__item .uni-tabbar__icon {
		width: 48rpx;
		height: 48rpx;
	}
</style>
