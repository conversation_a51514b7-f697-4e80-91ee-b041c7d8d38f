<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')

			// 小游戏环境检测
			// #ifdef MP-WEIXIN
			if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
				try {
					const systemInfo = wx.getSystemInfoSync()
					console.log('小游戏环境信息:', systemInfo)

					// 检查是否为小游戏环境
					if (systemInfo.environment === 'miniprogram') {
						console.log('运行在小游戏环境中')
					}
				} catch (e) {
					console.error('获取系统信息失败:', e)
				}
			}
			// #endif

			// 初始化游戏数据
			try {
				const gameData = require('./utils/gameData.js')
				console.log('游戏数据初始化完成')
			} catch (e) {
				console.error('游戏数据初始化失败:', e)
			}
		},
		onShow: function() {
			console.log('App Show')

			// 小游戏显示时的逻辑
			// #ifdef MP-WEIXIN
			try {
				// 重新连接 WebSocket（如果需要）
				const wsManager = require('./utils/websocket.js').default || require('./utils/websocket.js');
				if (wsManager && typeof wsManager.reconnect === 'function') {
					wsManager.reconnect();
					console.log('WebSocket 重新连接');
				}
			} catch (e) {
				console.error('WebSocket 重连失败:', e);
			}
			// #endif
		},
		onHide: function() {
			console.log('App Hide')
			// 只在小程序彻底隐藏时断开 WebSocket
			try {
				const wsManager = require('./utils/websocket.js').default || require('./utils/websocket.js');
				if (wsManager && typeof wsManager.disconnect === 'function') {
					wsManager.disconnect();
					console.log('全局 WebSocket 已断开');
				}
			} catch (e) {
				console.error('断开 WebSocket 失败:', e);
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	/* 自定义底部导航栏样式 */
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__icon {
		width: 80rpx !important;
		height: 80rpx !important;
		border-radius: 50% !important;
		background: linear-gradient(135deg, #667eea, #764ba2) !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		margin-bottom: 8rpx !important;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3) !important;
	}
	
	.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__label {
		color: #667eea !important;
		font-weight: bold !important;
	}

	/* 确保其他按钮正常显示 */
	.uni-tabbar__item .uni-tabbar__icon {
		width: 48rpx;
		height: 48rpx;
	}
</style>
