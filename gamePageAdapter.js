/**
 * 游戏页面适配器
 * 将现有的小程序页面功能适配到小游戏Canvas渲染
 * 保持所有业务逻辑不变，只改变渲染方式
 */

class GamePageAdapter {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.currentPage = 'login'; // 当前页面
    this.pageInstances = new Map(); // 页面实例缓存
    this.systemInfo = {};
    this.touchHandlers = new Map();
    
    // UI配置
    this.uiConfig = {
      headerHeight: 88,
      tabBarHeight: 100,
      padding: 30,
      fontSize: {
        title: 32,
        large: 24,
        normal: 28,
        small: 24
      },
      colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        background: '#f5f5f5',
        white: '#ffffff',
        text: '#333333',
        textSecondary: '#666666',
        border: '#e0e0e0'
      }
    };
    
    this.init();
  }
  
  // 初始化
  async init() {
    console.log('游戏页面适配器初始化...');
    
    // 获取系统信息
    this.systemInfo = wx.getSystemInfoSync();
    
    // 创建Canvas
    this.canvas = wx.createCanvas();
    this.ctx = this.canvas.getContext('2d');
    
    // 设置Canvas尺寸
    this.canvas.width = this.systemInfo.screenWidth;
    this.canvas.height = this.systemInfo.screenHeight;
    
    console.log('Canvas创建成功:', this.canvas.width, 'x', this.canvas.height);
    
    // 注册触摸事件
    this.registerTouchEvents();
    
    // 加载页面实例
    await this.loadPageInstances();
    
    // 显示登录页面
    this.navigateTo('login');
  }
  
  // 加载页面实例
  async loadPageInstances() {
    try {
      // 动态导入所有页面
      const pages = [
        'login', 'index', 'character', 'skills', 
        'shop', 'guild', 'character/backpack', 'crafting'
      ];
      
      for (const pageName of pages) {
        try {
          // 这里模拟加载页面逻辑，实际应该导入对应的页面文件
          const pageInstance = await this.createPageInstance(pageName);
          this.pageInstances.set(pageName, pageInstance);
          console.log(`页面 ${pageName} 加载成功`);
        } catch (error) {
          console.warn(`页面 ${pageName} 加载失败:`, error);
        }
      }
    } catch (error) {
      console.error('页面实例加载失败:', error);
    }
  }
  
  // 创建页面实例
  async createPageInstance(pageName) {
    // 根据页面名称创建对应的页面实例
    // 这里保持原有的页面逻辑，只是改变渲染方式
    
    const pageConfig = {
      login: () => this.createLoginPage(),
      index: () => this.createIndexPage(),
      character: () => this.createCharacterPage(),
      skills: () => this.createSkillsPage(),
      shop: () => this.createShopPage(),
      guild: () => this.createGuildPage(),
      'character/backpack': () => this.createBackpackPage(),
      crafting: () => this.createCraftingPage()
    };
    
    const createFn = pageConfig[pageName];
    if (createFn) {
      return createFn();
    } else {
      throw new Error(`未知页面: ${pageName}`);
    }
  }
  
  // 创建登录页面
  createLoginPage() {
    return {
      name: 'login',
      data: {
        loginForm: {
          username: '',
          password: ''
        },
        registerForm: {
          username: '',
          password: '',
          confirmPassword: '',
          characterName: '',
          gender: 'male'
        },
        isRegistering: false,
        isLoading: false
      },
      
      render: (ctx, data) => {
        this.clearCanvas();
        
        // 绘制背景渐变
        const gradient = ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (data.isLoading) {
          this.drawLoadingScreen(ctx);
        } else if (data.isRegistering) {
          this.drawRegisterForm(ctx, data);
        } else {
          this.drawLoginForm(ctx, data);
        }
      },
      
      onTouchEnd: (x, y, data) => {
        // 处理登录页面的触摸事件
        return this.handleLoginTouch(x, y, data);
      },
      
      // 保持原有的方法
      login: async (username, password) => {
        // 原有的登录逻辑
        console.log('执行登录:', username);
        
        // 这里调用原有的WebSocket登录逻辑
        const wsManager = require('./utils/websocket.js');
        if (wsManager && typeof wsManager.send === 'function') {
          wsManager.send({
            type: 'login',
            data: { username, password }
          });
        }
      },
      
      register: async (formData) => {
        // 原有的注册逻辑
        console.log('执行注册:', formData);
        
        const wsManager = require('./utils/websocket.js');
        if (wsManager && typeof wsManager.send === 'function') {
          wsManager.send({
            type: 'register',
            data: formData
          });
        }
      }
    };
  }
  
  // 创建主页面
  createIndexPage() {
    return {
      name: 'index',
      data: {
        playerStats: {},
        recentEvents: [],
        isAdventuring: false
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '仗剑江湖行');

        const contentY = this.uiConfig.headerHeight + 20;
        const tabBarY = this.canvas.height - this.uiConfig.tabBarHeight;

        // 绘制玩家信息
        this.drawPlayerInfo(ctx, contentY, data.playerStats);

        // 绘制闯江湖按钮
        this.drawAdventureButton(ctx, contentY + 200, data.isAdventuring);

        // 绘制最近事件
        this.drawRecentEvents(ctx, contentY + 300, data.recentEvents);

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleIndexTouch(x, y, data);
      },

      // 保持原有的方法
      startAdventure: () => {
        console.log('开始冒险');
        const wsManager = require('./utils/websocket.js');
        if (wsManager && typeof wsManager.send === 'function') {
          wsManager.send({
            type: 'adventure',
            action: 'start'
          });
        }
      }
    };
  }

  // 创建角色页面
  createCharacterPage() {
    return {
      name: 'character',
      data: {
        playerStats: {},
        equipment: {},
        skills: []
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '角色信息');

        const contentY = this.uiConfig.headerHeight + 20;

        // 绘制角色属性
        this.drawCharacterStats(ctx, contentY, data.playerStats);

        // 绘制装备信息
        this.drawEquipment(ctx, contentY + 300, data.equipment);

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleCharacterTouch(x, y, data);
      }
    };
  }

  // 创建武功页面
  createSkillsPage() {
    return {
      name: 'skills',
      data: {
        skills: [],
        selectedCategory: '内功',
        categories: ['内功', '外功', '轻功', '医术']
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '武功修炼');

        const contentY = this.uiConfig.headerHeight + 20;

        // 绘制武功分类
        this.drawSkillCategories(ctx, contentY, data.categories, data.selectedCategory);

        // 绘制武功列表
        this.drawSkillsList(ctx, contentY + 100, data.skills);

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleSkillsTouch(x, y, data);
      }
    };
  }

  // 创建商店页面
  createShopPage() {
    return {
      name: 'shop',
      data: {
        items: [],
        playerMoney: 0,
        selectedCategory: '全部'
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '江湖市场');

        const contentY = this.uiConfig.headerHeight + 20;

        // 绘制玩家银两
        this.drawPlayerMoney(ctx, contentY, data.playerMoney);

        // 绘制商品列表
        this.drawShopItems(ctx, contentY + 80, data.items);

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleShopTouch(x, y, data);
      }
    };
  }

  // 创建门派页面
  createGuildPage() {
    return {
      name: 'guild',
      data: {
        guildInfo: {},
        guildMembers: [],
        playerGuildStatus: null
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '门派');

        const contentY = this.uiConfig.headerHeight + 20;

        if (data.playerGuildStatus) {
          // 绘制门派信息
          this.drawGuildInfo(ctx, contentY, data.guildInfo);

          // 绘制门派成员
          this.drawGuildMembers(ctx, contentY + 200, data.guildMembers);
        } else {
          // 绘制加入门派界面
          this.drawJoinGuildInterface(ctx, contentY);
        }

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleGuildTouch(x, y, data);
      }
    };
  }

  // 创建背包页面
  createBackpackPage() {
    return {
      name: 'character/backpack',
      data: {
        items: [],
        selectedItem: null
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '背包');

        const contentY = this.uiConfig.headerHeight + 20;

        // 绘制背包物品
        this.drawBackpackItems(ctx, contentY, data.items);

        // 如果有选中物品，绘制详情
        if (data.selectedItem) {
          this.drawItemDetails(ctx, contentY + 400, data.selectedItem);
        }

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleBackpackTouch(x, y, data);
      }
    };
  }

  // 创建打造页面
  createCraftingPage() {
    return {
      name: 'crafting',
      data: {
        recipes: [],
        materials: [],
        selectedRecipe: null
      },

      render: (ctx, data) => {
        this.clearCanvas();
        this.drawHeader(ctx, '装备打造');

        const contentY = this.uiConfig.headerHeight + 20;

        // 绘制配方列表
        this.drawRecipesList(ctx, contentY, data.recipes);

        // 绘制材料信息
        this.drawMaterials(ctx, contentY + 300, data.materials);

        // 绘制底部导航
        this.drawTabBar(ctx);
      },

      onTouchEnd: (x, y, data) => {
        return this.handleCraftingTouch(x, y, data);
      }
    };
  }
  
  // 页面导航
  navigateTo(pageName, params = {}) {
    console.log(`导航到页面: ${pageName}`);
    
    const pageInstance = this.pageInstances.get(pageName);
    if (pageInstance) {
      this.currentPage = pageName;
      
      // 执行页面的onLoad生命周期
      if (typeof pageInstance.onLoad === 'function') {
        pageInstance.onLoad(params);
      }
      
      // 渲染页面
      this.renderCurrentPage();
    } else {
      console.error(`页面不存在: ${pageName}`);
    }
  }
  
  // 渲染当前页面
  renderCurrentPage() {
    const pageInstance = this.pageInstances.get(this.currentPage);
    if (pageInstance && typeof pageInstance.render === 'function') {
      pageInstance.render(this.ctx, pageInstance.data);
    }
  }
  
  // 注册触摸事件
  registerTouchEvents() {
    wx.onTouchEnd((e) => {
      if (!e.touches || e.touches.length === 0) return;
      
      const touch = e.touches[0];
      const x = touch.clientX;
      const y = touch.clientY;
      
      this.handleTouch(x, y);
    });
  }
  
  // 处理触摸事件
  handleTouch(x, y) {
    const pageInstance = this.pageInstances.get(this.currentPage);
    if (pageInstance && typeof pageInstance.onTouchEnd === 'function') {
      const result = pageInstance.onTouchEnd(x, y, pageInstance.data);
      
      // 如果有导航操作
      if (result && result.navigate) {
        this.navigateTo(result.navigate, result.params);
      }
      // 如果需要重新渲染
      else if (result && result.rerender) {
        this.renderCurrentPage();
      }
    }
  }
  
  // 清空画布
  clearCanvas() {
    this.ctx.fillStyle = this.uiConfig.colors.background;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }
  
  // 绘制文本
  drawText(text, x, y, options = {}) {
    const {
      fontSize = this.uiConfig.fontSize.normal,
      color = this.uiConfig.colors.text,
      align = 'left',
      bold = false
    } = options;
    
    this.ctx.font = `${bold ? 'bold ' : ''}${fontSize}px Arial`;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    this.ctx.fillText(text, x, y);
  }
  
  // 绘制按钮
  drawButton(text, x, y, width, height, options = {}) {
    const {
      backgroundColor = this.uiConfig.colors.primary,
      textColor = this.uiConfig.colors.white,
      fontSize = this.uiConfig.fontSize.normal
    } = options;
    
    // 绘制按钮背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制按钮文本
    this.drawText(text, x + width / 2, y + height / 2 + fontSize / 3, {
      fontSize,
      color: textColor,
      align: 'center'
    });
    
    return { x, y, width, height };
  }
  
  // 更新页面数据
  updatePageData(pageName, newData) {
    const pageInstance = this.pageInstances.get(pageName);
    if (pageInstance) {
      Object.assign(pageInstance.data, newData);
      
      // 如果是当前页面，重新渲染
      if (this.currentPage === pageName) {
        this.renderCurrentPage();
      }
    }
  }
  
  // 获取页面数据
  getPageData(pageName) {
    const pageInstance = this.pageInstances.get(pageName);
    return pageInstance ? pageInstance.data : null;
  }

  // 绘制头部
  drawHeader(ctx, title) {
    // 绘制头部背景
    ctx.fillStyle = this.uiConfig.colors.primary;
    ctx.fillRect(0, 0, this.canvas.width, this.uiConfig.headerHeight);

    // 绘制标题
    this.drawText(title, this.canvas.width / 2, this.uiConfig.headerHeight / 2 + 10, {
      fontSize: this.uiConfig.fontSize.title,
      color: this.uiConfig.colors.white,
      align: 'center',
      bold: true
    });
  }

  // 绘制底部导航栏
  drawTabBar(ctx) {
    const tabBarY = this.canvas.height - this.uiConfig.tabBarHeight;
    const tabWidth = this.canvas.width / 5;

    // 绘制背景
    ctx.fillStyle = this.uiConfig.colors.white;
    ctx.fillRect(0, tabBarY, this.canvas.width, this.uiConfig.tabBarHeight);

    // 绘制分割线
    ctx.strokeStyle = this.uiConfig.colors.border;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, tabBarY);
    ctx.lineTo(this.canvas.width, tabBarY);
    ctx.stroke();

    // 导航项
    const tabs = [
      { name: '角色', page: 'character' },
      { name: '武功', page: 'skills' },
      { name: '江湖', page: 'index' },
      { name: '市场', page: 'shop' },
      { name: '门派', page: 'guild' }
    ];

    tabs.forEach((tab, index) => {
      const x = index * tabWidth;
      const isActive = this.currentPage === tab.page;

      // 绘制标签文本
      this.drawText(tab.name, x + tabWidth / 2, tabBarY + this.uiConfig.tabBarHeight / 2 + 8, {
        fontSize: this.uiConfig.fontSize.small,
        color: isActive ? this.uiConfig.colors.primary : this.uiConfig.colors.textSecondary,
        align: 'center'
      });
    });

    return { y: tabBarY, height: this.uiConfig.tabBarHeight, tabWidth };
  }

  // 绘制登录表单
  drawLoginForm(ctx, data) {
    const formY = 220;
    const formWidth = this.canvas.width - 80;
    const formX = (this.canvas.width - formWidth) / 2;

    // 绘制表单背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillRect(formX, formY, formWidth, 350);

    // 绘制表单标题
    this.drawText('登录江湖', this.canvas.width / 2, formY + 50, {
      fontSize: this.uiConfig.fontSize.title,
      color: this.uiConfig.colors.text,
      align: 'center',
      bold: true
    });

    // 绘制输入框提示
    this.drawText('账号', formX + 30, formY + 100, {
      fontSize: this.uiConfig.fontSize.normal,
      color: this.uiConfig.colors.text
    });

    this.drawText('密码', formX + 30, formY + 180, {
      fontSize: this.uiConfig.fontSize.normal,
      color: this.uiConfig.colors.text
    });

    // 绘制输入框
    this.drawInputBox(ctx, formX + 30, formY + 120, formWidth - 60, 40, data.loginForm.username || '请输入账号');
    this.drawInputBox(ctx, formX + 30, formY + 200, formWidth - 60, 40, data.loginForm.password ? '••••••••' : '请输入密码');

    // 绘制登录按钮
    this.loginButton = this.drawButton('登录', formX + 30, formY + 260, formWidth - 60, 50, {
      backgroundColor: this.uiConfig.colors.primary,
      fontSize: this.uiConfig.fontSize.large
    });

    // 绘制注册链接
    this.drawText('还没有账号？点击注册', this.canvas.width / 2, formY + 340, {
      fontSize: this.uiConfig.fontSize.small,
      color: this.uiConfig.colors.secondary,
      align: 'center'
    });

    this.registerLink = {
      x: this.canvas.width / 2 - 100,
      y: formY + 325,
      width: 200,
      height: 30
    };
  }

  // 绘制注册表单
  drawRegisterForm(ctx, data) {
    const formY = 180;
    const formWidth = this.canvas.width - 60;
    const formX = (this.canvas.width - formWidth) / 2;

    // 绘制表单背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillRect(formX, formY, formWidth, 450);

    // 绘制表单标题
    this.drawText('创建角色', this.canvas.width / 2, formY + 40, {
      fontSize: this.uiConfig.fontSize.title,
      color: this.uiConfig.colors.text,
      align: 'center',
      bold: true
    });

    // 绘制输入字段
    let currentY = formY + 80;
    const fieldHeight = 70;

    const fields = [
      { label: '账号', value: data.registerForm.username, placeholder: '请输入账号' },
      { label: '密码', value: data.registerForm.password, placeholder: '请输入密码', isPassword: true },
      { label: '确认密码', value: data.registerForm.confirmPassword, placeholder: '请再次输入密码', isPassword: true },
      { label: '角色名', value: data.registerForm.characterName, placeholder: '请输入角色名' }
    ];

    fields.forEach((field, index) => {
      this.drawText(field.label, formX + 20, currentY + 20, {
        fontSize: this.uiConfig.fontSize.normal,
        color: this.uiConfig.colors.text
      });

      const displayValue = field.isPassword && field.value ? '••••••••' : (field.value || field.placeholder);
      this.drawInputBox(ctx, formX + 20, currentY + 30, formWidth - 40, 35, displayValue);

      currentY += fieldHeight;
    });

    // 绘制性别选择
    this.drawText('性别', formX + 20, currentY + 20, {
      fontSize: this.uiConfig.fontSize.normal,
      color: this.uiConfig.colors.text
    });

    const genderButtonWidth = 80;
    this.maleButton = this.drawButton('男', formX + 20, currentY + 35, genderButtonWidth, 40, {
      backgroundColor: data.registerForm.gender === 'male' ? this.uiConfig.colors.primary : this.uiConfig.colors.background,
      textColor: data.registerForm.gender === 'male' ? this.uiConfig.colors.white : this.uiConfig.colors.text
    });

    this.femaleButton = this.drawButton('女', formX + 120, currentY + 35, genderButtonWidth, 40, {
      backgroundColor: data.registerForm.gender === 'female' ? this.uiConfig.colors.primary : this.uiConfig.colors.background,
      textColor: data.registerForm.gender === 'female' ? this.uiConfig.colors.white : this.uiConfig.colors.text
    });

    // 绘制注册按钮
    this.registerButton = this.drawButton('创建角色', formX + 20, currentY + 90, formWidth - 40, 50, {
      backgroundColor: this.uiConfig.colors.success,
      fontSize: this.uiConfig.fontSize.large
    });

    // 绘制返回登录链接
    this.drawText('已有账号？返回登录', this.canvas.width / 2, currentY + 170, {
      fontSize: this.uiConfig.fontSize.small,
      color: this.uiConfig.colors.secondary,
      align: 'center'
    });

    this.backToLoginLink = {
      x: this.canvas.width / 2 - 100,
      y: currentY + 155,
      width: 200,
      height: 30
    };
  }

  // 绘制输入框
  drawInputBox(ctx, x, y, width, height, text) {
    // 绘制背景
    ctx.fillStyle = this.uiConfig.colors.white;
    ctx.fillRect(x, y, width, height);

    // 绘制边框
    ctx.strokeStyle = this.uiConfig.colors.border;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    // 绘制文本
    this.drawText(text, x + 15, y + height / 2 + 6, {
      fontSize: this.uiConfig.fontSize.normal,
      color: text.includes('请') ? this.uiConfig.colors.textSecondary : this.uiConfig.colors.text
    });
  }

  // 绘制加载屏幕
  drawLoadingScreen(ctx) {
    // 绘制半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // 绘制加载文本
    this.drawText('正在登录...', this.canvas.width / 2, this.canvas.height / 2, {
      fontSize: this.uiConfig.fontSize.large,
      color: this.uiConfig.colors.white,
      align: 'center'
    });
  }

  // 检查点击是否在区域内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  }

  // 处理登录页面触摸
  handleLoginTouch(x, y, data) {
    if (data.isLoading) return null;

    if (data.isRegistering) {
      // 注册界面的触摸处理
      if (this.registerButton && this.isPointInRect(x, y, this.registerButton)) {
        // 执行注册
        const pageInstance = this.pageInstances.get('login');
        if (pageInstance && typeof pageInstance.register === 'function') {
          pageInstance.register(data.registerForm);
        }
        return { rerender: true };
      } else if (this.maleButton && this.isPointInRect(x, y, this.maleButton)) {
        data.registerForm.gender = 'male';
        return { rerender: true };
      } else if (this.femaleButton && this.isPointInRect(x, y, this.femaleButton)) {
        data.registerForm.gender = 'female';
        return { rerender: true };
      } else if (this.backToLoginLink && this.isPointInRect(x, y, this.backToLoginLink)) {
        data.isRegistering = false;
        return { rerender: true };
      }
    } else {
      // 登录界面的触摸处理
      if (this.loginButton && this.isPointInRect(x, y, this.loginButton)) {
        // 执行登录
        const pageInstance = this.pageInstances.get('login');
        if (pageInstance && typeof pageInstance.login === 'function') {
          pageInstance.login(data.loginForm.username, data.loginForm.password);
        }
        return { rerender: true };
      } else if (this.registerLink && this.isPointInRect(x, y, this.registerLink)) {
        data.isRegistering = true;
        return { rerender: true };
      }
    }

    return null;
  }

  // 处理主页面触摸
  handleIndexTouch(x, y, data) {
    // 检查底部导航栏
    const tabBarInfo = this.drawTabBar(this.ctx);
    if (y >= tabBarInfo.y) {
      const tabIndex = Math.floor(x / tabBarInfo.tabWidth);
      const pages = ['character', 'skills', 'index', 'shop', 'guild'];
      if (tabIndex >= 0 && tabIndex < pages.length) {
        return { navigate: pages[tabIndex] };
      }
    }

    return null;
  }

  // 处理其他页面的触摸事件（简化实现）
  handleCharacterTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  handleSkillsTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  handleShopTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  handleGuildTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  handleBackpackTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  handleCraftingTouch(x, y, data) {
    return this.handleTabBarTouch(x, y);
  }

  // 通用的底部导航栏触摸处理
  handleTabBarTouch(x, y) {
    const tabBarY = this.canvas.height - this.uiConfig.tabBarHeight;
    if (y >= tabBarY) {
      const tabWidth = this.canvas.width / 5;
      const tabIndex = Math.floor(x / tabWidth);
      const pages = ['character', 'skills', 'index', 'shop', 'guild'];
      if (tabIndex >= 0 && tabIndex < pages.length) {
        return { navigate: pages[tabIndex] };
      }
    }
    return null;
  }
}

// 导出适配器
export default GamePageAdapter;
