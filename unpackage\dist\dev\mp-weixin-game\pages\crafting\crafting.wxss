
.container.data-v-d6bc61f4 {
	padding: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}
.header.data-v-d6bc61f4 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.crafting-level.data-v-d6bc61f4,
.stamina-info.data-v-d6bc61f4 {
	display: flex;
	align-items: center;
}
.level-label.data-v-d6bc61f4,
.stamina-label.data-v-d6bc61f4 {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}
.level-value.data-v-d6bc61f4,
.stamina-value.data-v-d6bc61f4 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 材料统计 */
.materials-summary.data-v-d6bc61f4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.summary-title.data-v-d6bc61f4 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}
.no-materials.data-v-d6bc61f4 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
	border: 1rpx dashed #ccc;
}
.no-materials-text.data-v-d6bc61f4 {
	font-size: 26rpx;
	color: #999;
}
.materials-grid.data-v-d6bc61f4 {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr));
	gap: 10rpx;
}
.material-summary-item.data-v-d6bc61f4 {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 10rpx;
	text-align: center;
	border: 1rpx solid #e0e0e0;
}
.material-summary-name.data-v-d6bc61f4 {
	font-size: 24rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.material-summary-count.data-v-d6bc61f4 {
	font-size: 28rpx;
	font-weight: bold;
	color: #667eea;
}
.recipes-section.data-v-d6bc61f4 {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.section-header.data-v-d6bc61f4 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.section-title.data-v-d6bc61f4 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.refresh-btn.data-v-d6bc61f4 {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 10rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}
.refresh-btn.data-v-d6bc61f4:hover {
	background: #5a6fd8;
}
.refresh-btn[disabled].data-v-d6bc61f4 {
	background: #bdc3c7;
	cursor: not-allowed;
}

/* 加载状态 */
.loading-container.data-v-d6bc61f4 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 60rpx;
}
.loading-text.data-v-d6bc61f4 {
	font-size: 28rpx;
	color: #666;
}

/* 空状态 */
.empty-container.data-v-d6bc61f4 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80rpx 20rpx;
	text-align: center;
}
.empty-icon.data-v-d6bc61f4 {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}
.empty-text.data-v-d6bc61f4 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.empty-desc.data-v-d6bc61f4 {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
.recipes-grid.data-v-d6bc61f4 {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
	gap: 15rpx;
}
.recipe-item.data-v-d6bc61f4 {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	text-align: center;
	border: 2rpx solid #e0e0e0;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.recipe-item.data-v-d6bc61f4:hover {
	transform: translateY(-5rpx);
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}
.recipe-item.can-craft.data-v-d6bc61f4 {
	border-color: #27ae60;
	background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}
.recipe-item.cannot-craft.data-v-d6bc61f4 {
	border-color: #e74c3c;
	background: linear-gradient(135deg, #fdecea 0%, #ffcdd2 100%);
}
.recipe-icon.data-v-d6bc61f4 {
	font-size: 48rpx;
	margin-bottom: 15rpx;
	color: #667eea;
	display: block;
}
.recipe-name.data-v-d6bc61f4 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	line-height: 1.2;
}
.recipe-quality.data-v-d6bc61f4 {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}
.recipe-type.data-v-d6bc61f4 {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 15rpx;
	display: block;
	background: rgba(102, 126, 234, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.craft-status.data-v-d6bc61f4 {
	margin-top: 10rpx;
}
.can-craft-text.data-v-d6bc61f4 {
	color: #27ae60;
	font-size: 26rpx;
	font-weight: bold;
	background: rgba(39, 174, 96, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	display: inline-block;
}
.cannot-craft-text.data-v-d6bc61f4 {
	color: #e74c3c;
	font-size: 26rpx;
	font-weight: bold;
	background: rgba(231, 76, 60, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	display: inline-block;
}
.detail-modal.data-v-d6bc61f4 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	animation: fadeIn-d6bc61f4 0.3s ease;
}
.detail-content.data-v-d6bc61f4 {
	background: white;
	border-radius: 20rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	animation: slideUp-d6bc61f4 0.3s ease;
}
.detail-header.data-v-d6bc61f4 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.detail-title.data-v-d6bc61f4 {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
}
.detail-quality.data-v-d6bc61f4 {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}
.detail-info.data-v-d6bc61f4 {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}
.detail-desc.data-v-d6bc61f4 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	display: block;
}
.detail-stamina.data-v-d6bc61f4 {
	font-size: 28rpx;
	color: #e74c3c;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
	background: rgba(231, 76, 60, 0.1);
	padding: 10rpx 15rpx;
	border-radius: 10rpx;
}
.materials-section.data-v-d6bc61f4 {
	margin-top: 20rpx;
}
.materials-title.data-v-d6bc61f4 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}
.materials-list.data-v-d6bc61f4 {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
.material-item.data-v-d6bc61f4 {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 15rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 26rpx;
	color: #333;
	border: 1rpx solid #e0e0e0;
	transition: all 0.3s ease;
}
.material-item.sufficient.data-v-d6bc61f4 {
	color: #27ae60;
	font-weight: bold;
	background: #e8f5e9;
	border-color: #27ae60;
}
.material-item.insufficient.data-v-d6bc61f4 {
	color: #e74c3c;
	font-weight: bold;
	background: #fdecea;
	border-color: #e74c3c;
}
.material-name.data-v-d6bc61f4 {
	font-size: 28rpx;
	font-weight: 500;
}
.material-quantity.data-v-d6bc61f4 {
	font-size: 28rpx;
	font-weight: bold;
}

/* 缺少材料提示 */
.missing-materials.data-v-d6bc61f4 {
	margin-top: 20rpx;
	padding: 15rpx;
	background: #fdecea;
	border-radius: 10rpx;
	border: 1rpx solid #e74c3c;
}
.missing-title.data-v-d6bc61f4 {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
	margin-bottom: 10rpx;
	display: block;
}
.missing-list.data-v-d6bc61f4 {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}
.missing-item.data-v-d6bc61f4 {
	font-size: 24rpx;
	color: #e74c3c;
	background: rgba(231, 76, 60, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}
.modal-footer.data-v-d6bc61f4 {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #f8f9fa;
}
.modal-btn.data-v-d6bc61f4 {
	flex: 1;
	padding: 20rpx;
	border-radius: 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}
.cancel-btn.data-v-d6bc61f4 {
	background: #95a5a6;
	color: white;
}
.cancel-btn.data-v-d6bc61f4:hover {
	background: #7f8c8d;
}
.confirm-btn.data-v-d6bc61f4 {
	background: #27ae60;
	color: white;
}
.confirm-btn.data-v-d6bc61f4:hover {
	background: #229954;
}
.confirm-btn[disabled].data-v-d6bc61f4 {
	background: #bdc3c7;
	color: #7f8c8d;
	cursor: not-allowed;
}
@keyframes fadeIn-d6bc61f4 {
from {
		opacity: 0;
}
to {
		opacity: 1;
}
}
@keyframes slideUp-d6bc61f4 {
from {
		transform: translateY(50rpx);
		opacity: 0;
}
to {
		transform: translateY(0);
		opacity: 1;
}
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
.recipes-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(2, 1fr);
		gap: 10rpx;
}
.materials-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(2, 1fr);
}
.recipe-item.data-v-d6bc61f4 {
		padding: 15rpx;
}
.recipe-icon.data-v-d6bc61f4 {
		font-size: 36rpx;
}
.recipe-name.data-v-d6bc61f4 {
		font-size: 24rpx;
}
}
@media screen and (min-width: 376px) and (max-width: 750px) {
.recipes-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(3, 1fr);
}
.materials-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(3, 1fr);
}
}
@media screen and (min-width: 751px) {
.recipes-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(4, 1fr);
}
.materials-grid.data-v-d6bc61f4 {
		grid-template-columns: repeat(4, 1fr);
}
}
