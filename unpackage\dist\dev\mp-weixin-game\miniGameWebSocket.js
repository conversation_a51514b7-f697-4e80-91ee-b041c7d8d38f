/**
 * 小游戏 WebSocket 通信管理器
 * 适配小游戏环境的 WebSocket 通信
 */

class MiniGameWebSocketManager {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isAuthed = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = -1;
    this.reconnectInterval = 3000;
    this.maxReconnectInterval = 30000;
    this.messageQueue = [];
    this.eventHandlers = {};
    this.connecting = false;
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
    this.manualDisconnect = false;
    this.serverUrl = 'ws://localhost:8080';
    this.disableAutoAuth = false;
    this.debug = false;
    this.connectPromise = null;
    
    // 小游戏特有的适配器引用
    this.gameAdapter = null;
    
    this.initEventHandlers();
  }
  
  // 设置游戏适配器引用
  setGameAdapter(adapter) {
    this.gameAdapter = adapter;
  }
  
  // 初始化事件处理器
  initEventHandlers() {
    this.on('login_success', (data) => {
      this.isAuthed = true;
      this.log('WebSocket: 登录成功，认证状态已更新');
      
      // 通知游戏适配器登录成功
      if (this.gameAdapter && typeof this.gameAdapter.onLoginSuccess === 'function') {
        this.gameAdapter.onLoginSuccess(data);
      }
    });
    
    this.on('auth_success', (data) => {
      this.isAuthed = true;
      this.log('WebSocket: 认证成功，认证状态已更新');
      
      // 通知游戏适配器认证成功
      if (this.gameAdapter && typeof this.gameAdapter.onAuthSuccess === 'function') {
        this.gameAdapter.onAuthSuccess(data);
      }
    });
    
    this.on('login_failed', (data) => {
      this.isAuthed = false;
      this.log('WebSocket: 登录失败，认证状态已重置');
      
      // 通知游戏适配器登录失败
      if (this.gameAdapter && typeof this.gameAdapter.onLoginFailed === 'function') {
        this.gameAdapter.onLoginFailed(data);
      }
    });
    
    this.on('auth_failed', (data) => {
      this.isAuthed = false;
      this.log('WebSocket: 认证失败，认证状态已重置');
      
      // 通知游戏适配器认证失败
      if (this.gameAdapter && typeof this.gameAdapter.onAuthFailed === 'function') {
        this.gameAdapter.onAuthFailed(data);
      }
    });
  }
  
  // 日志方法
  log(message, ...args) {
    if (this.debug) {
      console.log(`[MiniGame WebSocket] ${message}`, ...args);
    }
  }
  
  // 错误日志方法
  error(message, ...args) {
    console.error(`[MiniGame WebSocket错误] ${message}`, ...args);
  }
  
  // 连接到WebSocket服务器
  connect() {
    if (this.isConnected) {
      this.log('WebSocket已连接，无需重新连接');
      return Promise.resolve();
    }
    
    if (this.connecting) {
      this.log('WebSocket正在连接中，请等待...');
      return this.connectPromise || Promise.reject(new Error('正在连接中'));
    }
    
    this.connecting = true;
    this.connectPromise = new Promise((resolve, reject) => {
      try {
        this.log('开始连接WebSocket服务器:', this.serverUrl);
        
        // 使用小游戏的 WebSocket API
        if (typeof wx !== 'undefined' && wx.connectSocket) {
          this.ws = wx.connectSocket({
            url: this.serverUrl,
            success: () => {
              this.log('WebSocket连接请求已发送');
            },
            fail: (error) => {
              this.error('WebSocket连接请求失败:', error);
              this.connecting = false;
              this.connectPromise = null;
              reject(error);
            }
          });
          
          // 监听连接打开
          wx.onSocketOpen((res) => {
            this.isConnected = true;
            this.connecting = false;
            this.connectPromise = null;
            this.reconnectAttempts = 0;
            this.log('WebSocket连接已建立');
            
            this.processMessageQueue();
            this.startHeartbeat();
            
            if (this.eventHandlers['connected']) {
              this.eventHandlers['connected'].forEach(fn => fn());
            }
            
            // 通知游戏适配器连接成功
            if (this.gameAdapter && typeof this.gameAdapter.onWebSocketConnected === 'function') {
              this.gameAdapter.onWebSocketConnected();
            }
            
            // 自动认证
            if (!this.disableAutoAuth) {
              this.log('准备执行自动认证...');
              setTimeout(() => {
                this.autoAuthenticate();
              }, 500);
            }
            
            resolve(res);
          });
          
          // 监听连接关闭
          wx.onSocketClose((res) => {
            this.isConnected = false;
            this.connecting = false;
            this.connectPromise = null;
            this.stopHeartbeat();
            
            if (this.eventHandlers['disconnected']) {
              this.eventHandlers['disconnected'].forEach(fn => fn());
            }
            
            this.log('WebSocket连接断开，code:', res.code, 'reason:', res.reason);
            
            // 通知游戏适配器连接断开
            if (this.gameAdapter && typeof this.gameAdapter.onWebSocketDisconnected === 'function') {
              this.gameAdapter.onWebSocketDisconnected(res);
            }
            
            if (!this.manualDisconnect && res.code !== 1000) {
              this.log('检测到异常断开，开始重连...');
              this.handleDisconnect();
            } else if (this.manualDisconnect) {
              this.log('手动断开连接，不进行重连');
              this.manualDisconnect = false;
            }
          });
          
          // 监听连接错误
          wx.onSocketError((res) => {
            this.isConnected = false;
            this.connecting = false;
            this.connectPromise = null;
            this.error('WebSocket连接错误:', res);
            
            // 通知游戏适配器连接错误
            if (this.gameAdapter && typeof this.gameAdapter.onWebSocketError === 'function') {
              this.gameAdapter.onWebSocketError(res);
            }
            
            this.handleDisconnect();
            reject(res);
          });
          
          // 监听消息接收
          wx.onSocketMessage((res) => {
            this.handleMessage(res.data);
          });
          
        } else {
          // 非小游戏环境的降级处理
          reject(new Error('小游戏环境不可用'));
        }
        
      } catch (error) {
        this.error('WebSocket连接异常:', error);
        this.connecting = false;
        this.connectPromise = null;
        reject(error);
      }
    });
    
    return this.connectPromise;
  }
  
  // 处理接收到的消息
  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      this.log('收到消息:', message);
      
      // 触发对应的事件处理器
      if (message.type && this.eventHandlers[message.type]) {
        this.eventHandlers[message.type].forEach(fn => fn(message.data || message));
      }
      
      // 通知游戏适配器处理消息
      if (this.gameAdapter && typeof this.gameAdapter.onWebSocketMessage === 'function') {
        this.gameAdapter.onWebSocketMessage(message);
      }
      
    } catch (error) {
      this.error('解析消息失败:', error, data);
    }
  }
  
  // 发送消息
  send(data) {
    if (!this.isConnected) {
      this.log('WebSocket未连接，消息加入队列');
      this.messageQueue.push(data);
      return;
    }
    
    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      
      if (typeof wx !== 'undefined' && wx.sendSocketMessage) {
        wx.sendSocketMessage({
          data: message,
          success: () => {
            this.log('消息发送成功:', data);
          },
          fail: (error) => {
            this.error('消息发送失败:', error);
          }
        });
      }
      
    } catch (error) {
      this.error('发送消息异常:', error);
    }
  }
  
  // 处理消息队列
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }
  
  // 注册事件处理器
  on(event, handler) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }
  
  // 移除事件处理器
  off(event, handler) {
    if (this.eventHandlers[event]) {
      const index = this.eventHandlers[event].indexOf(handler);
      if (index > -1) {
        this.eventHandlers[event].splice(index, 1);
      }
    }
  }
  
  // 自动认证
  autoAuthenticate() {
    // 这里可以实现自动认证逻辑
    this.log('执行自动认证...');
    
    // 发送认证请求
    this.send({
      type: 'auth',
      data: {
        // 认证数据
      }
    });
  }
  
  // 开始心跳
  startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' });
      }
    }, 30000); // 30秒心跳
  }
  
  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }
  
  // 处理断开连接
  handleDisconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts || this.maxReconnectAttempts === -1) {
      this.reconnectAttempts++;
      const delay = Math.min(this.reconnectInterval * this.reconnectAttempts, this.maxReconnectInterval);
      
      this.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          this.error('重连失败:', error);
        });
      }, delay);
    } else {
      this.error('达到最大重连次数，停止重连');
    }
  }
  
  // 断开连接
  disconnect() {
    this.manualDisconnect = true;
    this.stopHeartbeat();
    
    if (this.ws && typeof wx !== 'undefined' && wx.closeSocket) {
      wx.closeSocket();
    }
    
    this.isConnected = false;
    this.isAuthed = false;
  }
  
  // 重连
  reconnect() {
    this.disconnect();
    setTimeout(() => {
      this.connect();
    }, 1000);
  }
}

// 导出管理器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MiniGameWebSocketManager;
} else if (typeof window !== 'undefined') {
  window.MiniGameWebSocketManager = MiniGameWebSocketManager;
}
