/**
 * UI管理器 - 负责所有界面绘制
 */

class UIManager {
  constructor(ctx, canvas) {
    this.ctx = ctx;
    this.canvas = canvas;
    
    // UI配置
    this.config = {
      colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        background: '#ecf0f1',
        white: '#ffffff',
        text: '#2c3e50',
        textSecondary: '#7f8c8d',
        border: '#bdc3c7',
        gradient: {
          start: '#667eea',
          end: '#764ba2'
        }
      },
      fonts: {
        title: 'bold 32px Arial',
        large: 'bold 24px Arial',
        normal: '18px Arial',
        small: '14px Arial',
        tiny: '12px Arial'
      },
      spacing: {
        small: 10,
        medium: 20,
        large: 30
      }
    };
  }
  
  // 清空画布
  clear() {
    this.ctx.fillStyle = this.config.colors.background;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }
  
  // 绘制渐变背景
  drawGradientBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
    gradient.addColorStop(0, this.config.colors.gradient.start);
    gradient.addColorStop(1, this.config.colors.gradient.end);
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }
  
  // 绘制文本
  drawText(text, x, y, options = {}) {
    const {
      font = this.config.fonts.normal,
      color = this.config.colors.text,
      align = 'left',
      baseline = 'top',
      maxWidth = null
    } = options;
    
    this.ctx.font = font;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    this.ctx.textBaseline = baseline;
    
    if (maxWidth) {
      this.ctx.fillText(text, x, y, maxWidth);
    } else {
      this.ctx.fillText(text, x, y);
    }
  }
  
  // 绘制按钮
  drawButton(text, x, y, width, height, options = {}) {
    const {
      backgroundColor = this.config.colors.primary,
      textColor = this.config.colors.white,
      font = this.config.fonts.normal,
      borderRadius = 5,
      border = false,
      borderColor = this.config.colors.border,
      disabled = false
    } = options;
    
    // 如果禁用，调整颜色
    const bgColor = disabled ? this.config.colors.textSecondary : backgroundColor;
    const txtColor = disabled ? this.config.colors.white : textColor;
    
    // 绘制按钮背景
    this.ctx.fillStyle = bgColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    if (border) {
      this.ctx.strokeStyle = borderColor;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, y, width, height);
    }
    
    // 绘制按钮文本
    this.drawText(text, x + width / 2, y + height / 2, {
      font,
      color: txtColor,
      align: 'center',
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  }
  
  // 绘制输入框
  drawInputBox(x, y, width, height, value, placeholder, options = {}) {
    const {
      backgroundColor = this.config.colors.white,
      borderColor = this.config.colors.border,
      textColor = this.config.colors.text,
      placeholderColor = this.config.colors.textSecondary,
      font = this.config.fonts.normal,
      focused = false
    } = options;
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    this.ctx.strokeStyle = focused ? this.config.colors.primary : borderColor;
    this.ctx.lineWidth = focused ? 2 : 1;
    this.ctx.strokeRect(x, y, width, height);
    
    // 绘制文本或占位符
    const displayText = value || placeholder;
    const color = value ? textColor : placeholderColor;
    
    this.drawText(displayText, x + 10, y + height / 2, {
      font,
      color,
      baseline: 'middle'
    });
    
    return { x, y, width, height };
  }
  
  // 绘制卡片
  drawCard(x, y, width, height, options = {}) {
    const {
      backgroundColor = this.config.colors.white,
      borderColor = this.config.colors.border,
      shadow = true
    } = options;
    
    // 绘制阴影
    if (shadow) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      this.ctx.fillRect(x + 2, y + 2, width, height);
    }
    
    // 绘制卡片背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    this.ctx.strokeStyle = borderColor;
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);
    
    return { x, y, width, height };
  }
  
  // 绘制头部导航
  drawHeader(title, options = {}) {
    const {
      height = 60,
      backgroundColor = this.config.colors.primary,
      textColor = this.config.colors.white,
      showBack = false
    } = options;
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, this.canvas.width, height);
    
    // 绘制标题
    this.drawText(title, this.canvas.width / 2, height / 2, {
      font: this.config.fonts.large,
      color: textColor,
      align: 'center',
      baseline: 'middle'
    });
    
    // 绘制返回按钮
    let backButton = null;
    if (showBack) {
      backButton = this.drawButton('←', 10, 10, 40, 40, {
        backgroundColor: 'transparent',
        textColor: textColor,
        font: this.config.fonts.large
      });
    }
    
    return { backButton };
  }
  
  // 绘制底部导航栏
  drawTabBar(tabs, currentTab) {
    const tabBarHeight = 80;
    const tabBarY = this.canvas.height - tabBarHeight;
    const tabWidth = this.canvas.width / tabs.length;
    
    // 绘制背景
    this.ctx.fillStyle = this.config.colors.white;
    this.ctx.fillRect(0, tabBarY, this.canvas.width, tabBarHeight);
    
    // 绘制顶部分割线
    this.ctx.strokeStyle = this.config.colors.border;
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(0, tabBarY);
    this.ctx.lineTo(this.canvas.width, tabBarY);
    this.ctx.stroke();
    
    const tabButtons = [];
    
    tabs.forEach((tab, index) => {
      const x = index * tabWidth;
      const isActive = currentTab === tab.key;
      
      // 绘制标签文本
      this.drawText(tab.name, x + tabWidth / 2, tabBarY + 40, {
        font: this.config.fonts.normal,
        color: isActive ? this.config.colors.primary : this.config.colors.textSecondary,
        align: 'center',
        baseline: 'middle'
      });
      
      // 如果是活跃标签，绘制指示器
      if (isActive) {
        this.ctx.fillStyle = this.config.colors.primary;
        this.ctx.fillRect(x + tabWidth / 2 - 20, tabBarY + 55, 40, 3);
      }
      
      tabButtons.push({
        x: x,
        y: tabBarY,
        width: tabWidth,
        height: tabBarHeight,
        key: tab.key
      });
    });
    
    return tabButtons;
  }
  
  // 绘制列表项
  drawListItem(x, y, width, height, title, subtitle, options = {}) {
    const {
      backgroundColor = this.config.colors.white,
      borderColor = this.config.colors.border,
      showArrow = false,
      icon = null
    } = options;
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    this.ctx.strokeStyle = borderColor;
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);
    
    let textX = x + 15;
    
    // 绘制图标
    if (icon) {
      this.drawText(icon, textX, y + height / 2, {
        font: this.config.fonts.large,
        baseline: 'middle'
      });
      textX += 40;
    }
    
    // 绘制标题
    this.drawText(title, textX, y + height / 2 - 10, {
      font: this.config.fonts.normal,
      color: this.config.colors.text,
      baseline: 'middle'
    });
    
    // 绘制副标题
    if (subtitle) {
      this.drawText(subtitle, textX, y + height / 2 + 10, {
        font: this.config.fonts.small,
        color: this.config.colors.textSecondary,
        baseline: 'middle'
      });
    }
    
    // 绘制箭头
    if (showArrow) {
      this.drawText('>', x + width - 30, y + height / 2, {
        font: this.config.fonts.normal,
        color: this.config.colors.textSecondary,
        baseline: 'middle'
      });
    }
    
    return { x, y, width, height };
  }
  
  // 绘制进度条
  drawProgressBar(x, y, width, height, progress, options = {}) {
    const {
      backgroundColor = this.config.colors.border,
      progressColor = this.config.colors.success,
      showText = true
    } = options;
    
    // 绘制背景
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制进度
    const progressWidth = width * Math.min(Math.max(progress, 0), 1);
    this.ctx.fillStyle = progressColor;
    this.ctx.fillRect(x, y, progressWidth, height);
    
    // 绘制文本
    if (showText) {
      const percentage = Math.round(progress * 100) + '%';
      this.drawText(percentage, x + width / 2, y + height / 2, {
        font: this.config.fonts.small,
        color: this.config.colors.text,
        align: 'center',
        baseline: 'middle'
      });
    }
  }
  
  // 检查点是否在矩形内
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UIManager;
} else {
  window.UIManager = UIManager;
}
